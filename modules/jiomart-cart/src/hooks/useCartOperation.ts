import JMCartNetworkController from '../../../jiomart-networkmanager/src/JMNetworkController/JMCartNetworkController';
import {useMutation} from '@tanstack/react-query';
import {useDispatch} from 'react-redux';
import {
  setCart,
  optimisticUpdateItem,
  rollbackOptimisticUpdate,
} from '../slices/cartSlice';
import {JMSharedViewModel} from '../../../jiomart-common/src/JMSharedViewModel';
import {AppSourceType} from '../../../jiomart-common/src/SourceType';
import {useGlobalState} from '../../../jiomart-general/src/context/JMGlobalStateProvider';
import {
  GenericToast,
  genericToastTypeData,
  mergeGenericToastTypeData,
} from '../../../jiomart-common/src/JMScreenSlot.types';
import {handleDeeplinkIntent} from '../../../jiomart-general/src/deeplink/JMDeeplinkUtility';
import {getBaseURL} from '../../../jiomart-networkmanager/src/JMEnvironmentConfig';
import {useRef} from 'react';

const cartController = new JMCartNetworkController();

// Queue to handle sequential API calls
interface QueuedOperation {
  type: 'add' | 'remove';
  request: any;
  optimisticPayload: any;
  rollbackData: {uid: number; previousQuantity: number; wasInCart: boolean};
}

const useCartOperation = () => {
  const dispatch = useDispatch();
  const {setToastTypeData} = useGlobalState();
  const operationQueueRef = useRef<QueuedOperation[]>([]);
  const isProcessingRef = useRef(false);

  const getCart = useMutation({
    mutationFn: cartController.fetchCart,
    onSuccess: (response: any) => {
      dispatch(setCart(response));
    },
    retry: 0,
  });

  const processQueue = async () => {
    if (isProcessingRef.current || operationQueueRef.current.length === 0) {
      return;
    }

    isProcessingRef.current = true;

    while (operationQueueRef.current.length > 0) {
      const operation = operationQueueRef.current.shift();
      if (!operation) continue;

      try {
        let response;
        if (operation.type === 'add') {
          response = await cartController.addToCart(operation.request);
        } else {
          response = await cartController.removeFromCart(operation.request);
        }

        if (response?.success || response?.status === 'success') {
          // Success - the optimistic update was correct, refresh cart to sync
          getCart.mutate(true);

          if (operation.type === 'add') {
            // Show success toast for add to cart
            let modifiedToastData: any;
            const deeplinkData = {
              mUri: `${getBaseURL()}/checkout/cart`,
            };
            const cta = await handleDeeplinkIntent(deeplinkData);
            if (cta) {
              modifiedToastData = {
                showButton: true,
                buttonText: 'VIEW',
                message: 'Add To Cart',
                cta,
              };
            } else {
              modifiedToastData = {
                message: 'Add To Cart',
              };
            }
            setToastTypeData(
              mergeGenericToastTypeData(
                GenericToast.SUCCESS,
                modifiedToastData,
              ),
            );
          }
        } else {
          // API returned failure - rollback optimistic update
          dispatch(rollbackOptimisticUpdate(operation.rollbackData));
          throw new Error('API returned failure');
        }
      } catch (error: any) {
        // API call failed - rollback optimistic update
        dispatch(rollbackOptimisticUpdate(operation.rollbackData));

        const modifiedToastData = {
          message:
            operation.type === 'add'
              ? error?.response?.reason?.reason_eng ??
                'Something went wrong while adding to cart'
              : error?.response?.reason?.reason_eng ??
                'Something went wrong while removing the item',
        };
        setToastTypeData(
          mergeGenericToastTypeData(
            GenericToast.ERROR,
            modifiedToastData as any,
          ),
        );
      }
    }

    isProcessingRef.current = false;
  };

  const addToCart = useMutation({
    mutationFn: cartController.addToCart,
    onSuccess: async (response: any) => {
      if (response?.success || response?.status === 'success') {
        getCart.mutate(true);

        let modifiedToastData: any;
        const deeplinkData = {
          mUri: `${getBaseURL()}/checkout/cart`,
        };
        const cta = await handleDeeplinkIntent(deeplinkData);
        if (cta) {
          modifiedToastData = {
            showButton: true,
            buttonText: 'VIEW',
            message: 'Add To Cart',
            cta,
          };
        } else {
          modifiedToastData = {
            message: 'Add To Cart',
          };
        }
        setToastTypeData(
          mergeGenericToastTypeData(GenericToast.SUCCESS, modifiedToastData),
        );
      }
    },
    onError: (error: any) => {
      const modifiedToastData = {
        message:
          error?.response?.reason?.reason_eng ??
          'Something went wrong while adding to cart',
      };
      setToastTypeData(
        mergeGenericToastTypeData(GenericToast.ERROR, modifiedToastData as any),
      );
    },
    retry: 0,
  });

  const removeFromCart = useMutation({
    mutationFn: cartController.removeFromCart,
    onSuccess: (response: any) => {
      if (response?.success || response?.status === 'success') {
        getCart.mutate(true);
      }
    },
    onError: (error: any) => {
      setToastTypeData(
        genericToastTypeData(
          GenericToast.ERROR,
          error?.response?.reason?.reason_eng ??
            'Something went wrong while removing the item',
        ),
      );
    },
    retry: 0,
  });

  // Optimistic add to cart with queuing
  const optimisticAddToCart = (request: any, currentQuantity: number = 0) => {
    const newQuantity = currentQuantity + 1;
    const wasInCart = currentQuantity > 0;

    // Perform optimistic update immediately
    dispatch(
      optimisticUpdateItem({
        uid: request.uid,
        quantity: newQuantity,
        product_name: request.product_name,
        minQty: request.minQty,
        maxQty: request.maxQty,
      }),
    );

    // Generate the proper API request payload
    const requestWithQuantity = {...request, quantity: 1};
    const apiRequest = generateAddToCartRequest(requestWithQuantity);

    // Add to queue for sequential processing
    const operation: QueuedOperation = {
      type: 'add',
      request: apiRequest,
      optimisticPayload: {
        uid: request.uid,
        quantity: newQuantity,
        product_name: request.product_name,
        minQty: request.minQty,
        maxQty: request.maxQty,
      },
      rollbackData: {
        uid: request.uid,
        previousQuantity: currentQuantity,
        wasInCart,
      },
    };

    operationQueueRef.current.push(operation);
    processQueue();
  };

  // Optimistic remove from cart with queuing
  const optimisticRemoveFromCart = (request: any, currentQuantity: number) => {
    const newQuantity = Math.max(0, currentQuantity - 1);
    const wasInCart = currentQuantity > 0;

    // Perform optimistic update immediately
    dispatch(
      optimisticUpdateItem({
        uid: request.uid,
        quantity: newQuantity,
        product_name: request.product_name,
        minQty: request.minQty,
        maxQty: request.maxQty,
      }),
    );

    // Generate the proper API request payload
    let removeQuantity = 1;
    if (request?.minQty && currentQuantity === request?.minQty) {
      removeQuantity = request?.minQty;
    }
    const requestWithQuantity = {...request, quantity: removeQuantity};
    const apiRequest = generateRemoveFromCartRequest(requestWithQuantity);

    // Add to queue for sequential processing
    const operation: QueuedOperation = {
      type: 'remove',
      request: apiRequest,
      optimisticPayload: {
        uid: request.uid,
        quantity: newQuantity,
        product_name: request.product_name,
        minQty: request.minQty,
        maxQty: request.maxQty,
      },
      rollbackData: {
        uid: request.uid,
        previousQuantity: currentQuantity,
        wasInCart,
      },
    };

    operationQueueRef.current.push(operation);
    processQueue();
  };

  const generateAddToCartRequest = (request: any) => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        return {
          slug: request?.slug,
          item_id: request?.uid,
          item_size: request?.size,
          quantity: request?.quantity,
          product_group_tag: request?.product_group_tag,
          meta: request?.meta,
          identifiers: request?.identifiers,
          parent_item_identifiers: request?.parent_item_identifiers,
        };

      case AppSourceType.JM_BAU:
        return {
          product_code: request?.uid,
          qty: request?.quantity,
          seller_id: request?.sellerId,
          store_code: request?.store_id,
        };
      default:
        throw new Error('app source not found');
    }
  };

  const generateRemoveFromCartRequest = (request: any) => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        return {
          item_id: request?.uid,
          item_size: request?.size,
          quantity: request?.quantity,
          meta: request?.meta,
          identifiers: request?.identifiers,
          parent_item_identifiers: request?.parent_item_identifiers,
          slug: request?.slug,
          product_group_tag: request?.product_group_tag,
        };
      case AppSourceType.JM_BAU:
        return {
          product_code: request?.uid,
          qty: request?.quantity,
          seller_id: request?.sellerId,
          store_code: request?.store_id,
        };
      default:
        throw new Error('app source not found');
    }
  };

  return {
    getCart,
    addToCart,
    removeFromCart,
    optimisticAddToCart,
    optimisticRemoveFromCart,
    generateAddToCartRequest,
    generateRemoveFromCartRequest,
  };
};

export default useCartOperation;
