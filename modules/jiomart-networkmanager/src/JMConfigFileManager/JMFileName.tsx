export enum JMConfigFileName {
  JMVersionFileName = 'JMRNFileVersionV1',
  JMCommonContentFileName = 'JMRNCommanConfigsV1',
  JMPLPConfigFileName = 'JMRNPLPConfigurationV1',
  JMGridPLPConfigurationFileName = 'JMRNGridPLPConfigurationV1',
  JMSearchPLPConfigurationFileName = 'JMRNSearchPLPConfigurationV1',
  JMSearchConfigurationFileName = 'JMRNSearchConfigurationV1',
  JMProfileConfigurationFileName = 'JMRNProfileConfigurationV1',
  JMAddressConfigurationFileNAme = 'JMRNAddressConfigurationV1',
  JMPDPConfigurationFileName = 'JMRNPDPConfigurationV1',
  JMHeaderConfigurationFileName = 'JMHeaderConfigurationV1',
  JMAllCategoriesConfigurationFileName = 'JMAllCategoriesConfigurationV1',
  JMDeeplinkConfigurationFileName = 'JMDeeplinkConfigurationV1',
  JMBottomNavBarConfigurationFileName = 'JMBottomNavBarConfigurationV1',
  JMOrdersConfigurationFileName = 'JMOrdersConfigurationV1',
  JMAppFAQConfigurationFileName = 'JMAppFAQConfigurationV1',
  JMFeedbackConfigurationFileName = 'JMFeedbackConfigurationV1',
  JMWebViewConfigurationFileName = 'JMWebViewConfigurationV1',
  JMHomeDashboardConfigurationFileName = 'JMHomeDashboardConfigurationV1',
  JMSplashScreenFileName = 'JMRNSplashConfigsV1',
  JMInAppBannerFileName = 'JMRNInAppBannerV1',
  JMAppFaqConfigurationFileName = 'JMAppFAQConfigurationV1',
}
