import React, {useRef, useCallback} from 'react';
import {
  NativeModules,
  View,
  FlatList,
  BackHandler,
  StyleSheet,
} from 'react-native';
import {
  AppScreens,
  type ScreenProps,
} from '../../jiomart-common/src/JMAppScreenEntry';
import {JMTemplateViewType} from '../../jiomart-common/src/JMTemplateViewType';
import BottomSheet from '../../jiomart-general/src/ui/BottomSheet/BottomSheet';
import MultiSearchBottomSheet from '../../jiomart-general/src/ui/BottomSheet/MultiSearchBottomSheet';
import ScreenSlot, {
  DeeplinkHandler,
} from '../../jiomart-general/src/ui/JMScreenSlot';
import {renderTemplate} from '../../jiomart-general/src/ui/JMTemplateController';
import SearchResultItem from './components/SearchResultItem';
import useJMSearchScreenController from './controller/useJMSearchScreenController';
import {SearchScreenContentProps} from './types/JMSearchScrennType';
import {logAnalyticsEvent} from '../../jiomart-common/src/JMAnalyticsUtility';
import {
  AnalyticsParams,
  EventTriggerChannel,
} from '../../jiomart-common/src/AnalyticsParams';

export type JMSearchScreenProps = ScreenProps<typeof AppScreens.SEARCH_SCREEN>;
const {KeyboardHandling} = NativeModules;

enum JMSearchScreenSubType {
  RECENT_SEARCH_CLEAR_ALL = 'recent_search_clear_all',
}

const SearchScreenContent = React.memo(
  ({
    sortedComponents,
    config,
    textInputRef,
    handleDiscoverMoreSearchResult,
    handleTrendingCategoryResult,
    handleRecentSearchResult,
    openMultiSearchBtmSheet,
    isSearchActive,
    searchResultData,
    searchResultFilteredData,
    categoriesData,
    brandsData,
    searchResulExtData,
    onBrandItemClick,
    onProductClicked,
    onCategoryItemClick,
    onSearchResultItemClick,
    sortedSspComponents,
    searchText,
  }: SearchScreenContentProps) => {
    const searchSuggestionList = searchResultFilteredData?.slice(
      0,
      config?.searchSuggestion_List?.maxItemsVisible ?? 4,
    );

    const renderItem = ({item, index}: any) => {
      return (
        <SearchResultItem
          key={`s_i_${index}`}
          onPress={() => {
            console.warn(
              'onClick getActionCta',
              'onClick getActionCta onSearchResultItemClick',
            );
            onSearchResultItemClick(
              item,
              index,
              searchSuggestionList?.length,
              config?.searchSuggestion_List?.cta,
            );
          }}
          searchResultItem={item}
        />
      );
    };

    const getActionCta = (JMTemplateData: any) => {
      // attach GA event to all click handlers
      let onClick = null;

      if (JMTemplateData?.viewType === JMTemplateViewType.SHOPPING_LIST_BLOCK) {
        onClick = () => {
          console.warn(
            'onClick getActionCta',
            'onClick getActionCta SHOPPING_LIST_BLOCK',
          );
          setTimeout(() => textInputRef?.current?.focus(), 50);
          KeyboardHandling?.nativeKeyboardEnabled(false);
          //logAnalyticsEvent(JMTemplateData?.analyticEvent);
          openMultiSearchBtmSheet();
        };
      } else if (
        JMTemplateData?.viewType === JMTemplateViewType.RECENT_SEARCHES
      ) {
        onClick = (text: string) => {
          console.warn(
            'onClick getActionCta',
            'onClick getActionCta RECENT_SEARCHES',
          );
          if (text === JMSearchScreenSubType.RECENT_SEARCH_CLEAR_ALL) {
            logAnalyticsEvent(
              JMTemplateData?.actionContentItems?.[0]?.analyticEvent,
            );
          } else {
            logAnalyticsEvent(JMTemplateData?.analyticEvent?.itemClick);
            logAnalyticsEvent({
              ...JMTemplateData?.analyticEvent?.itemClickFirebase,
              payload: {
                ...JMTemplateData?.analyticEvent?.itemClickFirebase?.payload,
                [AnalyticsParams.SEARCH_TERM]: text,
              },
            });
            const appsflyerEventDict = {
              af_search_string: text,
            };

            logAnalyticsEvent({
              eventName: 'af_search',
              payload: {
                ...appsflyerEventDict,
              },
              channel: EventTriggerChannel.APPSFLYER,
            });
          }
          handleRecentSearchResult(text, JMTemplateData?.cta);
        };
      } else if (
        JMTemplateData?.viewType === JMTemplateViewType.DISCOVER_MORE
      ) {
        onClick = (text: string) => {
          console.warn(
            'onClick getActionCta',
            'onClick getActionCta DISCOVER_MORE',
          );
          handleDiscoverMoreSearchResult(text, JMTemplateData?.cta);

          logAnalyticsEvent({
            ...JMTemplateData?.analyticEvent?.itemClick,
            payload: {
              ...JMTemplateData?.analyticEvent?.itemClick?.payload,
              [AnalyticsParams.SEARCH_TERM]: text,
              [AnalyticsParams.TITLE]: text,
            },
          });

          console.log(
            '🚀 ~ getActionCta ~ text:',
            JSON.stringify(JMTemplateData),
          );
          logAnalyticsEvent({
            ...JMTemplateData?.analyticEvent?.itemClickFirebase,
            payload: {
              ...JMTemplateData?.analyticEvent?.itemClickFirebase?.payload,
              suggestive_try_something_new_clicked: text,
            },
          });

          const appsflyerEventDict = {
            af_search_string: text,
          };

          logAnalyticsEvent({
            eventName: 'af_search',
            payload: {
              ...appsflyerEventDict,
            },
            channel: EventTriggerChannel.APPSFLYER,
          });
        };
      } else if (
        JMTemplateData?.viewType === JMTemplateViewType.POPULAR_CATEGORY
      ) {
        onClick = (slug: string, page: string, item: any) => {
          console.warn(
            'onClick getActionCta',
            'onClick getActionCta POPULAR_CATEGORY--' +
              slug +
              '||' +
              JSON.stringify(page) +
              '||' +
              JSON.stringify(item?.name),
          );
          handleTrendingCategoryResult(slug, page, JMTemplateData?.cta);
          logAnalyticsEvent({
            ...JMTemplateData?.analyticEvent?.itemClickFirebase,
            payload: {
              ...JMTemplateData?.analyticEvent?.itemClickFirebase?.payload,
              [AnalyticsParams.LABEL]: item?.name,
            },
          });
          const appsflyerEventDict = {
            af_search_string: item?.name,
          };

          logAnalyticsEvent({
            eventName: 'af_search',
            payload: {
              ...appsflyerEventDict,
            },
            channel: EventTriggerChannel.APPSFLYER,
          });
        };
      } else if (JMTemplateData?.type === 'categories') {
        onClick = (l3CategoryName: string) => {
          console.warn(
            'onClick getActionCta',
            'onClick getActionCta categories',
          );
          onCategoryItemClick(l3CategoryName, JMTemplateData?.cta);
          logAnalyticsEvent({
            ...JMTemplateData?.analyticEvent?.itemClick,
            payload: {
              ...JMTemplateData?.analyticEvent?.itemClick?.payload,
              [AnalyticsParams.LABEL]: l3CategoryName,
            },
          });

          logAnalyticsEvent({
            ...JMTemplateData?.analyticEvent?.itemClickFirebase,
            payload: {
              ...JMTemplateData?.analyticEvent?.itemClickFirebase?.payload,
              [AnalyticsParams.LABEL]: l3CategoryName,
              [AnalyticsParams.SEARCH_TERM]: searchText,
            },
          });

          const appsflyerEventDict = {
            af_search_string: l3CategoryName,
          };

          logAnalyticsEvent({
            eventName: 'af_search',
            payload: {
              ...appsflyerEventDict,
            },
            channel: EventTriggerChannel.APPSFLYER,
          });
        };
      } else if (JMTemplateData?.type === 'brands') {
        onClick = (brand: string) => {
          console.warn('onClick getActionCta', 'onClick getActionCta brands');
          onBrandItemClick(brand, JMTemplateData?.cta);
          logAnalyticsEvent({
            ...JMTemplateData?.analyticEvent?.itemClickFirebase,
            payload: {
              ...JMTemplateData?.analyticEvent?.itemClickFirebase?.payload,
              [AnalyticsParams.SEARCH_TERM]: searchText,
              suggestive_brand_clicked: brand,
            },
          });

          const appsflyerEventDict = {
            af_search_string: brand,
          };

          logAnalyticsEvent({
            eventName: 'af_search',
            payload: {
              ...appsflyerEventDict,
            },
            channel: EventTriggerChannel.APPSFLYER,
          });
        };
      } else if (JMTemplateData?.viewType === JMTemplateViewType.PRODUCT_GRID) {
        onClick = (slug: string, url: string, item: any) => {
          console.warn(
            'onClick getActionCta',
            'onClick getActionCta PRODUCT_GRID',
          );
          onProductClicked(slug, url, JMTemplateData?.cta);
          logAnalyticsEvent({
            ...JMTemplateData?.analyticEvent?.itemClick,
            payload: {
              ...JMTemplateData?.analyticEvent?.itemClick?.payload,
              [AnalyticsParams.LABEL]: item?.name,
            },
          });
          logAnalyticsEvent({
            ...JMTemplateData?.analyticEvent?.itemClickFirebase,
            payload: {
              ...JMTemplateData?.analyticEvent?.itemClickFirebase?.payload,
              [AnalyticsParams.LABEL]: item?.name,
            },
          });
          const appsflyerEventDict = {
            af_search_string: item?.name,
          };

          logAnalyticsEvent({
            eventName: 'af_search',
            payload: {
              ...appsflyerEventDict,
            },
            channel: EventTriggerChannel.APPSFLYER,
          });
        };
      } else if (
        JMTemplateData?.viewType === JMTemplateViewType.RECOMMENDED_PRODUCT
      ) {
        onClick = (slug: string, url: string, item: any) => {
          console.warn(
            'onClick getActionCta',
            'onClick getActionCta RECOMMENDED_PRODUCT',
          );
          onProductClicked(slug, url, JMTemplateData?.cta);
          logAnalyticsEvent({
            ...JMTemplateData?.analyticEvent?.itemClick,
            payload: {
              ...JMTemplateData?.analyticEvent?.itemClick?.payload,
              [AnalyticsParams.LABEL]: item?.name,
            },
          });
          logAnalyticsEvent({
            ...JMTemplateData?.analyticEvent?.itemClickFirebase,
            payload: {
              ...JMTemplateData?.analyticEvent?.itemClickFirebase?.payload,
              [AnalyticsParams.LABEL]: item?.name,
            },
          });
          const appsflyerEventDict = {
            af_search_string: item?.name,
          };

          logAnalyticsEvent({
            eventName: 'af_search',
            payload: {
              ...appsflyerEventDict,
            },
            channel: EventTriggerChannel.APPSFLYER,
          });
        };
      }

      return onClick;
    };
    return (
      <View style={styles.container}>
        <FlatList
          data={isSearchActive ? sortedSspComponents : sortedComponents}
          keyboardDismissMode="on-drag"
          ListHeaderComponent={
            isSearchActive &&
            searchResultData?.items &&
            searchResultData?.items?.length > 0 ? (
              <>
                {config?.searchSuggestion_List?.is_visible &&
                  searchSuggestionList?.map((item: any, index: number) =>
                    renderItem({item, index}),
                  )}
              </>
            ) : null
          }
          renderItem={({item, index}) =>
            renderTemplate(
              {
                ...item,
                viewType: item.viewType,
                algoliaSearchConfiguration: config?.algoliaSearchConfiguration,
                jiomart_header: config?.jiomart_header,
                data:
                  item.type === 'categories'
                    ? categoriesData.current
                    : item.type === 'brands'
                    ? brandsData.current
                    : searchResulExtData?.items,
                onClick: getActionCta(item),
                showShadowTextChip: true,
              },
              index,
            )
          }
        />
      </View>
    );
  },
);

const JMSearchScreen = (props: JMSearchScreenProps) => {
  const {route, navigation} = props;
  const navigationBean = route.params;
  const textInputRef = useRef(null);
  const {
    config,
    sortedComponents,
    handleRecentSearchResult,
    handleDiscoverMoreSearchResult,
    handleTrendingCategoryResult,
    openMultiSearchBtmSheet,
    closeMultiSearchBtmSheet,
    handleAutoComplete,
    onCategoryItemClick,
    onBrandItemClick,
    onProductClicked,
    isSearchActive,
    searchResultData,
    searchResultFilteredData,
    categoriesData,
    brandsData,
    searchResulExtData,
    multiSearchBottomSheet,
    onSearchResultItemClick,
    onChangeMultiSearch,
    sortedSspComponents,
    searchText,
  } = useJMSearchScreenController(props);

  const searchScreenMainUI = useCallback(() => {
    return (
      <SearchScreenContent
        sortedComponents={sortedComponents}
        config={config}
        textInputRef={textInputRef}
        remoteConfig={config}
        handleRecentSearchResult={handleRecentSearchResult}
        handleDiscoverMoreSearchResult={handleDiscoverMoreSearchResult}
        handleTrendingCategoryResult={handleTrendingCategoryResult}
        openMultiSearchBtmSheet={openMultiSearchBtmSheet}
        closeMultiSearchBtmSheet={closeMultiSearchBtmSheet}
        isSearchActive={isSearchActive}
        searchResultData={searchResultData ?? []}
        searchResultFilteredData={searchResultFilteredData}
        categoriesData={categoriesData}
        brandsData={brandsData}
        searchResulExtData={searchResulExtData}
        onCategoryItemClick={onCategoryItemClick}
        onProductClicked={onProductClicked}
        onBrandItemClick={onBrandItemClick}
        onSearchResultItemClick={onSearchResultItemClick}
        sortedSspComponents={sortedSspComponents}
        searchText={searchText}
      />
    );
  }, [
    sortedComponents,
    config,
    handleRecentSearchResult,
    handleDiscoverMoreSearchResult,
    handleTrendingCategoryResult,
    openMultiSearchBtmSheet,
    closeMultiSearchBtmSheet,
    isSearchActive,
    searchResultData,
    searchResultFilteredData,
    categoriesData,
    brandsData,
    searchResulExtData,
    onCategoryItemClick,
    onProductClicked,
    onBrandItemClick,
    onSearchResultItemClick,
    sortedSspComponents,
  ]);

  const bottomSheetContent = () => {
    return (
      <>
        {config?.btm_sheet?.shopping_list_block?.is_visible ? (
          <BottomSheet
            isStretchEnabled
            maxHeightPercent={50}
            enableKeyboarAvoidingView
            onBackDropClick={closeMultiSearchBtmSheet}
            onDrag={closeMultiSearchBtmSheet}
            visible={multiSearchBottomSheet}>
            <MultiSearchBottomSheet
              textInputRef={textInputRef}
              closeMultiSearchBtmSheet={closeMultiSearchBtmSheet}
              onChangeMultiSearch={(text: string) =>
                onChangeMultiSearch(
                  text,
                  config?.btm_sheet?.shopping_list_block?.cta,
                )
              }
              config={config?.btm_sheet?.shopping_list_block}
            />
          </BottomSheet>
        ) : null}
      </>
    );
  };
  return (
    <DeeplinkHandler
      navigationBean={navigationBean}
      navigation={navigation}
      children={bean => (
        <ScreenSlot
          navigationBean={bean}
          navigation={navigation}
          bottomSheetContent={bottomSheetContent}
          searchTextHandler={handleAutoComplete}
          onCustomBackPress={() => {
            if (multiSearchBottomSheet) {
              closeMultiSearchBtmSheet();
            } else if (navigation.canGoBack()) {
              navigation.goBack();
            } else {
              BackHandler.exitApp();
            }
          }}
          onSubmitHandler={text => {
            handleDiscoverMoreSearchResult(
              text,
              config?.searchSuggestion_List?.cta,
            );
          }}
          children={_ => {
            return searchScreenMainUI();
          }}
        />
      )}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    flex: 1,
  },
});

export default JMSearchScreen;
