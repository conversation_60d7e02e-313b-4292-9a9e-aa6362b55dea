import { useState } from 'react';
import { Alert, Platform } from 'react-native';
import { PermissionStatus, RESULTS } from 'react-native-permissions';
import { getCurrentPosition } from '../../../jiomart-common/src/GeolocationUtility';
import {
  AndroidPermission,
  IOSPermission,
} from '../../../jiomart-common/src/JMConstants';
import {
  checkAndRequestSinglePermission,
  checkSinglePermission,
  openAppSetting,
  type JMPermissionType,
} from '../../../jiomart-common/src/JMPermission';
import { getReverseGeoCodeFromLatLongNB } from '../../../jiomart-general/src/bridge/JMRNBridge';
import { JMDatabaseManager } from '../../../jiomart-networkmanager/src/db/JMDatabaseManager';
import { JMSharedViewModel } from '../../../jiomart-common/src/JMSharedViewModel';

interface UseCurrentLocationProps {
  alertBlocked: {
    title: string;
    message?: string;
    button?: {
      text?: string;
    };
  };
}

type Handlers = {
  onGranted: () => Promise<any>;
  onLimited?: () => Promise<any>;
  onBlocked?: () => any;
  onDenied?: () => any;
  onUnavailable?: () => any;
};

const useCurrentLocation = (props: UseCurrentLocationProps) => {
  const { alertBlocked } = props;
  const [isFetchingLocation, setIsFetchingLocation] = useState(false);
  const LocationPermission = Platform.select({
    ios: IOSPermission.LOCATION_WHEN_IN_USE,
    android: AndroidPermission.ACCESS_FINE_LOCATION,
  });

  const checkLocationPermission = async ({
    onGranted,
    onLimited,
    onBlocked,
    onDenied,
    onUnavailable,
  }: Handlers) => {
    try {
      const status = await checkLocationPermissionStatus()
      JMSharedViewModel.Instance.setPermissionGivenStatus(status === RESULTS.GRANTED || status === RESULTS.LIMITED)
      switch (status) {
        case RESULTS.GRANTED:
          return await onGranted();

        case RESULTS.LIMITED:
          return onLimited ? await onLimited() : RESULTS.LIMITED;

        case RESULTS.BLOCKED:
          return onBlocked ? onBlocked() : RESULTS.BLOCKED;

        case RESULTS.DENIED:
          return onDenied ? onDenied() : RESULTS.DENIED;

        default:
          return status;
      }
    } catch (error) {
      return onUnavailable ? onUnavailable() : Promise.reject(RESULTS.UNAVAILABLE);
    }
  };

  const checkLocationPermissionStatus = async (): Promise<PermissionStatus> => {
    if (Platform.OS === 'ios') {
      const whenInUse = await checkSinglePermission(LocationPermission as JMPermissionType) as PermissionStatus;
      const always = await checkSinglePermission(IOSPermission.LOCATION_ALWAYS) as PermissionStatus;

      if (whenInUse === RESULTS.GRANTED || always === RESULTS.GRANTED) {
        JMSharedViewModel.Instance.setPermissionGivenStatus(true)
        return RESULTS.GRANTED;
      }

      return whenInUse != RESULTS.GRANTED ? whenInUse : always;
    } else {
      return await checkSinglePermission(LocationPermission as JMPermissionType);
    }
  };

  const fetchCurrentLocation = async (redirectToSettingDirectly: boolean = false) => {
    try {
      setIsFetchingLocation(true);
      const status = await checkAndRequestSinglePermission(
        LocationPermission as JMPermissionType,
      );
      JMSharedViewModel.Instance.setPermissionGivenStatus(status === RESULTS.GRANTED || status === RESULTS.LIMITED)
      switch (status) {
        case RESULTS.GRANTED:
        case RESULTS.LIMITED:
          return await getCurrentPosition();
        case RESULTS.BLOCKED:{
          if(!redirectToSettingDirectly){
            Alert.alert(alertBlocked.title, alertBlocked.message, [
              {
                text: alertBlocked.button?.text,
                onPress: () => {
                  openAppSetting();
                },
              },
            ]);
          }
          else{
            JMSharedViewModel.Instance.fromLocationSettings = true;
            openAppSetting();
          }
          return Promise.resolve(status);
        }
        default:
          return Promise.resolve(status);
      }
    } catch (error) {
      return Promise.reject(RESULTS.UNAVAILABLE);
    } finally {
      setIsFetchingLocation(false);
    }
  };

  const fetchLocationFromReverseGeoCodeFromLatLong = async (
    fromCache: boolean = false, redirectToSettingDirectly: boolean = false
  ) => {
    const geoCords: any = await fetchCurrentLocation(redirectToSettingDirectly);
    const lat = geoCords?.coords?.latitude ?? 0;
    const long = geoCords?.coords?.longitude ?? 0;
    let address: any = undefined;
    if (fromCache) {
      // Check cache first
      address = await JMDatabaseManager.address.getCachedReverseGeocode(
        lat,
        long,
      );
      if (!address) {
        const addressStr = await getReverseGeoCodeFromLatLongNB(lat, long);
        address = JSON.parse(addressStr ?? '');
        if (address) {
          await JMDatabaseManager.address.setCachedReverseGeocode(
            lat,
            long,
            address,
          );
        }
      }
    } else {
      const addressStr = await getReverseGeoCodeFromLatLongNB(lat, long);
      address = JSON.parse(addressStr ?? '');
      if (address) {
        await JMDatabaseManager.address.setCachedReverseGeocode(
          lat,
          long,
          address,
        );
      }
    }
    return {
      address,
      coords: geoCords?.coords,
    };
  };

  return {
    isFetchingLocation,
    fetchCurrentLocation,
    fetchLocationFromReverseGeoCodeFromLatLong,
    checkLocationPermission,
  };
};

export default useCurrentLocation;
