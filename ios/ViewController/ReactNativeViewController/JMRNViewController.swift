//
//  JMRNViewController.swift
//  JioMart
//
//  Created by <PERSON><PERSON><PERSON> on 01/07/25.
//

import UIKit
import React
import JioRNBundle
import JioDesignSystem
import JdsUI

class JMRNViewController: UIViewController {
  
  static var currentBridge: RCTBridge?
    
  override func viewDidLoad() {
    super.viewDidLoad()
    Typography.staticFontSize = true
    JDSTypography.staticFontSize = true
    JMRNBundleDownLoad.shared.initializeJioRNBundleSDK()
    loadRNSetupCode()
  }
  
  func setupRNScreen() {
    let initialProps = JMRN.shared.initialProps
    guard let bridge = RCTBridge(delegate: self, launchOptions: [:]) else { return }
    JMRNViewController.currentBridge = bridge
    let rootView = RCTRootView(bridge: bridge, moduleName: JMConstant.moduleName, initialProperties: initialProps)
    rootView.backgroundColor = UIColor(red: 0/255, green: 120/255, blue: 173/255, alpha: 1.0)
    self.view = rootView
  }
  
  func loadRNSetupCode() {
    JioRNBundleDownLoad.shared.loadReactNativeViewBasedOnDownloadType { [weak self] in
      self?.removeTopChildViewController()
      // load react native view
      self?.setupRNScreen()
    } loadForceUpdateView: { [weak self] controller in
      // load force update view
      guard let forceUpdateController = controller else { return }
      guard let self = self else { return }
      forceUpdateController.view.frame = CGRect(x: 0, y: 0, width: Int(self.view.frame.size.width), height: Int(self.view.frame.size.height))
      self.addChild(forceUpdateController)
      forceUpdateController.didMove(toParent: self)
      self.view.addSubview(forceUpdateController.view)
    }
  }
  
  func removeTopChildViewController() {
    if self.children.isNotEmpty {
      let viewControllers: [UIViewController] = self.children
      viewControllers.last?.willMove(toParent: nil)
      viewControllers.last?.removeFromParent()
      viewControllers.last?.view.removeFromSuperview()
    }
  }
}

// MARK: - RCTBridgeDelegate
extension JMRNViewController: RCTBridgeDelegate {
  func sourceURL(for bridge: RCTBridge) -> URL? {
    return JioRNBundleDownLoad.shared.getBundleUrl()
  }
}

