import {useState} from 'react';
import {AddCtaViewProps} from '../types/AddCtaModel';
import {
  useCart,
  useCartItems,
} from '../../../../../jiomart-cart/src/hooks/useCart';
import useCartOperation from '../../../../../jiomart-cart/src/hooks/useCartOperation';
import {Platform} from 'react-native';
import {isNullOrUndefinedOrEmpty} from '../../../../../jiomart-common/src/JMObjectUtility';
import handleHapticFeedback from '../../../../../jiomart-common/src/utils/JMHapticFeedback';
import {logAnalyticsEvent} from '../../../../../jiomart-common/src/JMAnalyticsUtility';
import {
  getCustomerId,
  getCustomerType,
} from '../../../../../jiomart-common/src/utils/UserUtils';
import {EventTriggerChannel} from '../../../../../jiomart-common/src/AnalyticsParams';

const useAddCta = (props: AddCtaViewProps) => {
  let {request} = props;
  const productCart = useCartItems({uid: request?.uid});
  const [loader, setLoader] = useState(false);
  const {
    optimisticAddToCart,
    optimisticRemoveFromCart,
    generateAddToCartRequest,
    generateRemoveFromCartRequest,
  } = useCartOperation();

  const handleCartHapticClick = () =>
    Platform.OS === 'android'
      ? handleHapticFeedback('impactMedium')
      : handleHapticFeedback('impactLight');

  const handleAddToCart = () => {
    try {
      if (
        productCart &&
        request?.maxQty &&
        request?.maxQty !== 0 &&
        request?.maxQty <= productCart?.quantity
      ) {
        return;
      }

      console.log('productCart---am', JSON.stringify(productCart), request);

      // Show loading briefly for user feedback
      setLoader(true);
      setTimeout(() => setLoader(false), 300);

      handleCartHapticClick();

      let quantity = 1;
      if (productCart) {
        quantity = 1;
      } else if (request?.minQty) {
        quantity = request?.minQty ?? 1;
      }

      logAnalyticsEvent({
        eventName: 'add_to_cart',
        payload: {
          action: 'add',
          label: productCart?.product_name,
          category: 'slp products',
        },
        channel: `${EventTriggerChannel.FIREBASE}`,
      });

      // Use optimistic update - UI updates immediately
      optimisticAddToCart(
        {
          ...request,
          quantity,
          parent_item_identifiers: productCart?.parent_item_identifiers,
          identifiers: productCart?.identifiers,
          article_id: productCart?.article_id,
          product_name: productCart?.product_name,
        },
        productCart?.quantity || 0,
      );
    } catch (error) {
      console.error('Error in handleAddToCart:', error);
      setLoader(false);
    }
  };

  const handleRemoveFromCart = () => {
    try {
      if (productCart && request && productCart?.quantity < request?.minQty) {
        return;
      }

      // Show loading briefly for user feedback
      setLoader(true);
      setTimeout(() => setLoader(false), 300);

      handleCartHapticClick();

      let quantity = 1;
      if (request?.minQty && productCart?.quantity === request?.minQty) {
        quantity = request?.minQty;
      }

      logAnalyticsEvent({
        eventName: 'remove_from_cart',
        payload: {
          action: 'remove',
          label: productCart?.product_name,
          category: 'slp products',
        },
        channel: `${EventTriggerChannel.FIREBASE}`,
      });

      // Use optimistic update - UI updates immediately
      optimisticRemoveFromCart(
        {
          ...request,
          quantity,
          parent_item_identifiers: productCart?.parent_item_identifiers,
          identifiers: productCart?.identifiers,
          article_id: productCart?.article_id,
          product_name: productCart?.product_name,
        },
        productCart?.quantity || 0,
      );
    } catch (error) {
      console.error('Error in handleRemoveFromCart:', error);
      setLoader(false);
    }
  };

  return {
    ...props,
    productCart,
    loader,
    handleAddToCart,
    handleRemoveFromCart,
  };
};

export default useAddCta;
