import {useEffect, useState, useRef, useCallback} from 'react';
import JMProductNetworkController from '../../../jiomart-networkmanager/src/JMNetworkController/JMProductNetworkController';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {
  getPrefString,
  addStringPref,
} from '../../../jiomart-common/src/JMAsyncStorageHelper';
import {AsyncStorageKeys} from '../../../jiomart-common/src/JMConstants';
import {
  NavigationBean,
  navBeanObj,
} from '../../../jiomart-common/src/JMNavGraphUtil';
import {isNullOrUndefinedOrEmpty} from '../../../jiomart-common/src/JMObjectUtility';
import {JMSharedViewModel} from '../../../jiomart-common/src/JMSharedViewModel';
import {AppSourceType} from '../../../jiomart-common/src/SourceType';
import {
  getVersionSpecificFilterListData,
  formatCategoryName,
  generateFilterReq,
} from '../../../jiomart-common/src/utils/JMCommonFunctions';
import {navigateTo} from '../../../jiomart-general/src/navigation/JMNavGraph';
import {getConfigFileDataAsync} from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileManager';
import {JMConfigFileName} from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import {getBaseURL} from '../../../jiomart-networkmanager/src/JMEnvironmentConfig';
import {fetchAlgoliaSearch} from '../../../jiomart-networkmanager/src/JMNetworkController/AlgoliaService';
import SearchService from '../../../jiomart-networkmanager/src/JMNetworkController/JMSearchAPINetworkController';
import {JProps} from '../JMSearchScreen';
import {SearchComponent} from '../types/JMSearchScrennType';
import JMFirebaseABTestUtility from '../../../jiomart-general/src/utils/JMFirebaseABTestUtility';
import {logAnalyticsEvent} from '../../../jiomart-common/src/JMAnalyticsUtility';
import {
  AnalyticsParams,
  EventTriggerChannel,
} from '../../../jiomart-common/src/AnalyticsParams';

const productNetworkController = new JMProductNetworkController();

const useJMSearchScreenController = (props: JProps) => {
  const {route} = props;
  const {params} = route.params;
  const searchValue = params?.searchValue ?? '';
  const [config, setConfigData] = useState<any>(null);
  const [sortedComponents, setSortedComponents] = useState<SearchComponent[]>(
    [],
  );
  const [sortedSspComponents, setSortedSspComponents] = useState<
    SearchComponent[]
  >([]);
  const configRef = useRef<any>(null);
  const [searchTextLocalState, setSearchTextLocalState] = useState('');
  const [multiSearchBottomSheet, setMultiSearchBottomSheet] = useState(false);
  const navigation = useNavigation<NativeStackNavigationProp<any>>();
  const searchApi = 'none';
  const [isSearchActive, setSearchActive] = useState<boolean>(false);
  const searchTimeout = useRef<NodeJS.Timeout | null>(null);
  const categoriesData = useRef<any>(null);
  const brandsData = useRef<any>(null);
  const textInputRef = useRef(null);
  const [isAlcoholProduct, setIsAlcoholProduct] = useState(false);
  const [isLocalShopProduct, setIsLocalShopProduct] = useState(false);
  const [searchResultData, setSearchResultData] = useState<any>();
  const [searchResulExtData, setSearchResulExtData] = useState<any>();
  const [getStoreData, setStoreData] = useState(null);
  const [algoliaConfig, setAlgoliaConfig] = useState(null);
  const searchResultSet = new Set();
  searchResultData?.items?.forEach(item => {
    searchResultSet.add(item?.display?.toLowerCase());
  });
  const searchResultFilteredData = [...searchResultSet];

  const getSearchEndPointDetails = async () => {
    const searchEndPointDetails = await getPrefString(
      AsyncStorageKeys.SEARCH_END_POINT_DETAILS,
    );
    if (searchEndPointDetails) {
      setAlgoliaConfig(JSON.parse(searchEndPointDetails));
    }
  };

  useEffect(() => {
    const configFileRead = async () => {
      const content = await getConfigFileDataAsync(
        JMConfigFileName.JMSearchConfigurationFileName,
      );
      if (content) {
        let searchConfig = getVersionSpecificFilterListData(
          content.searchConfig,
        )?.[0];
        console.log(
          '🚀 ~ configFileRead ~ searchConfig:',
          JSON.stringify(searchConfig),
        );
        setConfigData(searchConfig);
        configRef.current = searchConfig;
      }
    };
    configFileRead();
    getSearchEndPointDetails();
  }, []);

  useEffect(() => {
    if (
      !isNullOrUndefinedOrEmpty(searchValue) &&
      !isNullOrUndefinedOrEmpty(config)
    ) {
      setSearchActive(true);
      handleAutoComplete(searchValue ?? '');
    }
  }, [searchValue, config]);

  useEffect(() => {
    if (config) {
      const filteredAndSorted: any = getVersionSpecificFilterListData(
        config?.slpContent
          ?.filter(component => component?.showElement)
          ?.sort((a, b) => (a?.orderNo || 0) - (b?.orderNo || 0)),
      );

      const filteredAndSortedSspContent: any = getVersionSpecificFilterListData(
        config?.sspContent
          ?.filter(component => component?.showElement)
          ?.sort((a, b) => (a?.orderNo || 0) - (b?.orderNo || 0)),
      );

      console.log('filteredAndSorted---am>', filteredAndSorted);

      if (
        JSON.stringify(filteredAndSorted) !== JSON.stringify(sortedComponents)
      ) {
        setSortedComponents(filteredAndSorted || []);
      }
      if (
        JSON.stringify(filteredAndSortedSspContent) !==
        JSON.stringify(sortedSspComponents)
      ) {
        setSortedSspComponents(filteredAndSortedSspContent || []);
      }
    }
  }, [config, sortedComponents]);

  const handleAutoComplete = async (searchQuery: any) => {
    searchQuery = searchQuery.replace(/^[^a-zA-Z0-9]+/, '');
    setSearchTextLocalState(searchQuery);
    if (searchQuery?.length > 0) {
      setSearchActive(true);
      if (searchTimeout.current) {
        clearTimeout(searchTimeout.current);
      }
      const timeOutID = setTimeout(() => {
        if (JMSharedViewModel.Instance.appSource === AppSourceType.JM_BAU) {
          handleNewSearchPageData(searchQuery, config);
        } else {
          fetchSearchResults(searchQuery);
          fetchSearchExtResults(searchQuery);
        }
      }, 200);
      searchTimeout.current = timeOutID;
    } else {
      setSearchActive(false);
      searchResultData?.items.splice(0, searchResultData?.items?.length);
    }
  };

  const fetchSearchResults = async (searchQuery: string) => {
    const queryParams = {
      q: searchQuery,
    };

    try {
      const searchResultData = await SearchService.getSearchResults(
        queryParams,
      );
      setSearchResultData(searchResultData);
    } catch (error) {
      console.error('Error fetching search results:', error);
    }
  };

  const fetchSearchExtResults = async (searchQuery: string) => {
    const queryParams = {
      // filters: true,
      page_id: '%2A',
      page_size: 5,
      q: searchQuery,
    };
    await productNetworkController
      .fetchSearchProductList({params: queryParams})
      .then(searchResultExtData => {
        handleCategoryAndBrandData(searchResultExtData);
        setSearchResulExtData(searchResultExtData);
        // console.warn('fetchSearchExtResults Data: ' + searchResultExtData);
      })
      .catch(error => {
        return error;
      });
  };

  const handleCategoryAndBrandData = (data: any) => {
    const result = {
      brand: [],
      categories: [],
    };

    data?.filters?.forEach(filter => {
      const keyName = filter.key?.name.toLowerCase();

      // Extract brand
      if (keyName === 'brand') {
        result.brand = filter.values;
      }

      // Extract category-related fields
      if (keyName === 'l3_category_names') {
        result.categories = filter.values?.map(v => {
          return {
            ...v,
            display: formatCategoryName(v?.display),
          };
        });
      }
    });

    brandsData.current = result.brand;
    categoriesData.current = result.categories;
  };

  const handleRecentSearchResult = (text: string, cta: NavigationBean) => {
    const actionUrl = `${getBaseURL()}${cta?.actionUrl?.replace(
      '[QUERY]',
      text,
    )}`;
    onChangeDiscoverMore({
      chip: text,
      cta,
      actionUrl,
      params: {
        query: text,
        searchValue: text,
      },
    });
    setSearchTextLocalState(text);
  };

  const handleDiscoverMoreSearchResult = (
    text: string,
    cta: NavigationBean,
  ) => {
    const actionUrl = `${getBaseURL()}${cta?.actionUrl?.replace(
      '[QUERY]',
      text,
    )}`;
    onChangeDiscoverMore({
      chip: text,
      cta,
      actionUrl,
      params: {
        query: text,
        searchValue: text,
      },
    });
    setSearchTextLocalState(text);
  };

  const onChangeDiscoverMore = ({chip, cta, actionUrl, params}: any) => {
    let keyWord = chip.replace(/\s*,\s*/g, ',');
    let searchText = keyWord
      .replace(/,+/g, ',')
      .replace(/,\s*,/g, ',')
      .replace(/^,|,$/g, '')
      .trim();
    keyWord = encodeURIComponent(keyWord);
    storeRecentSearchData(searchText);
    openPlpScreen({
      cta,
      actionUrl,
      params,
    });
  };

  const handleTrendingCategoryResult = (text: string, query: any, cta: any) => {
    const actionUrl = `${getBaseURL()}${cta?.actionUrl?.replace(
      '[SLUG]',
      text,
    )}`;
    openPlpScreen({
      cta,
      actionUrl,
      params: {
        slug: text,
        page: {
          department: query?.query?.department,
          category: query?.query?.category,
        },
      },
    });
  };

  const openPlpScreen = ({actionUrl, params, cta}: any) => {
    navigateTo(
      navBeanObj({
        ...cta,
        actionUrl,
        params,
      }),
      navigation,
    );
  };

  const openPDPScreen = ({actionUrl, cta}: any) => {
    navigateTo(
      navBeanObj({
        ...cta,
        actionUrl,
      }),
      navigation,
    );
  };

  const openMultiSearchBtmSheet = () => {
    setMultiSearchBottomSheet(true);
  };

  const storeRecentSearchData = async (newData: string) => {
    const data = await getPrefString(AsyncStorageKeys.RECENT_SEARCH);
    let jsonData = [];
    if (!isNullOrUndefinedOrEmpty(data)) {
      jsonData = JSON.parse(data);
      jsonData = jsonData.filter((item: string) => item !== newData);
      jsonData.unshift(newData);
    } else {
      jsonData = [newData];
    }
    await addStringPref(
      AsyncStorageKeys.RECENT_SEARCH,
      JSON.stringify(jsonData),
    );
  };

  const closeMultiSearchBtmSheet = () => {
    setMultiSearchBottomSheet(false);
  };

  const onCategoryItemClick = (l3_category_names: any, cta: NavigationBean) => {
    const actionUrl = `${getBaseURL()}${cta?.actionUrl
      ?.replace('[QUERY]', searchTextLocalState.toLowerCase())
      ?.replace('[L3_CATEGORY_NAMES]', encodeURIComponent(l3_category_names))}`;

    openPlpScreen({
      cta,
      actionUrl,
      params: {
        query: searchTextLocalState.toLowerCase(),
        filter: generateFilterReq({
          l3_category_names: [l3_category_names?.value],
        }),
        searchValue: searchTextLocalState,
      },
    });
  };

  const onBrandItemClick = (brand: any, cta: NavigationBean) => {
    const actionUrl = `${getBaseURL()}${cta?.actionUrl
      ?.replace('[QUERY]', searchTextLocalState.toLowerCase())
      ?.replace(
        '[BRAND]',
        JMSharedViewModel.Instance.appSource === AppSourceType.JM_BAU
          ? encodeURIComponent(brand)
          : brand?.value,
      )}`;

    openPlpScreen({
      cta,
      actionUrl,
      params: {
        query: searchTextLocalState.toLowerCase(),
        filter: generateFilterReq({
          brand: [brand?.value],
        }),
        searchValue: searchTextLocalState,
      },
    });
  };

  const onSearchResultItemClick = (
    item: string,
    index: number,
    count: number,
    cta: NavigationBean,
  ) => {
    storeRecentSearchData(item);
    let keyWord = item.replace(/\s*,\s*/g, ',');
    keyWord = encodeURIComponent(keyWord);
    const actionUrl = `${getBaseURL()}${cta?.actionUrl?.replace(
      '[QUERY]',
      keyWord,
    )}`;
    openPlpScreen({
      cta,
      actionUrl,
      params: {
        query: keyWord,
        searchValue: item,
      },
    });

    logAnalyticsEvent({
      ...configRef.current?.searchSuggestion_List?.analyticEvent
        ?.itemClickFirebase,
      payload: {
        ...configRef.current?.searchSuggestion_List?.analyticEvent
          ?.itemClickFirebase.payload,
        [AnalyticsParams.SEARCH_TERM]: keyWord,
        suggestive_search_term_index: index.toFixed(0),
        suggestive_search_count: count,
      },
    });

    const appsflyerEventDict = {
      [AnalyticsParams.AF_SEARCH_STRING]: keyWord,
    };

    logAnalyticsEvent({
      eventName: 'af_search',
      payload: {
        ...appsflyerEventDict,
      },
      channel: EventTriggerChannel.APPSFLYER,
    });
  };

  const onProductClicked = (slug: string, url: string, cta: NavigationBean) => {
    let actionUrl = '';
    if (JMSharedViewModel.Instance.appSource === AppSourceType.JM_BAU) {
      actionUrl = `${getBaseURL()}${url}`;
    } else {
      actionUrl = `${getBaseURL()}/product/${slug}`;
    }
    openPDPScreen({
      actionUrl,
      cta,
    });
  };

  const filterWhiteSearches = (text: string) => {
    var multiText = '';
    const subText = text.split(',');
    subText.map((item, index) => {
      if (item.trim() != '') {
        multiText = multiText + item + ',';
      }
    });
    text = multiText.slice(0, -1);
    return text;
  };

  const convertToPipeEncoded = (inputString: string): string => {
    // Split the input string by commas
    const items = inputString.split(',');
    // Join the items with '|'
    const pipeSeparated = items.join('|');
    // URL encode the string
    const encodedString = encodeURIComponent(pipeSeparated);
    return encodedString;
  };

  const onChangeMultiSearch = (searchString: string, cta: NavigationBean) => {
    const cleaned = searchString
      .split(',')
      .map(s => s.trim())
      .filter(Boolean)
      .join(', ');

    storeRecentSearchData(cleaned);

    const encodedQuery = encodeURIComponent(cleaned);

    const actionUrl = `${getBaseURL()}${cta?.actionUrl?.replace(
      '[QUERY]',
      encodedQuery,
    )}`;

    openPlpScreen({
      cta,
      actionUrl,
      params: {
        query: cleaned,
        searchValue: cleaned,
      },
    });
  };

  const handleNewSearchPageData = useCallback(
    async (query: string, config: any) => {
      try {
        const configApiId =
          configRef.current?.algoliaSearchConfiguration?.uiPathID;
        const configApiHash =
          configRef.current?.algoliaSearchConfiguration?.cardViewID;
        const algoliaFirstIndexName =
          configRef.current?.algoliaSearchConfiguration?.algoliaFirstIndexName;

        const algoliaSecondIndexName =
          configRef.current?.algoliaSearchConfiguration?.algoliaSecondIndexName;

        const algoliaIndex = [algoliaFirstIndexName, algoliaSecondIndexName];
        const hitsPerPageForSearchQuery =
          configRef.current?.algoliaSearchConfiguration
            ?.hitsPerPageForSearchQuery;
        const hitsPerPageForMasterVertical =
          configRef.current?.algoliaSearchConfiguration
            ?.hitsPerPageForMasterVertical;

        const hitsPerPage = [
          hitsPerPageForSearchQuery,
          hitsPerPageForMasterVertical,
        ];

        const results = await fetchAlgoliaSearch({
          query,
          algoliaConfig: algoliaConfig,
          algoliaIndex,
          hitsPerPage,
          configApiId,
          configApiHash,
        });

        const regionCode = algoliaConfig?.regionCode;

        const hits = results?.[algoliaSecondIndexName] || [];

        const filteredHits = !(
          getStoreData && Object.keys(getStoreData).length > 0
        )
          ? hits.filter(data => {
              if (data?.vertical_code === 'ALCOHOL') {
                setIsAlcoholProduct(true);
              }
              if (data?.vertical_code === 'LOCALSHOPS') {
                setIsLocalShopProduct(true);
              }
              return (
                data?.vertical_code !== 'LOCALSHOPS' &&
                data?.vertical_code !== 'ALCOHOL'
              );
            })
          : hits;

        const getBuyBoxDetails = (hitsData: any[]) => {
          return hitsData?.map(hit => {
            const verticalCode = hit?.vertical_code;
            const regionCodes = regionCode?.[verticalCode] || [];
            let matchedPricing = null;

            for (const regionCodeKey of regionCodes) {
              if (matchedPricing) {
                break;
              }
              Object.keys?.(hit?.buybox_mrp)?.forEach(key => {
                if (key.includes(regionCodeKey)) {
                  matchedPricing = hit?.buybox_mrp?.[key];
                }
              });
            }

            return {
              product_code: hit?.product_code,
              display_name: hit?.display_name,
              brand: hit?.brand,
              category_level: hit?.category_level?.level4 || [],
              food_type: hit?.food_type || '',
              vertical_code: hit?.vertical_code,
              image_path: hit?.image_path,
              url_path: hit?.url_path,
              buybox_mrp: matchedPricing || null,
            };
          });
        };

        const processedHits = getBuyBoxDetails(filteredHits);

        if (results) {
          const suggestionResults =
            results?.[
              config?.algoliaSearchConfiguration?.algoliaFirstIndexName
            ] || results?.prod_mart_query_suggestions;
          const masterVerticalResults = filteredHits;

          const level4Categories = masterVerticalResults
            ?.filter(
              item => item?.category_level && item?.category_level.level4,
            )
            ?.map(item => item?.category_level?.level4)
            ?.flat();

          const removeDuplicates = (array: any[]) => {
            return array?.filter(
              (item, index) => array?.indexOf(item) === index,
            );
          };

          const uniqueLevel4Categories = removeDuplicates(level4Categories);

          const extractedBrands = masterVerticalResults
            ?.filter(item => item?.brand)
            ?.map(item => item?.brand)
            ?.flat();

          const uniqueExtractedBrands = removeDuplicates(extractedBrands);

          //   // eslint-disable-next-line @typescript-eslint/no-unused-vars
          const newProducts = masterVerticalResults
            ?.filter(hit => hit.display_name)
            ?.map(hit => ({
              objectID: hit.objectID,
              display_name: hit.display_name,
              image_url: hit.image_url || hit.thumbnail_url,
            }));

          const suggestions = suggestionResults?.slice(
            0,
            config?.searchSuggestion_List?.maxItemsVisible,
          );
          const searchResultData = {
            items: migrateBAUSearchResultsToJCPSearchResults(suggestions),
          };
          setSearchResultData(searchResultData);
          brandsData.current = uniqueExtractedBrands;
          categoriesData.current = uniqueLevel4Categories;
          const searchResultExtData = {
            items: migrateBAUSearchDataModelToJCPSearchDataModel(processedHits),
          };
          setSearchResulExtData(searchResultExtData);
        } else {
          console.error('No results found');
        }
      } catch (error) {
        console.error('Error during search:', error);
      }
    },
    [config],
  );

  const migrateBAUSearchResultsToJCPSearchResults = (searchResults: any[]) => {
    return searchResults.map(result => ({
      action: {
        page: {
          type: 'products',
          query: {
            brand: [],
          },
        },
        type: 'page',
      },
      display: result.query,
      type: 'brand',
      logo: {
        url: '',
        type: 'image',
      },
      _custom_json: {},
    }));
  };

  const migrateBAUSearchDataModelToJCPSearchDataModel = (
    searchResults: any[],
  ) => {
    return searchResults.map(result => ({
      name: result.display_name,
      url: result.url_path,
      attributes: {
        'vertical-code': result.vertical_code,
      },
      item_code: result.product_code,
      discount: `${result.buybox_mrp.discount_pt} % OFF`,
      seller_id: result.buybox_mrp.seller_id,
      medias: [
        {
          type: 'image',
          url: `${getBaseURL()}/images/product/original/${result.image_path}`,
          alt: '',
        },
      ],
      price: {
        effective: {
          min: result.buybox_mrp.price,
          max: result.buybox_mrp.price,
        },
        marked: {
          min: result.buybox_mrp.mrp,
          max: result.buybox_mrp.mrp,
        },
      },
      sellable: result.buybox_mrp.available,
      minQty: result?.buybox_mrp?.min_qty_in_order,
      maxQty: result?.buybox_mrp?.max_qty_in_order,
      qty: result?.buybox_mrp?.min_qty_in_order,
    }));
  };

  return {
    config,
    sortedComponents,
    searchText: searchTextLocalState,
    handleRecentSearchResult,
    handleDiscoverMoreSearchResult,
    handleTrendingCategoryResult,
    openMultiSearchBtmSheet,
    closeMultiSearchBtmSheet,
    handleAutoComplete,
    onCategoryItemClick,
    onBrandItemClick,
    onSearchResultItemClick,
    onChangeMultiSearch,
    onProductClicked,
    isSearchActive,
    searchResultData,
    searchResultFilteredData,
    categoriesData,
    brandsData,
    searchResulExtData,
    multiSearchBottomSheet,
    sortedSspComponents,
  };
};

export default useJMSearchScreenController;
