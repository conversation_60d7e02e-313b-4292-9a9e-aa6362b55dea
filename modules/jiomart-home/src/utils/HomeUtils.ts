import {getScreenDim} from '../../../jiomart-common/src/JMResponsive';
import {
  AdTypes,
  type JioAdsProps,
} from '../../../jiomart-general/src/JioAds/types/JioAds';
import {generateDefaultAdMetaData} from '../../../jiomart-general/src/JioAds/utils/adUtility';

export const isFirstTab = (key: string) => {
  return ['lowest-price-guarantee', 'home'].includes(key);
};

export const getPersistString = (slug: string) => {
  return isFirstTab(slug) ? 'persist' : 'non-persist';
};

export const SPECIAL_IDENTIFIERS = {
  'top_deals_lowest_product_card_ds2.0': 'TOPDEAL',
  'top_picks_ds2.0': 'TOPPICKS',
  'new_top_deals_ds2.0': 'TOPDEAL',
  new_top_deals: 'TOPDEAL',
  // Add more special identifiers here as needed
};

export const extractJioAdsData = (item: string): JioAdsProps => {
  const jioAdsData = item?.split('@');
  const unit = jioAdsData[0]?.split('X');
  const height = unit?.[1] ?? 0;
  const width = unit?.[0] ?? 0;
  const adHeight = parseInt(height, 10);
  const adWidth = parseInt(width, 10);
  const adSpotId = jioAdsData[2];
  let adMetaData = {};
  generateDefaultAdMetaData().then(data => {
    adMetaData = data;
  });
  let adType: number = AdTypes.customNative.id;

  switch (jioAdsData[1]) {
    case AdTypes.customNative.name:
      adType = AdTypes.customNative.id;
      break;
    case AdTypes.dynamicDisplay.name:
      adType = AdTypes.dynamicDisplay.id;
      break;
    case AdTypes.instreamVideo.name:
      adType = AdTypes.instreamVideo.id;
      break;
    case AdTypes.interstitial.name:
      adType = AdTypes.interstitial.id;
      break;
    case AdTypes.nativeContentStream.name:
      adType = AdTypes.nativeContentStream.id;
      break;
    default:
      adType = AdTypes.customNative.id;
      break;
  }

  return {
    adHeight,
    adWidth,
    adSpotId,
    adType,
    adjHeight: adHeight / 2,
    adMetaData,
  };
};

export const getAdHeight = (alt: string, padding: number = 0) => {
  const adsData = extractJioAdsData(alt);
  return (
    (getScreenDim?.width - padding * 2) / (adsData.adWidth / adsData.adHeight)
  );
};
