export const enum JMJCPAddressEndpointKeys {
  ADDRESS_LIST = '/api/service/application/cart/v1.0/address',
  REMOVE_ADDRESS = '/api/service/application/cart/v1.0/address/{address_id}',
  DEFAULT_ADDRESS = '/api/service/application/cart/v1.0/address/{address_id}',
  EDIT_ADDRESS = '/api/service/application/cart/v1.0/address/{address_id}',
  NEW_ADDRESS = '/api/service/application/cart/v1.0/address',
  GET_PINCODE = '/api/service/application/logistics/v1.0/pincode/{pincode}',
}

export const enum JMBAUAddressEndpointKeys {
  ADDRESS_LIST = '/mst/rest/v1/address/v2/get/all',
  REMOVE_ADDRESS = '/mst/rest/v1/address/v2/del/{address_id}',
  DEFAULT_BILLING_ADDRESS = '/mst/rest/v1/entity/customer/set_preferred_billing_address/{address_id}',
  DEFAULT_SHIPPING_ADDRESS = '/mst/rest/v1/entity/customer/set_preferred_shipping_address/{address_id}',
  EDIT_ADDRESS = '/mst/rest/v1/address/v2/update/',
  NEW_ADDRESS = '/mst/rest/v1/address/v2/',
  GET_PINCODE = '/mst/rest/v1/pin/{pincode}',
}

export const enum JMJCPCartEndpointKeys {
  CART_LIST = '/ext/jmshipmentfee/cart/v1.0/get_cart',
  INITIALIZE_CART = '/ext/jmshipmentfee/cart/v1.0/add_items',
  UPDATE_CART = '/ext/jmshipmentfee/cart/v1.0/update_cart',
}

export const enum JMBAUCartEndpointKeys {
  CART_LIST = '/mst/rest/v1/cart/get',
  CREATE_CART = '/mst/rest/v1/cart/create',
  ADD_TO_CART = '/mst/rest/v1/cart/add_item',
  REMOVE_FROM_CART = '/mst/rest/v1/cart/remove_item',
}

export const enum JMBAUSearchEndpointKeys {
  RECOMMENDED_ITEMS = '/api/v1/customer/personalizedproducts',
  BUY_AGAIN = '/hcat/rest/v1/customer/telos',
  RECOMMENDED_ITEMS_SERVICIBILITY = '/hcat/rest/v1/catalog/algolia/inventory-check',
}
export const enum JMJCPAllCategoriesEndpointKeys {
  GET_DEPARTMENT = '/api/service/application/catalog/v1.0/departments/',
  GET_ALL_CATEGORIES = '/ext/algolia/application/api/v1.0/products',
}

export const enum JMBAUAllCategoriesEndpointKeys {
  GET_ALL_CATEGORIES = '/catalog/tree_json_plp/algolia',
}
export const enum JMJCPSearchEndpointKeys {
  RECOMMENDED_ITEMS = '/ext/mylist/rra',
  DISCOVER_MORE_ITEMS = '/ext/algolia/application/api/v1.0/discover-more',
  SEARCH_RESULTS = '/ext/algolia/application/api/v1.0/auto-complete',
  RECOMMENDED_ITEMS_SERVICIBILITY = '/ext/algolia/application/api/v1.0/deliverable/products/',
  BUY_AGAIN = '',
}

export const enum JMJCPProductEndpointKeys {
  PRODUCT_PRICE = '/api/service/application/catalog/v1.0/products/sizes/price',
  PRODUCT_SIZE = '/api/service/application/catalog/v1.0/products/{slug}/sizes',
  PRODUCT_LIST = '/ext/algolia/application/api/v1.0/collections/{slug}/items',
  PRODUCT_SEARCH_LIST = '/ext/algolia/application/api/v1.0/products',
}
export const enum JMBAUProductEndpointKeys {
  PRODUCT_PRICE = '/api/service/application/catalog/v1.0/products/sizes/price',
}

export const enum JMJCPDataLoaderEndpointKeys {
  DATA_LOADER = '/api/service/application/content/v1.0/data-loader',
}

export const enum JMJCPWishlistEndpointKeys {
  WISHLIST = '/api/service/application/catalog/v1.0/follow/{collection_type}/',
  ADD_TO_WISHLIST = '/api/service/application/catalog/v1.0/follow/{collection_type}/{collection_id}/',
  REMOVE_FROM_WISHLIST = '/api/service/application/catalog/v1.0/follow/{collection_type}/{collection_id}/',
}

export const enum JMJCPQCEndpointKeys {
  QC_DETAILS = '/api/service/application/logistics/v1.0/qc-promise',
}

export const enum JMBAUWishlistEndpointKeys {
  WISHLIST = '/bamboo/api/v2/catalog/mywishlist',
  ADD_TO_WISHLIST = '/bamboo/api/v2/catalog/mywishlist',
  REMOVE_FROM_WISHLIST = '/bamboo/api/v2/catalog/mywishlist',
}

export const enum JMBAUOrderEndpointKeys {
  ORDER_LIST = '/hcat/rest/v1/myorders/getmainorders',
  REFUND_LIST = '/api/v1/myorders/refundheader',
  REFUND_DETAILS = '/api/v1/myorders/refunddetails',
  SERVICE_REQUEST_LIST = '/api/v1/needhelp/listalltickets',
}

export const enum JMJCPOrderEndpointKeys {
  ORDER_LIST = '',
  REFUND_LIST = '',
  SERVICE_REQUEST_LIST = '',
}

export const enum JMJCPUserApiEndpointKeys {
  GET_USER_DETAILS = '/api/service/application/user/authentication/v1.0/session/',
  GET_LOGGED_IN_USER_SESSION = '/ext/retail-auth/api/v1.0/native-user-session-create',
  LOGOUT_USER = '/api/service/application/user/authentication/v1.0/logout',
}

export const enum JMBAUUserApiEndpointKeys {
  GET_USER_DETAILS = '/mst/rest/v1/entity/customer/get_details/',
  GET_LOGGED_IN_USER_SESSION = '/api/v1/cra/user/session',
  CREATE_GUEST_USER_SESSION = '/mst/rest/v1/session/create/guest',
  LOGOUT_USER = '/mst/rest/v1/session/logout',
  GET_CRA_CUSTOMER_DETAILS = '/service/application/account-centre/v1.0/get-profile',
}

export const enum JMBAURefreshTokenEndpointKeys {
  REFRESH_TOKEN = '/service/application/retail-auth/v2.0/refresh-token',
}

export const enum JMJCPFeedbackApiEndpointKeys {
  REVIEW_LIST = '/customer/v1/review/user',
}

export const enum JMBAUFeedbackApiEndpointKeys {
  REVIEW_LIST = '/customer/v1/review/user',
  SUBMIT_REVIEW = '/customer/v1/review/product',
  UPDATE_REVIEW = '/customer/v1/review/product/{reviewid}',
  REVIEW_VALIDATION_CONFIG = '/customer/op/v1/config/review-validation',
  RATING_AND_REVIEW_REQUEST_BODY = '/hcat/rest/v1/cm/get_list',
  GENERATE_SIGNED_URL = '',
  UPLOAD_IMAGE = '/customer/v1/file/multiUpload',
}

export const enum JMJCPHomeEndpointKeys {
  CHECK_SERVICEABILITY = '/payment/serviceability/check',
  GET_MCAT_INVENTORY = '/collection/mcat_pincode/get_mcat_inventory_code',
  GET_SINGLE_TOP_DEAL = '/cms/topdeals/get_single_topdeal',
  GET_HOMEPAGE_DATA = '/moonx/rest/v1/homepage/sections',
  GET_ALGOLIA_PRODUCT_DETAILS = '/hcat/rest/v1/catalog/algolia/searchproducts',
  // About Serviceability
  GET_PINCODE = '/mst/rest/v1/pin/{pincode}',
  HCAT_QC_CONFIG = '/hcat/rest/v1/cm/polygon/pinsearch/{pincode}',
  STORE_DETAILS = '/service/public/geography/v1.0/stores/serviceable',
  GET_REGION_CODE = '/mcat/rest/v1/5/pvt/mracer/get_region_code',
  CHECK_PROMISE = '/platform/logistics/api/v1/check-promise',
  SET_DELIVERY_DISTANCE = '/mst/rest/v1/5/pvt/set_delivery_distance',
  CHECK_PROMISE_WRAPPER = '/hcat/rest/v1/delivery/region',
}

export const enum JMBAUHomeEndpointKeys {
  CHECK_SERVICEABILITY = '/payment/serviceability/check',
  GET_MCAT_INVENTORY = '/collection/mcat_pincode/get_mcat_inventory_code',
  GET_SINGLE_TOP_DEAL = '/cms/topdeals/get_single_topdeal',
  GET_HOMEPAGE_DATA = '/moonx/rest/v1/homepage/sections',
  GET_WIDGETS = '/moonx/rest/v1/homepage/widgets',
  GET_SINGLE_WIDGET = '/moonx/rest/v1/homepage/singlewidgets',
  GET_CATEGORY_DETAILS = '/moonx/rest/v1/homepage/getcategorydetails',
  GET_ALGOLIA_PRODUCT_DETAILS = '/hcat/rest/v1/catalog/algolia/searchproducts',
  // About Serviceability
  GET_PINCODE = '/mst/rest/v1/pin/{pincode}',
  HCAT_QC_CONFIG = '/hcat/rest/v1/cm/polygon/pinsearch/{pincode}',
  STORE_DETAILS = '/service/public/geography/v1.0/stores/serviceable',
  GET_REGION_CODE = '/mcat/rest/v1/5/pvt/mracer/get_region_code',
  CHECK_PROMISE = '/platform/logistics/api/v1/check-promise',
  SET_DELIVERY_DISTANCE = '/mst/rest/v1/5/pvt/set_delivery_distance',
  CHECK_PROMISE_WRAPPER = '/hcat/rest/v1/delivery/region',
}

export type JMApiEndpointKeys =
  | JMJCPAddressEndpointKeys
  | JMBAUAddressEndpointKeys
  | JMJCPAllCategoriesEndpointKeys
  | JMBAUAllCategoriesEndpointKeys
  | JMJCPCartEndpointKeys
  | JMBAUCartEndpointKeys
  | JMJCPSearchEndpointKeys
  | JMJCPProductEndpointKeys
  | JMBAUProductEndpointKeys
  | JMJCPDataLoaderEndpointKeys
  | JMJCPWishlistEndpointKeys
  | JMJCPOrderEndpointKeys
  | JMBAUOrderEndpointKeys
  | JMBAUWishlistEndpointKeys
  | JMJCPHomeEndpointKeys
  | JMBAUHomeEndpointKeys
  | JMJCPQCEndpointKeys
  | JMBAUUserApiEndpointKeys
  | JMBAURefreshTokenEndpointKeys;
