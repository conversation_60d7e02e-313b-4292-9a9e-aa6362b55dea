import React from 'react';
import {
  StyleSheet,
  Pressable,
  View,
  type StyleProp,
  type ViewStyle,
} from 'react-native';
import {JioText, useColor} from '@jio/rn_components';
import {JioTypography, type JioColor} from '@jio/rn_components/src/index.types';
import {capitalizeFirstLetter} from '../../../jiomart-common/src/utils/JMStringUtility';
import JMTag from '../../../jiomart-general/src/ui/JMTag';

interface JMServiceRequestCardProps {
  style?: StyleProp<ViewStyle>;
  ticketId: string;
  badgeTitle?: string;
  badgeColor?: JioColor;
  taskTitle?: string;
  date?: string;
  subPrefixTitle?: string;
  subTitleDate?: string;
  customFormatingStyle?: {
    borderWidth?: number;
    borderRadius?: number;
    borderColor?: JioColor;
  };

  onPress?: () => void;
}

const JMHelpServiceRequestCard = (props: JMServiceRequestCardProps) => {
  const {
    style,
    ticketId,
    badgeTitle,
    badgeColor,
    taskTitle,
    date,
    subPrefixTitle,
    subTitleDate,
    customFormatingStyle,
    onPress,
  } = props;

  const borderWidth = customFormatingStyle?.borderWidth ?? 1;
  const borderRadius = customFormatingStyle?.borderRadius ?? 16;
  const borderColor = customFormatingStyle?.borderColor ?? 'primary_grey_40';

  const borderColorValue = useColor(borderColor);
  const badgeColorValue = useColor(badgeColor ?? 'primary_60');

  return (
    <Pressable
      style={[
        styles.card,
        {borderWidth, borderRadius, borderColor: borderColorValue},
        style,
      ]}
      onPress={onPress}>
      <View style={styles.headerRow}>
        <JioText
          text={ticketId}
          color={'primary_grey_80'}
          appearance={JioTypography.BODY_XS_BOLD}
          style={{flexShrink: 1}}
          maxLines={1}
        />
        {badgeTitle && (
          <JMTag
            style={{paddingHorizontal: 8, paddingVertical: 4}}
            color={badgeColorValue}
            text={capitalizeFirstLetter(badgeTitle ?? '')}
            textColor="white"
          />
        )}
      </View>
      <JioText
        style={styles.headerTitle}
        text={taskTitle ?? ''}
        color={'black'}
        appearance={JioTypography.BODY_XS_BOLD}
      />
      <JioText
        text={date ?? ''}
        color={'primary_grey_80'}
        appearance={JioTypography.BODY_XXS}
      />
      <View style={styles.inlineRow}>
        <JioText
          text={subPrefixTitle ?? ''}
          color={'primary_grey_80'}
          appearance={JioTypography.BODY_XXS}
        />
        <JioText
          text={subTitleDate ?? ''}
          color={'primary_grey_80'}
          appearance={JioTypography.BODY_XXS_BOLD}
        />
      </View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  inlineRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  card: {
    backgroundColor: '#fff',
    padding: 12,
  },

  headerTitle: {
    marginBottom: 8,
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  activeStatusText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 12,
  },
  resolvedStatusText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 12,
  },
});

export default JMHelpServiceRequestCard;
