import React from 'react';
import {addStringPref} from '../../../jiomart-common/src/JMAsyncStorageHelper';
import SharedPreferencesManager from '../bridge/JMRNBridge';
import {NativeCacheConstants} from './NativeCacheConstants';
import {AsyncStorageKeys} from '../../../jiomart-common/src/JMConstants';
import useUserSessionHook from '../../../jiomart-main/src/controllers/useUserSessionHook';
import {JMDatabaseManager} from '../../../jiomart-networkmanager/src/db/JMDatabaseManager';
import {Platform} from 'react-native';
import {PlatformType} from '../../../jiomart-common/src/JMObjectUtility';

const useNativeCacheHook = () => {
  const {saveUserSessionDetails, saveProfileDetails, handleAddressAfterLogin} =
    useUserSessionHook();

  const handleNativeCache = async () => {
    if (Platform.OS === PlatformType.IOS) {
      await handleIOSNativeCache();
    } else {
      await handleAndroidNativeCache();
    }
  };

  const handleAndroidNativeCache = async () => {
    const permissionPopUpShown = await SharedPreferencesManager.getBoolean(
      NativeCacheConstants.IS_PERMISSION_POP_UP_SHOWN,
    );
    if (permissionPopUpShown != null) {
      await addStringPref(
        AsyncStorageKeys.PERMISSION_DIALOG,
        permissionPopUpShown.toString(),
      );
    }

    const privacyPolicyPopUpShown = await SharedPreferencesManager.getBoolean(
      NativeCacheConstants.IS_POLICY_UPDATE_SHOWN,
    );
    if (privacyPolicyPopUpShown != null) {
      await addStringPref(
        AsyncStorageKeys.PRIVACY_POLICY,
        privacyPolicyPopUpShown.toString(),
      );
    }

    const pincodePopUpShown = await SharedPreferencesManager.getBoolean(
      NativeCacheConstants.JIOMART_IS_PINCODE_ENTERED
    )
    if(pincodePopUpShown != null){
      await addStringPref(AsyncStorageKeys.PINCODE_PROVIDED, pincodePopUpShown.toString())
    }


    const enableLocationPopUpShown = await SharedPreferencesManager.getBoolean(
      NativeCacheConstants.IS_POLYGON_PERMISSION_BOTTOMSHEET_SHOWN
    )
    if(enableLocationPopUpShown != null){
      await addStringPref(AsyncStorageKeys.ENABLE_LOCATION, enableLocationPopUpShown.toString())
    }

    let craSessionDetails: string | null = null;
    let guestSessionDetails: string | null = null;

    craSessionDetails = await SharedPreferencesManager.getDecryptedString(
      NativeCacheConstants.CRA_SESSION_DETAILS,
    );
    guestSessionDetails = await SharedPreferencesManager.getDecryptedString(
      NativeCacheConstants.JIOMART_SESSION_RESPONSE_DATA,
    );

    if (craSessionDetails) {
      const parsedCraSessionDetails = JSON.parse(craSessionDetails);
      await saveUserSessionDetails(parsedCraSessionDetails);
      await saveProfileDetails();
    } else if (guestSessionDetails) {
      const parsedGuestSessionDetails = JSON.parse(guestSessionDetails);
      const jsonString = JSON.stringify(
        parsedGuestSessionDetails?.result?.session,
      );
      JMDatabaseManager.user.saveGuestUserSession(jsonString);
    }

    const pincodeSelected = await SharedPreferencesManager.getString(
      NativeCacheConstants.PINCODE_SELECTED,
    );
    if (pincodeSelected) {
      await addStringPref(
        AsyncStorageKeys.X_LOCATION_DETAIL,
        JSON.stringify({pin: pincodeSelected}),
      );
      await handleAddressAfterLogin();
    }

    await SharedPreferencesManager.clearAll();
  };

  const handleIOSNativeCache = async () => {
    const permissionPopUpShown = await SharedPreferencesManager.getBoolean(
      NativeCacheConstants.IS_PERMISSION_POP_UP_SHOWN_IOS,
    );
    if (permissionPopUpShown != null) {
      await addStringPref(
        AsyncStorageKeys.PERMISSION_DIALOG,
        (!permissionPopUpShown).toString(),
      );
    }

    const privacyPolicyPopUpShown = await SharedPreferencesManager.getBoolean(
      NativeCacheConstants.IS_POLICY_UPDATE_SHOWN_IOS,
    );
    if (privacyPolicyPopUpShown != null) {
      await addStringPref(
        AsyncStorageKeys.PRIVACY_POLICY,
        privacyPolicyPopUpShown.toString(),
      );
    }

    const pincodeSelected = await SharedPreferencesManager.getDecryptedString(
      NativeCacheConstants.PINCODE_SELECTED_IOS,
    );
    if (pincodeSelected) {
      await addStringPref(
        AsyncStorageKeys.X_LOCATION_DETAIL,
        JSON.stringify({pin: pincodeSelected}),
      );
      await handleAddressAfterLogin();
    }

    let craSessionDetails: string | null = null;
    let guestSessionDetails: string | null = null;

    craSessionDetails = await SharedPreferencesManager.getDecryptedString(
      NativeCacheConstants.USER_SESSION,
    );
    guestSessionDetails = await SharedPreferencesManager.getDecryptedString(
      NativeCacheConstants.GUEST_SESSION,
    );
    
    if (craSessionDetails) {
      const parsedCraSessionDetails = JSON.parse(craSessionDetails);
      await saveUserSessionDetails(parsedCraSessionDetails?.data);
      await saveProfileDetails();
    } else if (guestSessionDetails) {
      const parsedGuestSessionDetails = JSON.parse(guestSessionDetails);
      const jsonString = JSON.stringify(
        parsedGuestSessionDetails?.result?.session,
      );
      JMDatabaseManager.user.saveGuestUserSession(jsonString);
    }
    await SharedPreferencesManager.clearAll();
  };

  return {
    handleNativeCache,
  };
};

export default useNativeCacheHook;
