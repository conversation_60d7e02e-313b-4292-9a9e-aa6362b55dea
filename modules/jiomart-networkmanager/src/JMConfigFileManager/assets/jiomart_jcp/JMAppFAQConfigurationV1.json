{"needHelpScreen": [{"title": "Need help with recent orders?", "subTitle": "", "viewType": "order_card", "order": 0, "rnVersion": "", "iosVersion": "2051", "androidVersion": "2063", "viewAllCta": {"actionType": "T001", "actionUrl": "customer/orderhistory", "bundle": "", "destination": "JMOrderListScreen", "headerType": 11, "headerVisibility": 1, "navTitle": "Recent Orders", "navigationType": "push", "params": null, "shouldShowDeliverToBar": false, "source": "", "userAuthenticationRequired": 2, "userJourneyRequiredState": 1, "shouldShowBottomNavBar": true, "type": "Orders"}}, {"title": "Service Requests", "subTitle": "", "viewType": "service_request", "order": 0, "rnVersion": "", "iosVersion": "2051", "androidVersion": "2063", "viewAllCta": {"actionType": "T001", "actionUrl": "customer/orderhistory", "bundle": "", "destination": "JMOrderListScreen", "headerType": 11, "headerVisibility": 1, "navTitle": "Service Requests", "navigationType": "push", "params": null, "shouldShowDeliverToBar": false, "source": "", "userAuthenticationRequired": 2, "userJourneyRequiredState": 1, "shouldShowBottomNavBar": true, "type": "service_request"}, "serviceRequestDetailCta": {"actionType": "T003", "actionUrl": "/help-support/ticket-chat?category=", "bundle": "", "destination": "", "headerType": 1, "headerVisibility": 1, "navTitle": "Help & Support", "navigationType": "push", "params": null, "shouldShowDeliverToBar": false, "source": "", "userAuthenticationRequired": 2, "userJourneyRequiredState": 1, "shouldShowBottomNavBar": true, "type": "service_request"}}, {"title": "Here are some quick links", "subTitle": "Track your orders, returns, refunds, and others.", "viewType": "quick_links", "order": 0, "rnVersion": "", "iosVersion": "2051", "androidVersion": "2063", "items": [{"iconName": "IcOrder", "title": "Your Orders", "isVisible": true, "cta": {"actionType": "T001", "actionUrl": "customer/orderhistory", "bundle": "", "destination": "JMOrderListScreen", "headerType": 11, "headerVisibility": 1, "navTitle": "My Orders", "navigationType": "push", "params": null, "shouldShowDeliverToBar": false, "source": "", "userAuthenticationRequired": 2, "userJourneyRequiredState": 1, "shouldShowBottomNavBar": true, "type": "Orders"}}, {"iconName": "IcRefund", "title": "Your Refunds", "isVisible": true, "cta": {"actionType": "T001", "actionUrl": "customer/orderhistory", "bundle": "", "destination": "JMOrderListScreen", "headerType": 11, "headerVisibility": 1, "navTitle": "My Orders", "navigationType": "push", "params": null, "shouldShowDeliverToBar": false, "source": "", "userAuthenticationRequired": 2, "userJourneyRequiredState": 1, "shouldShowBottomNavBar": true, "type": "Refunds"}}, {"iconName": "IcFavorite", "title": "Wallet & Cashbacks", "isVisible": true, "cta": {"actionType": "T003", "actionUrl": "/customer/account/jiomartwallets", "bundle": "", "destination": "CommonWebViewScreen", "headerType": 6, "headerVisibility": 1, "navTitle": "<PERSON><PERSON><PERSON><PERSON>", "navigationType": "push", "params": null, "shouldShowDeliverToBar": true, "source": "", "userAuthenticationRequired": 2, "userJourneyRequiredState": 1, "type": "<PERSON><PERSON><PERSON><PERSON>"}}, {"iconName": "IcProfile", "title": "Manage Account", "isVisible": true, "cta": {"actionType": "T003", "actionUrl": "customer/couponstore", "bundle": "", "destination": "CommonWebViewScreen", "headerType": 7, "headerVisibility": 1, "navTitle": "My Account", "navigationType": "push", "params": null, "shouldShowDeliverToBar": false, "source": "", "userAuthenticationRequired": 2, "userJourneyRequiredState": 1}}]}, {"title": "Need more help?", "subTitle": "", "viewType": "need_more_help", "order": 0, "rnVersion": "", "iosVersion": "2051", "androidVersion": "2063", "viewAllCta": {"actionType": "T001", "actionUrl": "customer/orderhistory", "bundle": "", "destination": "JMOrderListScreen", "headerType": 11, "headerVisibility": 1, "navTitle": "Faq", "navigationType": "push", "params": null, "shouldShowDeliverToBar": false, "source": "", "userAuthenticationRequired": 2, "userJourneyRequiredState": 1, "shouldShowBottomNavBar": true, "type": "Orders"}}, {"title": "Connect with us", "subTitle": "", "viewType": "connect_with_us", "order": 0, "rnVersion": "", "iosVersion": "2051", "androidVersion": "2063"}], "faqScreen": [{"title": "All", "order": 0, "rnVersion": "", "iosVersion": "2051", "androidVersion": "2063", "faqTypes": 0, "body": [{"viewType": "service_request", "rnVersion": "", "iosVersion": "2051", "androidVersion": "2063"}, {"viewType": "faq_view", "rnVersion": "", "iosVersion": "2051", "androidVersion": "2063"}, {"viewType": "connect_us", "rnVersion": "", "iosVersion": "2051", "androidVersion": "2063"}]}, {"title": "Registration", "order": 1, "rnVersion": "", "iosVersion": "2051", "androidVersion": "2063", "faqTypes": 0, "body": [{"viewType": "faq_view", "rnVersion": "", "iosVersion": "2051", "androidVersion": "2063", "faqs": [{"title": "How do I create a Ji<PERSON>Mart Account?", "subTitle": "You can create an acoount by downloading", "order": 0, "viewType": "faq", "rnVersion": "", "iosVersion": "2051", "androidVersion": "2063", "more_icon": "+", "less_icon": "-"}, {"title": "How do I create a Ji<PERSON>Mart Account?", "subTitle": "You can create an acoount by downloading", "order": 1, "viewType": "faq", "rnVersion": "", "iosVersion": "2051", "androidVersion": "2063", "more_icon": "+", "less_icon": "-"}, {"title": "How do I create a Ji<PERSON>Mart Account?", "subTitle": "You can create an acoount by downloading", "order": 2, "viewType": "faq", "rnVersion": "", "iosVersion": "2051", "androidVersion": "2063", "more_icon": "+", "less_icon": "-"}, {"title": "How do I create a Ji<PERSON>Mart Account?", "subTitle": "You can create an acoount by downloading", "viewType": "faq", "rnVersion": "", "iosVersion": "2051", "androidVersion": "2063", "order": 3, "more_icon": "+", "less_icon": "-"}]}]}, {"title": "Request", "faqType": "F003", "order": "2", "commonActionUrl": "", "body": [{"viewType": "service_request", "title": "email", "iconUrl": "", "actionTAG": "", "commonActionUrl": "", "order": 0, "faqs": [{"title": "How do I create a Ji<PERSON>Mart Account?", "subTitle": "You can create an acoount by downloading", "order": 0, "viewType": "faq", "rnVersion": "", "iosVersion": "2051", "androidVersion": "2063", "more_icon": "+", "less_icon": "-"}, {"title": "How do I create a Ji<PERSON>Mart Account?", "subTitle": "You can create an acoount by downloading", "order": 0, "viewType": "faq", "rnVersion": "", "iosVersion": "2051", "androidVersion": "2063", "more_icon": "+", "less_icon": "-"}, {"title": "How do I create a Ji<PERSON>Mart Account?", "subTitle": "You can create an acoount by downloading", "order": 0, "viewType": "faq", "rnVersion": "", "iosVersion": "2051", "androidVersion": "2063", "more_icon": "+", "less_icon": "-"}]}]}, {"title": "Product & Catalog", "rnVersion": "", "iosVersion": "2051", "androidVersion": "2063", "faqTypes": 0, "order": 2, "body": [{"viewType": "faq_view", "faqs": [{"title": "How do I create a Ji<PERSON>Mart Account?", "subTitle": "You can create an acoount by downloading", "type": "faq", "viewType": "action", "rnVersion": "", "iosVersion": "2051", "androidVersion": "2063", "order": 0, "more_icon": "+", "less_icon": "-"}]}]}, {"title": "Connect With Us", "order": 3, "rnVersion": "", "iosVersion": "2051", "androidVersion": "2063", "faqTypes": 1, "commonActionUrl": "", "body": [{"rnVersion": "", "iosVersion": "2051", "androidVersion": "2063", "viewType": "connect_us", "faqs": [{"title": "chat with us", "subTitle": "lorem Ipsum", "viewType": "action", "rnVersion": "", "iosVersion": "2051", "androidVersion": "2063", "iconUrl": "", "actionTAG": "", "commonActionUrl": "", "order": 0}, {"title": "Raise a request", "subTitle": "lorem Ipsum", "viewType": "action", "rnVersion": "", "iosVersion": "2051", "androidVersion": "2063", "iconUrl": "", "actionTAG": "", "commonActionUrl": "", "order": 1}, {"title": "call us", "subTitle": "lorem Ipsum", "viewType": "action", "rnVersion": "", "iosVersion": "2051", "androidVersion": "2063", "iconUrl": "", "actionTAG": "", "commonActionUrl": "", "order": 2}, {"title": "<PERSON><PERSON><PERSON>t", "subTitle": "lorem Ipsum", "viewType": "action", "rnVersion": "", "iosVersion": "2051", "androidVersion": "2063", "iconUrl": "", "actionTAG": "", "commonActionUrl": "", "order": 3}]}]}]}