import React, {useState, useRef, useEffect, useCallback, useMemo} from 'react';
import {
  Dimensions,
  GestureResponderEvent,
  LayoutChangeEvent,
  NativeScrollEvent,
  NativeSyntheticEvent,
  Platform,
  ScrollView,
  StyleSheet,
  View,
  AppState,
  AppStateStatus,
} from 'react-native';
import {useFocusEffect, useIsFocused} from '@react-navigation/native';

type TOnSwipe = {
  left?: () => void;
  right?: () => void;
  up?: () => void;
  down?: () => void;
};

export const useSwipeHandler = () => {
  const startX = useRef(0);
  const startY = useRef(0);

  const onTouchStart = useCallback((e: GestureResponderEvent) => {
    startX.current = e.nativeEvent.pageX;
    startY.current = e.nativeEvent.pageY;
  }, []);

  const onScrollBeginDrag = useCallback(
    (e: NativeSyntheticEvent<NativeScrollEvent>) => {
      startX.current = e.nativeEvent.contentOffset.x;
      startY.current = e.nativeEvent.contentOffset.y;
    },
    [],
  );

  const onTouchEnd = useCallback(
    (e: GestureResponderEvent, onSwipe?: TOnSwipe) => {
      const distX = e.nativeEvent.pageX - startX.current;
      const distY = e.nativeEvent.pageY - startY.current;
      if (Math.abs(distX) > Math.abs(distY)) {
        // moved horizontally
        if (distX > 0) {
          // right
          if (onSwipe?.right) onSwipe.right();
        } else {
          // left
          if (onSwipe?.left) onSwipe.left();
        }
      } else {
        // moved vertically
        if (distY > 0) {
          // down
          if (onSwipe?.down) onSwipe.down();
        } else {
          // up
          if (onSwipe?.up) onSwipe.up();
        }
      }
    },
    [],
  );

  const onScrollEndDrag = useCallback(
    (e: NativeSyntheticEvent<NativeScrollEvent>, onSwipe?: TOnSwipe) => {
      const distX = e.nativeEvent.contentOffset.x - startX.current;
      const distY = e.nativeEvent.contentOffset.y - startY.current;
      if (Math.abs(distX) > Math.abs(distY)) {
        // moved horizontally
        if (distX < 0) {
          // right
          if (onSwipe?.right) onSwipe.right();
        } else {
          // left
          if (onSwipe?.left) onSwipe.left();
        }
      } else {
        // moved vertically
        if (distY < 0) {
          // down
          if (onSwipe?.down) onSwipe.down();
        } else {
          // up
          if (onSwipe?.up) onSwipe.up();
        }
      }
    },
    [],
  );

  return useMemo(
    () => ({
      onTouchStart,
      onTouchEnd,
      onScrollBeginDrag,
      onScrollEndDrag,
    }),
    [onTouchStart, onTouchEnd, onScrollBeginDrag, onScrollEndDrag],
  );
};

type TCustomCarouselProps<T> = {
  items: (T & {key?: React.Key})[];
  RenderItem: (props: T) => React.JSX.Element | null;
  onIndexChange?: (index: number) => void;
  customStyles?: {
    indicatorList?: {[key: string]: any};
    itemList?: {[key: string]: any};
    indicatorItemList?: {[key: string]: any};
  };
  autoScroll?: boolean;
  autoScrollTimer?: number;
  onAutoScrollFinish?: () => void;
  showIndicator?: boolean;
  peek?: number;
};

const CustomCarousel = <T,>({
  items,
  RenderItem,
  onIndexChange,
  customStyles,
  autoScroll = false,
  autoScrollTimer = 3000,
  onAutoScrollFinish,
  showIndicator = true,
  peek = 0,
}: TCustomCarouselProps<T>) => {
  // Memoize filtered items to prevent unnecessary recalculations
  const validItems = useMemo(() => {
    return items?.filter?.(item => !!item) ?? [];
  }, [items]);

  const swipeHandlers = useSwipeHandler();
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isInFocus, setIsInFocus] = useState(false);
  const scrollViewRef = useRef<ScrollView>(null);
  const carouselViewRef = useRef<View>(null);
  const scrollViewWidth = useRef(Dimensions.get('window').width);
  const autoScrollTimeout = useRef<NodeJS.Timeout | null>(null);
  const currentIndexRef = useRef(0);
  const itemsLengthRef = useRef(0);
  const isAutoScrolling = useRef(false);

  // Memoize calculated dimensions
  const dimensions = useMemo(() => {
    const ITEM_WIDTH = scrollViewWidth.current - peek * 2;
    const SPACER_WIDTH = peek;
    return {ITEM_WIDTH, SPACER_WIDTH};
  }, [peek]);

  // Memoize styles to prevent object recreation
  const scrollViewStyle = useMemo(
    () => [
      styles.scrollView,
      customStyles?.itemList ? customStyles.itemList : null,
    ],
    [customStyles?.itemList],
  );

  const contentContainerStyle = useMemo(
    () => (peek > 0 ? {paddingHorizontal: dimensions.SPACER_WIDTH} : {}),
    [peek, dimensions.SPACER_WIDTH],
  );

  const indicatorListStyle = useMemo(
    () => [
      styles.carouselIndicatorsView,
      customStyles?.indicatorList ? customStyles?.indicatorList : null,
    ],
    [customStyles?.indicatorList],
  );

  // Reset carousel to first slide
  const resetToFirstSlide = useCallback(() => {
    setCurrentIndex(0);
    currentIndexRef.current = 0;
    if (scrollViewRef.current) {
      scrollViewRef.current.scrollTo({
        x: 0,
        animated: false,
      });
    }
  }, []);

  // Stop auto-scroll timer
  const stopAutoScrollTimer = useCallback(() => {
    if (autoScrollTimeout.current) {
      clearTimeout(autoScrollTimeout.current);
      autoScrollTimeout.current = null;
    }
  }, []);

  // Function to start/restart auto-scroll timer only when in focus
  const startAutoScrollTimer = useCallback(() => {
    if (autoScrollTimeout.current) clearTimeout(autoScrollTimeout.current);
    if (autoScroll && isInFocus && validItems.length > 1) {
      autoScrollTimeout.current = setTimeout(() => {
        _onSwipeLeft(true);
      }, autoScrollTimer);
    }
  }, [autoScroll, isInFocus, validItems.length, autoScrollTimer]);

  // Check if we're in a navigation context
  const navigationIsFocused = useIsFocused();

  // Handle app state changes for background/foreground detection
  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (nextAppState === 'active') {
        setIsInFocus(navigationIsFocused);
      } else {
        setIsInFocus(false);
        stopAutoScrollTimer();
        resetToFirstSlide();
      }
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );
    return () => subscription?.remove();
  }, [navigationIsFocused, stopAutoScrollTimer, resetToFirstSlide]);

  // Handle navigation focus changes
  useEffect(() => {
    if (AppState.currentState === 'active') {
      setIsInFocus(navigationIsFocused);
    } else {
      setIsInFocus(false);
    }
  }, [navigationIsFocused]);

  // Handle screen focus/blur events for navigation context
  useFocusEffect(
    useCallback(() => {
      // Screen is focused and app is active
      if (AppState.currentState === 'active') {
        setIsInFocus(true);
      }
      return () => {
        // Screen is blurred
        setIsInFocus(false);
        stopAutoScrollTimer();
        resetToFirstSlide();
      };
    }, [stopAutoScrollTimer, resetToFirstSlide]),
  );

  useEffect(() => {
    itemsLengthRef.current = validItems.length;
  }, [validItems]);

  // Start/stop auto-scroll based on focus state
  useEffect(() => {
    if (isInFocus) {
      startAutoScrollTimer();
    } else {
      stopAutoScrollTimer();
    }

    return () => {
      stopAutoScrollTimer();
    };
  }, [isInFocus, startAutoScrollTimer, stopAutoScrollTimer]);

  useEffect(() => {
    if (onIndexChange) {
      onIndexChange(currentIndex);
    }
    if (scrollViewRef.current && isInFocus) {
      scrollViewRef.current.scrollTo({
        x: currentIndex * dimensions.ITEM_WIDTH,
        animated: true,
      });
    }
  }, [currentIndex, isInFocus, onIndexChange, dimensions.ITEM_WIDTH]);

  const _onSwipeLeft = useCallback(
    (autoScrollContinue = false) => {
      const newIndex = (currentIndexRef.current + 1) % itemsLengthRef.current;
      if (newIndex < itemsLengthRef.current) {
        setCurrentIndex(newIndex);
        currentIndexRef.current = newIndex;
      }
      if (autoScrollContinue && autoScroll && isInFocus) {
        isAutoScrolling.current = true; // Mark as auto-scrolling
        if (autoScrollTimeout.current) clearTimeout(autoScrollTimeout.current);
        if (newIndex == itemsLengthRef.current - 1 && onAutoScrollFinish) {
          autoScrollTimeout.current = setTimeout(() => {
            onAutoScrollFinish();
            isAutoScrolling.current = false; // Reset flag
          }, autoScrollTimer);
        } else {
          autoScrollTimeout.current = setTimeout(() => {
            _onSwipeLeft(true);
          }, autoScrollTimer);
        }
      } else if (peek > 0) {
        if (newIndex < itemsLengthRef.current) {
          if (scrollViewRef.current) {
            isAutoScrolling.current = autoScrollContinue; // Set flag for programmatic scroll
            scrollViewRef.current.scrollTo({
              x: newIndex * dimensions.ITEM_WIDTH,
              animated: true,
            });
          }
        }
      }
    },
    [
      autoScroll,
      isInFocus,
      autoScrollTimer,
      onAutoScrollFinish,
      peek,
      dimensions.ITEM_WIDTH,
    ],
  );

  const _onSwipeRight = useCallback(() => {
    if (currentIndex > 0) {
      const newIndex = currentIndex - 1;
      setCurrentIndex(newIndex);
      currentIndexRef.current = newIndex;
      if (peek > 0) {
        if (scrollViewRef.current) {
          scrollViewRef.current.scrollTo({
            x: newIndex * dimensions.ITEM_WIDTH,
            animated: true,
          });
        }
      }
    }
  }, [currentIndex, peek, dimensions.ITEM_WIDTH]);

  const onSwipeWrapper = useCallback(
    (swipeAction: () => void) => {
      stopAutoScrollTimer();
      isAutoScrolling.current = false; // Reset flag on manual swipe
      swipeAction();
      // Restart auto-scroll after user swipe only if in focus
      if (isInFocus) {
        startAutoScrollTimer();
      }
    },
    [stopAutoScrollTimer, isInFocus, startAutoScrollTimer],
  );

  const onLayout = useCallback((e: LayoutChangeEvent) => {
    scrollViewWidth.current = e.nativeEvent.layout.width; //dynamically get scroll view component width
  }, []);

  // Real-time scroll tracking for instant indicator updates
  const onScroll = useCallback(
    (e: NativeSyntheticEvent<NativeScrollEvent>) => {
      // Only update during manual scrolling, not during auto-scroll
      if (peek > 0 && !isAutoScrolling.current) {
        const newIndex = Math.round(
          e.nativeEvent.contentOffset.x / dimensions.ITEM_WIDTH,
        );
        if (
          newIndex !== currentIndexRef.current &&
          newIndex >= 0 &&
          newIndex < itemsLengthRef.current
        ) {
          setCurrentIndex(newIndex);
          currentIndexRef.current = newIndex;
        }
      }
    },
    [peek, dimensions.ITEM_WIDTH],
  );

  const onMomentumScrollEnd = useCallback(
    (e: NativeSyntheticEvent<NativeScrollEvent>) => {
      // Reset auto-scrolling flag when momentum ends
      isAutoScrolling.current = false;

      if (peek > 0) {
        const newIndex = Math.round(
          e.nativeEvent.contentOffset.x / dimensions.ITEM_WIDTH,
        );
        if (newIndex !== currentIndexRef.current) {
          setCurrentIndex(newIndex);
          currentIndexRef.current = newIndex;
          if (isInFocus) {
            startAutoScrollTimer();
          }
        }
      }
    },
    [peek, dimensions.ITEM_WIDTH, isInFocus, startAutoScrollTimer],
  );

  // Memoize touch handlers to prevent inline function creation
  const handleTouchEnd = useCallback(
    (e: GestureResponderEvent) => {
      swipeHandlers.onTouchEnd(e, {
        left: () => {
          onSwipeWrapper(_onSwipeLeft);
        },
        right: () => {
          onSwipeWrapper(_onSwipeRight);
        },
      });
    },
    [swipeHandlers, onSwipeWrapper, _onSwipeLeft, _onSwipeRight],
  );

  const handleScrollEndDrag = useCallback(
    (e: NativeSyntheticEvent<NativeScrollEvent>) => {
      swipeHandlers.onScrollEndDrag(e, {
        left: () => {
          onSwipeWrapper(_onSwipeLeft);
        },
        right: () => {
          onSwipeWrapper(_onSwipeRight);
        },
      });
    },
    [swipeHandlers, onSwipeWrapper, _onSwipeLeft, _onSwipeRight],
  );

  // Memoize rendered items to prevent re-rendering on each scroll
  const renderedItems = useMemo(() => {
    return validItems.map((item, index) => {
      const itemWidth =
        peek > 0 ? dimensions.ITEM_WIDTH : scrollViewWidth.current;
      return (
        <View style={{width: itemWidth}} key={item.key ?? index}>
          <RenderItem {...item} />
        </View>
      );
    });
  }, [validItems, peek, dimensions.ITEM_WIDTH, RenderItem]);

  // Memoize indicator items
  const indicatorItems = useMemo(() => {
    if (!showIndicator || validItems.length <= 1) return null;

    return validItems.map((item, index) => (
      <View
        style={[
          styles.carouselIndicatorItem,
          customStyles?.indicatorItemList
            ? customStyles?.indicatorItemList
            : null,
          index == currentIndex ? styles.carouselIndicatorItemSelected : null,
        ]}
        key={index}
      />
    ));
  }, [
    validItems,
    currentIndex,
    customStyles?.indicatorItemList,
    showIndicator,
  ]);

  return (
    <View ref={carouselViewRef}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        showsHorizontalScrollIndicator={false}
        bounces={false}
        ref={scrollViewRef}
        onLayout={onLayout}
        style={scrollViewStyle}
        contentContainerStyle={contentContainerStyle}
        horizontal={true}
        pagingEnabled={peek === 0}
        decelerationRate={peek > 0 ? 'fast' : 'normal'}
        snapToInterval={peek > 0 ? dimensions.ITEM_WIDTH : 0}
        onScroll={onScroll}
        onMomentumScrollEnd={onMomentumScrollEnd}
        scrollEventThrottle={16}
        onTouchStart={
          Platform.OS == 'ios' && peek === 0
            ? swipeHandlers.onTouchStart
            : undefined
        }
        onScrollBeginDrag={
          Platform.OS == 'android' && peek === 0
            ? swipeHandlers.onScrollBeginDrag
            : undefined
        }
        onTouchEnd={
          Platform.OS == 'ios' && peek === 0 ? handleTouchEnd : undefined
        }
        onScrollEndDrag={
          Platform.OS == 'android' && peek === 0
            ? handleScrollEndDrag
            : undefined
        }>
        {renderedItems}
      </ScrollView>
      {showIndicator && (
        <View style={indicatorListStyle}>{indicatorItems}</View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  scrollView: {
    height: '100%',
    width: '100%',
  },
  carouselIndicatorsView: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'center',
    columnGap: 8,
    width: '100%',
  },
  carouselIndicatorItem: {
    height: 8,
    width: 8,
    borderRadius: 100,
    backgroundColor: '#0C5273',
    opacity: 0.3,
  },
  carouselIndicatorItemSelected: {
    width: 24,
    backgroundColor: '#0C5273',
    opacity: 1,
  },
});

// Memoize the component to prevent unnecessary re-renders
export default React.memo(CustomCarousel) as <T>(
  props: TCustomCarouselProps<T>,
) => React.JSX.Element;
