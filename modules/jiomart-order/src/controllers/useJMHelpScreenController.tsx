import {useState} from 'react';
import {NativeSyntheticEvent, NativeScrollEvent} from 'react-native';
import {UseJMHelpScreenProps} from '../types/JMHelpScreenType';
import JMOrderNetworkController from '../../../jiomart-networkmanager/src/JMNetworkController/JMOrderNetworkController';
import {useQuery} from '@tanstack/react-query';
import {AsyncStorageKeys, RQKey} from '../../../jiomart-common/src/JMConstants';
import {useConfigFile} from '../../../jiomart-general/src/hooks/useJMConfig';
import {JMConfigFileName} from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import {navigateTo} from '../../../jiomart-general/src/navigation/JMNavGraph';
import {navBeanObj} from '../../../jiomart-common/src/JMNavGraphUtil';
import {
  addStringPref,
  getPrefString,
} from '../../../jiomart-common/src/JMAsyncStorageHelper';

export interface QuickLink {
  id: string;
  icon: string;
  label: string;
  onPress: () => void;
}

export interface FAQ {
  id: string;
  question: string;
  answer: string;
  isExpanded: boolean;
}

export type TabType = 'Product' | 'Ordering' | 'Payments' | 'Delivery';

const orderController = new JMOrderNetworkController();

const useJMHelpScreenController = (props: UseJMHelpScreenProps) => {
  const {route, navigation} = props;

  const [selectedTab, setSelectedTab] = useState<TabType>('Product');
  const [showFloatingButton, setShowFloatingButton] = useState(false);
  const [visible, setVisible] = useState(true);
  const [orderVisible, setOrderVisible] = useState(true);
  const [orderBlockVisible, setOrderBlockVisible] = useState(true);
  const [serviceRequestBlockVisible, setServiceRequestBlockVisible] =
    useState(true);

  const helpFaqConfig = useConfigFile(
    JMConfigFileName.JMAppFaqConfigurationFileName,
  );

  const orderListRes = useQuery({
    queryKey: [RQKey.ORDER_LIST_HELP],
    queryFn: async ({pageParam = 1}) => {
      const orderResponse = await orderController.getOrderList({
        pageIndex: pageParam,
        pageSize: 5,
      });

      try {
        await addStringPref(
          AsyncStorageKeys.ORDER_RESPONSE,
          JSON.stringify(orderResponse),
        );
      } catch (error) {}
      if (orderResponse) {
        setOrderVisible(true);
      } else {
        setOrderVisible(false);
        setOrderBlockVisible(false);
      }
      return orderResponse;
    },
    retry: 0,
    placeholderData: (previousData, _) => previousData,
    initialData: () => getPrefString(AsyncStorageKeys.ORDER_RESPONSE),
    enabled: true,
  });
  console.warn(
    'orderResponse',
    'orderResponse---' + JSON.stringify(orderListRes.isFetching),
  );

  const serviceRequestListRes = useQuery({
    queryKey: [RQKey.SERVICE_REQUEST_LIST],
    queryFn: async ({pageParam = 1}) => {
      try {
        const serviceRequestResponse =
          await orderController.getServiceRequestList();

        console.warn(
          'serviceRequestResponse',
          'serviceRequestResponse---' + JSON.stringify(serviceRequestResponse),
        );

        // Set visibility to true after successful response

        if (serviceRequestResponse) {
          setVisible(true);
        } else {
          setVisible(false);
          setServiceRequestBlockVisible(false);
        }
        try {
          await addStringPref(
            AsyncStorageKeys.SERVICE_REQUEST_RESPONSE,
            JSON.stringify(serviceRequestResponse),
          );
        } catch (error) {}
        return serviceRequestResponse;
      } catch (error) {
        console.error('Error fetching service requests:', error);
        throw error;
      }
    },
    retry: 0,
    placeholderData: (previousData, _) => previousData,
    initialData: () => getPrefString(AsyncStorageKeys.SERVICE_REQUEST_RESPONSE),
    enabled: true,
  });

  console.warn('serviceRequestListRes', JSON.stringify(serviceRequestListRes));

  const [faqs, setFaqs] = useState<Record<TabType, FAQ[]>>({
    Product: [
      {
        id: '1',
        question: 'How do I know if I placed my order correctly?',
        answer:
          'You will receive an order confirmation email and SMS with your order details once your order is successfully placed.',
        isExpanded: false,
      },
      {
        id: '2',
        question: 'Can I call and place an order?',
        answer:
          'Yes, you can call our customer service number to place an order. Our representatives will assist you with the ordering process.',
        isExpanded: false,
      },
      {
        id: '3',
        question: 'How are the fruits and vegetables weighed?',
        answer:
          'All fruits and vegetables are weighed accurately using calibrated digital scales at our fulfillment centers.',
        isExpanded: false,
      },
      {
        id: '4',
        question: 'How do I make changes to my order?',
        answer:
          'You can modify your order within 30 minutes of placing it by going to "My Orders" section and selecting "Modify Order".',
        isExpanded: false,
      },
    ],
    Ordering: [
      {
        id: '5',
        question: 'What are the delivery time slots available?',
        answer:
          'We offer multiple delivery slots throughout the day. You can choose your preferred slot during checkout.',
        isExpanded: false,
      },
      {
        id: '6',
        question: 'Can I schedule my order for later delivery?',
        answer:
          'Yes, you can schedule your order for delivery up to 7 days in advance during the checkout process.',
        isExpanded: false,
      },
      {
        id: '7',
        question: 'What is the minimum order value?',
        answer:
          'The minimum order value varies by location. You can check the minimum order amount for your area during checkout.',
        isExpanded: false,
      },
      {
        id: '8',
        question: 'How do I track my order?',
        answer:
          'You can track your order in real-time through the "Track Order" section in the app or via the tracking link sent to your email.',
        isExpanded: false,
      },
    ],
    Payments: [
      {
        id: '9',
        question: 'What payment methods do you accept?',
        answer:
          'We accept all major credit/debit cards, UPI, net banking, wallets, and cash on delivery.',
        isExpanded: false,
      },
      {
        id: '10',
        question: 'When will my money be refunded?',
        answer:
          'Refunds are processed within 5-7 business days to your original payment method.',
        isExpanded: false,
      },
      {
        id: '11',
        question: 'Can I use multiple payment methods for one order?',
        answer:
          'Currently, you can use only one payment method per order. However, you can combine wallet balance with other payment methods.',
        isExpanded: false,
      },
      {
        id: '12',
        question: 'How do I apply a coupon or promo code?',
        answer:
          'You can apply coupon codes during checkout by entering the code in the "Apply Coupon" section.',
        isExpanded: false,
      },
    ],
    Delivery: [
      {
        id: '9',
        question: 'What payment methods do you accept?',
        answer:
          'We accept all major credit/debit cards, UPI, net banking, wallets, and cash on delivery.',
        isExpanded: false,
      },
      {
        id: '10',
        question: 'When will my money be refunded?',
        answer:
          'Refunds are processed within 5-7 business days to your original payment method.',
        isExpanded: false,
      },
      {
        id: '11',
        question: 'Can I use multiple payment methods for one order?',
        answer:
          'Currently, you can use only one payment method per order. However, you can combine wallet balance with other payment methods.',
        isExpanded: false,
      },
      {
        id: '12',
        question: 'How do I apply a coupon or promo code?',
        answer:
          'You can apply coupon codes during checkout by entering the code in the "Apply Coupon" section.',
        isExpanded: false,
      },
    ],
  });

  const tabs: TabType[] = ['Product', 'Ordering', 'Payments'];

  const handleTabPress = (tab: TabType) => {
    setSelectedTab(tab);
  };

  const handleFAQToggle = (faqId: string) => {
    setFaqs(prevFaqs => ({
      ...prevFaqs,
      [selectedTab]: prevFaqs[selectedTab].map(faq =>
        faq.id === faqId ? {...faq, isExpanded: !faq.isExpanded} : faq,
      ),
    }));
  };

  const handleQuickLinkPress = (cta: any) => {
    console.log(`Quick link pressed: ${JSON.stringify(cta)}`);
    navigateTo(cta, props.navigation);
  };

  const handleOrderCardPress = (orderId: string) => {
    console.log(`Order card pressed: ${orderId}`);
    // Implement navigation to order details
  };

  const handleServiceRequestCardPress = (
    serviceRequestId: string,
    cta: any,
  ) => {
    console.log(
      `Service request card pressed: ${serviceRequestId} ` +
        JSON.stringify(cta),
    );
    cta.actionUrl = cta?.actionUrl + serviceRequestId;
    navigateTo(cta, props.navigation);
    // Implement navigation to order details
  };
  const handleViewAllPress = () => {
    console.log('View All pressed');
    // Implement navigation to full FAQ list
  };

  const handleViewAllOrdersPress = (viewAllCta: any) => {
    console.log('View All orders---' + JSON.stringify(viewAllCta));
    navigateTo(viewAllCta, props.navigation);
  };

  const handleViewAllServiceRequestsPress = (viewAllCta: any) => {
    console.log('View All service requests---' + JSON.stringify(viewAllCta));
    navigateTo(
      navBeanObj({
        ...viewAllCta,
      }),
      navigation,
    );
    // Implement navigation to full FAQ list
  };

  const handleViewAllFaqsPress = () => {
    console.log('View All faqs');
    // Implement navigation to full FAQ list
  };
  const handleLetsChatPress = () => {
    console.log("Let's Chat pressed");
    // Implement chat functionality
  };

  const getCurrentFAQs = () => {
    return faqs[selectedTab] || [];
  };

  const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const scrollY = event.nativeEvent.contentOffset.y;
    const threshold = 50; // Show button after scrolling 50px

    if (scrollY > threshold && !showFloatingButton) {
      setShowFloatingButton(true);
    } else if (scrollY <= threshold && showFloatingButton) {
      setShowFloatingButton(false);
    }
  };

  return {
    ...props,
    navigationBean: route.params,
    selectedTab,
    showFloatingButton,
    visible,
    orderVisible,
    orderData: orderListRes?.data?.result?.orderList,
    serviceRequestData: serviceRequestListRes?.data?.result,
    tabs,
    getCurrentFAQs,
    handleTabPress,
    handleFAQToggle,
    handleQuickLinkPress,
    handleOrderCardPress,
    handleServiceRequestCardPress,
    handleViewAllPress,
    handleViewAllOrdersPress,
    handleViewAllServiceRequestsPress,
    handleViewAllFaqsPress,
    handleLetsChatPress,
    handleScroll,
    helpFaqConfig,
    isOrderLoading: orderListRes?.isFetching,
    isServiceRequestLoading: serviceRequestListRes?.isFetching,
    orderBlockVisible,
    serviceRequestBlockVisible,
  };
};

export default useJMHelpScreenController;
