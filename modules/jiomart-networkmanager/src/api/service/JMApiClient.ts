import axios, {
  AxiosInstance,
  AxiosResponse,
  type InternalAxiosRequestConfig,
} from 'axios';
import {handleApiError, isAuthTokenExpired} from '../utils/JMErrorHandler';
import JMRequestHelper from '../helpers/JMRequestHelper';
import {JMError, JMErrorCodes, JMErrorMessages} from '../utils/JMErrorCodes';
import {JMNetworkRequestConstants} from '../constants/JMNetworkRequestConstants';
import {JMApiLogger} from '../../models/JMApiLogger';
import networkService from '../../../../jiomart-common/src/JMNetworkConnectionUtility';
import axiosToCurl from '../utils/curl';
import {JMLogger} from '../../../../jiomart-common/src/utils/JMLogger';
import {JMTokenService} from './JMTokenService';
import {getPrefString} from '../../../../jiomart-common/src/JMAsyncStorageHelper';
import {
  AsyncStorageKeys,
  EventEmitterKeys,
} from '../../../../jiomart-common/src/JMConstants';
import {JMBAUUserApiEndpointKeys} from '../endpoints/JMApiEndpoints';
import {emitRNEvent} from '../../../../jiomart-common/src/Emitter';
import {JMDatabaseManager} from '../../db/JMDatabaseManager';

class JMApiClient {
  private _axios_instance: AxiosInstance;
  private static _instance: JMApiClient;

  private isRefreshing = false;
  private refreshTokenPromise: Promise<any> | null = null;
  private isGuestTokenApiCalling = false;
  private guestTokenApiPromise: Promise<any> | null = null;
  public tokenService: JMTokenService;

  public static get getInstance() {
    return this._instance || (this._instance = new this());
  }

  private constructor() {
    this._axios_instance = axios.create();
    this.tokenService = JMTokenService.getInstance();
    this.setupInterceptors();
  }

  public resetSharedInstanceData() {
    this.isRefreshing = false;
    this.refreshTokenPromise = null;
    this.isGuestTokenApiCalling = false;
    this.guestTokenApiPromise = null;
  }

  private setupInterceptors() {
    this._axios_instance.interceptors.request.use(
      async config => {
        this.getCurl(config);
        JMApiLogger.logRequest(config);
        return config;
      },
      error => {
        JMApiLogger.logError(error);
        return Promise.reject(handleApiError(error));
      },
    );

    this._axios_instance.interceptors.response.use(
      (response: AxiosResponse<any>) => {
        JMApiLogger.logResponse(response.data);
        return response.data;
      },
      error => {
        JMApiLogger.logError(error);
        return Promise.reject(handleApiError(error));
      },
    );
  }

  private getCurl(request: InternalAxiosRequestConfig) {
    try {
      const curlCommand = axiosToCurl(request);
      console.log('------------ CURL ------------');
      console.log(curlCommand);
      console.log('------------ END CURL ------------');
    } catch (error) {}
  }

  private async refreshToken(): Promise<string | JMError> {
    try {
      JMLogger.log('refreshToken api called');
      this.refreshTokenPromise = this.tokenService.callRefreshTokenApi();
      const response = await this.refreshTokenPromise;
      return response;
    } catch (error) {
      JMApiLogger.logError('refreshToken ' + error);
      throw error;
    }
  }

  private async guestTokenApi(): Promise<string | JMError> {
    try {
      this.guestTokenApiPromise = this.tokenService.callGuestTokenApi();
      const response = await this.guestTokenApiPromise;
      return response;
    } catch (error) {
      JMApiLogger.logError('guestToken ' + error);
      throw error;
    }
  }

  public async request<T, M>(
    request: JMRequestHelper<M>,
    retryCount: number = 0,
  ): Promise<T> {
    try {
      if (!networkService.getConnectionStatus()) {
        throw {
          code: JMErrorCodes.NETWORK_ERROR,
          message: JMErrorMessages.NETWORK_ERROR,
        } as JMError;
      }
      if (
        request?._request?.endpoint ===
          JMBAUUserApiEndpointKeys.GET_CRA_CUSTOMER_DETAILS &&
        this.isRefreshing
      ) {
        try {
          await this.refreshTokenPromise;
        } catch (error) {
          JMApiLogger.logError('refreshTokenPromise error ' + error);
          throw error;
        }
      }
      if (this.isGuestTokenApiCalling) {
        try {
          await this.guestTokenApiPromise;
        } catch (error) {
          JMApiLogger.logError('refreshTokenPromise error ' + error);
          throw error;
        }
      }

      await request.updateRequestDetails();
      return await this._axios_instance.request(request.build());
    } catch (error: any) {
      const sessionData = await getPrefString(
        AsyncStorageKeys.CRA_USER_SESSION_DATA,
      );
      if (
        isAuthTokenExpired(error) &&
        retryCount < JMNetworkRequestConstants.MAX_RETRIES + 1
      ) {
        const errorCode = error?.code;
        const errorMessage = error?.response?.errors?.[0]?.message;

        if (
          (errorCode === JMErrorCodes.TOKEN_EXPIRED ||
            errorMessage === 'Access Token Expired') &&
          request?._request?.endpoint ===
            JMBAUUserApiEndpointKeys.GET_CRA_CUSTOMER_DETAILS &&
          sessionData
        ) {
          if (!this.isRefreshing) {
            JMLogger.log(
              'refreshToken api called coming api request' +
                JSON.stringify(request),
            );
            this.isRefreshing = true;
            try {
              await this.refreshToken();
              this.isRefreshing = false;
              const response = await this.request<T, M>(
                request,
                retryCount + 1,
              );
              return response;
            } catch (err) {
              throw err; // Reject if token refresh fails
            }
          } else {
            JMLogger.log(
              'refreshToken api called of pending coming api request ' +
                JSON.stringify(request),
            );
            try {
              await this.refreshTokenPromise;
              JMLogger.log(
                'refreshToken api called of pending coming api request called ' +
                  JSON.stringify(request),
              );
              return this.request<T, M>(request, retryCount + 1);
            } catch (err) {
              throw err;
            }
          }
        } else if (
          (errorCode === JMErrorCodes.TOKEN_EXPIRED ||
            errorMessage === 'invalid merge session id') &&
          !JMDatabaseManager.user.isUserLoggedInFlag()
        ) {
          if (!this.isGuestTokenApiCalling) {
            this.isGuestTokenApiCalling = true;
            try {
              await this.guestTokenApi();
              this.isGuestTokenApiCalling = false;
              const response = await this.request<T, M>(
                request,
                retryCount + 1,
              );
              return response;
            } catch (err) {
              throw err;
            }
          } else {
            try {
              await this.guestTokenApiPromise;
              return this.request<T, M>(request, retryCount + 1);
            } catch (err) {
              throw err;
            }
          }
        } else if (
          (errorCode === JMErrorCodes.TOKEN_EXPIRED ||
            (errorCode === JMErrorCodes.BAD_REQUEST &&
              error?.response?.reason?.[0]?.reason_code === 'UNAUTHORIZED')) &&
          request?._request?.endpoint?.startsWith?.('/mst/') &&
          sessionData
        ) 
        {
          emitRNEvent(EventEmitterKeys.CLOSE, {logout: true});
        } else {
          throw error;
        }
      } else {
        throw error;
      }
    }
  }
}

export default JMApiClient;
