//
//  JMInfoPlist.swift
//  JioMart
//
//  Created by <PERSON><PERSON><PERSON> Sah on 26/04/25.
//

import Foundation

enum InfoPlist {
  static let googleMapKey = "GOOGLE_MAPS_API_KEY"
  static let environment = "Environment"
  static let appIdentifierKey = "AppIdentifier"
  static let appsflyerAppId = "AppsFlyerAppleId"
  static let appsflyerDevId = "AppsFlyerDevId"
  static let appsflyerDomain = "AppsFlyerDomainUrl"
  static let oneRetailDomain = "OneRetailDomain"
  static let oneRetailClientID = "OneRetailClientID"
  static let oneRetailReturnUIURL = "OneRetailReturnUIURL"
  static let cleverTapAccountID = "CleverTapAccountID"
  static let cleverTapToken = "CleverTapToken"
  static let xAppToken = "XAppToken"
  static let xomsApplicationID = "XOMSApplicationID"
  static let polygonAccountToken = "PolygonAccountToken"
}

@objc public class JMInfoPlist: NSObject {
  
    public static var infoPlistDictionary: [String: Any] = {
        guard let infoPlistDictionary = Bundle.main.infoDictionary else {
            fatalError("Plist file not found")
        }
        return infoPlistDictionary
    }()
    
    // MARK: - Plist values
    static let googleMapKey: String = {
      guard let value = JMInfoPlist.infoPlistDictionary[InfoPlist.googleMapKey] as? String else {
            return "AIzaSyCdP5Kx1XxidSUsy4vTArUFrfqGKsmPbh0"
        }
        return value
    }()

  static let appEnvironment: String = {
      guard let environment = JMInfoPlist.infoPlistDictionary[InfoPlist.environment] as? String else {
          return "PROD"
      }
      return environment
  }()
  
  static let appIdentifier: String = {
    guard let identity = JMInfoPlist.infoPlistDictionary[InfoPlist.appIdentifierKey] as? String else {
      return "com.jio.jiomart"
    }
    return identity;
  }()
  
  @objc public static func appsFlyerAppId() -> String {
    guard let identity = JMInfoPlist.infoPlistDictionary[InfoPlist.appsflyerAppId] as? String else {
      return "1522085683"
    }
    return identity;
  }
  
  @objc public static func appsflyerDevId() -> String {
    guard let identity = JMInfoPlist.infoPlistDictionary[InfoPlist.appsflyerDevId] as? String else {
      return "jGgetr8Ui6Vyyzdjmb5QkM"
    }
    return identity;
  }
  
  static let appsflyerDomain: String = {
    guard let identity = JMInfoPlist.infoPlistDictionary[InfoPlist.appsflyerDomain] as? String else {
      return "jiomart.onelink.me"
    }
    return identity;
  }()
  
  static let oneRetailDomain: String = {
      guard let domain = JMInfoPlist.infoPlistDictionary[InfoPlist.oneRetailDomain] as? String else {
          return "https://account.relianceretail.com"
      }
      return domain
  }()
  
  static let oneRetailClientID: String = {
      guard let clientID = JMInfoPlist.infoPlistDictionary[InfoPlist.oneRetailClientID] as? String else {
          return "fdb646ea-e708-4725-a953-228fa1cb8355"
      }
    return clientID
  }()
  
  static let oneRetailReturnUIURL: String = {
      guard let returnUIURL = JMInfoPlist.infoPlistDictionary[InfoPlist.oneRetailReturnUIURL] as? String else {
          return "www.jiomart.com/customer/account/login"
      }
      return returnUIURL
  }()
  
  static let cleverTapAccountID: String = {
      guard let cleverTapAccountID = JMInfoPlist.infoPlistDictionary[InfoPlist.cleverTapAccountID] as? String else {
          return "88R-W4Z-495Z"
      }
      return cleverTapAccountID
  }()
  
  static let cleverTapToken: String = {
      guard let cleverTapToken = JMInfoPlist.infoPlistDictionary[InfoPlist.cleverTapToken] as? String else {
          return "140-2bb"
      }
      return cleverTapToken
  }()
  
  static let xAppToken: String = {
      guard let xAppToken = JMInfoPlist.infoPlistDictionary[InfoPlist.xAppToken] as? String else {
          return "5ea6821b3425bb07c82a25c1"
      }
      return xAppToken
  }()
  
  static let xomsApplicationID: String = {
      guard let xomsApplicationID = JMInfoPlist.infoPlistDictionary[InfoPlist.xomsApplicationID] as? String else {
          return "qO2p_wQkq"
      }
      return xomsApplicationID
  }()
  
  static let polygonAccountToken: String = {
      guard let polygonAccountToken = JMInfoPlist.infoPlistDictionary[InfoPlist.polygonAccountToken] as? String else {
          return "84abe8b4-1ba7-4c71-a28b-5828c9eb3d54"
      }
      return polygonAccountToken
  }()
}
