package com.jcpjiomart

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.os.PersistableBundle
import android.util.Log
import com.facebook.react.ReactActivity
import com.facebook.react.ReactActivityDelegate
import com.facebook.react.defaults.DefaultNewArchitectureEntryPoint.fabricEnabled
import com.facebook.react.defaults.DefaultReactActivityDelegate
import com.jcpjiomart.react_packages.JMBridgeEventEmitterModule
import com.jio.codepush.BundleUpdater
import com.jiomart.module.JMPhoneNumberHintModule

class MainActivity : ReactActivity() {

  /**
   * Returns the name of the main component registered from JavaScript. This is used to schedule
   * rendering of the component.
   */
  override fun getMainComponentName(): String = "jcpJiomart"

  /**
   * Returns the instance of the [ReactActivityDelegate]. We use [DefaultReactActivityDelegate]
   * which allows you to enable New Architecture with a single boolean flags [fabricEnabled]
   */
    override fun createReactActivityDelegate(): ReactActivityDelegate {
        return object : ReactActivityDelegate(this, mainComponentName) {
            override fun getLaunchOptions(): Bundle {
                val intent: Intent = intent
                val env = intent.getStringExtra("env") ?: ""
                val directDeeplink = intent.getStringExtra("directDeeplink") ?: ""
                val screenId = intent.getStringExtra("SCREEN_ID") ?: ""
                val deeplink = intent.getStringExtra("deeplink") ?: ""
                val mobileNumber = intent.getStringExtra("mobileNumber") ?: ""
                val appName = intent.getStringExtra("appName") ?: ""
                val source = intent.getStringExtra("source") ?: ""
                val mUri = handleDeepLink(intent, false)
                val paramsBundle = Bundle()
                paramsBundle.putString("env", env)
                paramsBundle.putString("directDeeplink", directDeeplink)
                paramsBundle.putString("deeplink", deeplink)
                paramsBundle.putString("appName", appName)
                paramsBundle.putString("mobileNumber", mobileNumber)
                paramsBundle.putString("source", source)
                val launchOptions = Bundle()
                launchOptions.putString("SCREEN_ID", screenId)
                launchOptions.putString("env", Utility.getCurrentEnv())
                launchOptions.putString("appName", appName)
                launchOptions.putString("directDeeplink", directDeeplink)
                launchOptions.putString("deeplink", deeplink)
                launchOptions.putString("mUri", mUri)
//                launchOptions.putString("payload", payload)
                launchOptions.putString("mobileNumber", mobileNumber)
                launchOptions.putString("source", source)
                launchOptions.putBundle("BUNDLE", paramsBundle)
                launchOptions.putBoolean("UPDATE_BUNDLE", BundleUpdater.getUpdatePendingStatus() || BundleUpdater.getUpdateIgnoreStatus())
                return launchOptions
            }
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        setIntent(intent)
        handleDeepLink(intent)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
          JMPhoneNumberHintModule.registerPhoneNumberLauncher(this)
           if (!BuildConfig.DEBUG) {
                    BundleUpdater.sync()
           }
    }

    private fun handleDeepLink(intent: Intent?, callEmitter: Boolean = true) : String{
        var mUri : Uri? = null
        var payload = ""
        Log.d("handleDeepLink ", "intent $intent")
        if (intent?.data != null) {
            if (intent.action == Intent.ACTION_VIEW && intent.data != null && intent.data?.toString()?.contains("jiomart.onelink.me") == false) {
                mUri = intent.data
            }
        }
        if(callEmitter && mUri?.toString()?.isEmpty() == false){
            val eventEmitter = JMBridgeEventEmitterModule.getInstance()
            eventEmitter?.deeplink(mUri?.toString() ?: "", payload)
        }
        return mUri?.toString() ?: ""
    }
}
