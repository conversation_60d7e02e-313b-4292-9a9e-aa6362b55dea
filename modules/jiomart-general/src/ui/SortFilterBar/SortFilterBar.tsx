import { JioI<PERSON>, JioText } from '@jio/rn_components';
import { IconColor, IconKind, IconSize, JioTypography } from '@jio/rn_components/src/index.types';
import React from 'react';
import {
    Pressable,
    TouchableOpacity,
    View
} from 'react-native';
import { styles } from './styles/SortFilterBarStyle';

interface SortFilterBarProps {
    category: string,
    sortApplied: number;
    filterApplied: number;
    isCategoryClickable: boolean;
    backgroundColor?: string;
    offsetState: 'Start' | 'Move' | 'Finish';
    disableBar: boolean;
    onLeftPress: () => void;
    onSortPress: () => void;
    onFilterPress: () => void;
}

const SortFilterBar: React.FC<SortFilterBarProps> = ({
    category,
    sortApplied,
    filterApplied,
    isCategoryClickable,
    backgroundColor = '#fff',
    offsetState,
    disableBar,
    onLeftPress,
    onSortPress,
    onFilterPress,
}) => {
    // const offset = useMemo(() => new Animated.Value(0), []);

    // useEffect(() => {
    //     const toValue = offsetState === 'Start' ? 0 : offsetState === 'Move' ? -20 : 16;
    //     Animated.timing(offset, {
    //         toValue,
    //         duration: 700,
    //         useNativeDriver: true,
    //     }).start();
    // }, [offsetState]);

    return (
        <View>
            <View style={[styles.container, { backgroundColor, /*transform: [{ translateY: offset }]*/ }]}>
                <TouchableOpacity
                    style={styles.leftSection}
                    onPress={() => {
                        if (isCategoryClickable && !disableBar) {
                            onLeftPress();
                        }
                    }}
                    disabled={!isCategoryClickable || disableBar}
                >
                    <JioText
                        text={category}
                        color={'primary_60'}
                        appearance={JioTypography.BODY_XXS_BOLD}
                        maxLines={1}
                        style={styles.leftText}
                    />
                    {isCategoryClickable && <JioIcon ic={'IcChevronDown'} size={IconSize.SMALL} kind={IconKind.DEFAULT} color={IconColor.GREY100} isClickable={false} />}
                </TouchableOpacity>

                <View style={styles.rightSection}>
                    <BadgeButton text="Sort" count={sortApplied} icon={'IcSort'} showDot onPress={onSortPress} disabled={disableBar} />
                    <BadgeButton text="Filter" count={filterApplied} icon={'IcFilterMultiple'} onPress={onFilterPress} disabled={disableBar} />
                </View>

            </View>
            {disableBar && <ScrimOverlay />}
        </View>

    );
};

const BadgeButton = ({ text, count, icon, showDot = false, onPress, disabled = false }: any) => (
    <TouchableOpacity 
        style={[styles.badgeButton, disabled && styles.badgeButtonDisabled]} 
        onPress={onPress}
        disabled={disabled}
    >
        <JioIcon ic={icon} size={IconSize.SMALL} kind={IconKind.DEFAULT} color={IconColor.PRIMARY} isClickable={false} />
        <JioText
            text={text}
            color={'primary_80'}
            appearance={JioTypography.BODY_XXS_BOLD}
            maxLines={1}
            style={styles.badgeButtonText}
        />
        {count > 0 && (
            <View style={[styles.badge, showDot ? styles.dot : styles.circle]}>
                {!showDot && <JioText
                    text={count}
                    color={'white'}
                    appearance={JioTypography.BODY_XXS}
                    maxLines={1}
                    style={styles.badgeText}
                />
                }
            </View>
        )}
    </TouchableOpacity>
);

const ScrimOverlay = () => (
    <Pressable style={styles.scrim} pointerEvents="auto" onPress={() => { }} />
);

export default SortFilterBar;
