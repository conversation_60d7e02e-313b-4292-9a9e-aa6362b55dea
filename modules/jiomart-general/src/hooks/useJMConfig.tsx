import {useQueries, useQuery} from '@tanstack/react-query';
import {
  callVersionFileAsync,
  getConfigFileDataAsync,
} from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileManager';
import {JMConfigFileName} from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import {getAssetConfigFileData} from '../../../jiomart-networkmanager/src/JMConfigFileManager/utils/JMAssetConfigFileUtility';

// 1. App Startup Hook (fetches multiple config files)
export const useAppStartupConfig = (configFileKeys: JMConfigFileName[]) => {
  const config = useQueries({
    queries: configFileKeys.map(key => ({
      queryKey: [key],
      queryFn: () => getConfigFileDataAsync(key),
      staleTime: 0,
      gcTime: Infinity,
      initialData: () => getAssetConfigFileData(key),
      placeholderData: (previousData: any) => previousData,
    })),
  });

  let mapConfig = {};

  for (let i = 0; i < configFileKeys?.length; i++) {
    mapConfig = {...mapConfig, ...{[configFileKeys[i]]: config[i].data}};
  }

  return mapConfig;
};

// 2. Per-Screen Config Fetch (fetches a single config file)
export const useConfigFile = (key: JMConfigFileName, enabled = true) => {
  return useQuery({
    queryKey: [key],
    queryFn: () => getAssetConfigFileData(key),
    enabled,
    staleTime: 0,
    gcTime: Infinity,
    initialData: () => getAssetConfigFileData(key),
    placeholderData: (previousData, _) => previousData,
  })?.data;
};
