import React, {useCallback, useEffect, useState, useMemo, useRef} from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  NativeSyntheticEvent,
  NativeScrollEvent,
  Pressable,
} from 'react-native';
import {navigateTo} from '../../../jiomart-general/src/navigation/JMNavGraph';
import {
  navBeanObj,
  NavigationType,
} from '../../../jiomart-common/src/JMNavGraphUtil';
import {IconColor, JioTypography} from '@jio/rn_components/src/index.types';
import {SliderDetails} from '../../../jiomart-home/src/utils/HomePageTypes';
import JioText from '@jio/rn_components/src/components/JioText/JioText';
import {JMSliderTypeName} from '../../../jiomart-home/src/types/JMHomeComponentType';
import Video from 'react-native-video';
import {getBaseURL} from '../../../jiomart-networkmanager/src/JMEnvironmentConfig';
import {getImageHeight} from '../../../jiomart-common/src/utils/JMImageUtility';
import {JioIcon} from '@jio/rn_components';
import FastImage from 'react-native-fast-image';

interface PortraitCarouselProps {
  data: SliderDetails[];
  title: string;
  borderRadius: number;
  showIndicator: boolean;
  autoScrollTimer?: number;
  navigation: any;
  padding?: number;
  peekValue?: number;
  section_id?: any;
}

const {width: screenWidth} = Dimensions.get('window');

function useCarouselHeight(data: any[], padding: number) {
  const [carouselHeight, setCarouselHeight] = useState<number>(200);

  useEffect(() => {
    if (!data || data.length === 0) return;

    const calculate = async () => {
      const heightPromises = data.map(async item => {
        try {
          if (item.slide_type_name === JMSliderTypeName.VIDEO && item.image) {
            return await getImageHeight(item.image);
          }
        } catch (err) {
          console.warn('Error calculating height:', err);
        }
        return 200; // Default height
      });

      const heights = await Promise.all(heightPromises);
      const validHeights = heights.filter(h => h > 0);
      if (validHeights.length > 0) {
        setCarouselHeight(Math.max(...validHeights));
      }
    };

    calculate();
  }, [data]);

  return carouselHeight;
}

const PortraitVideoCarousel = ({
  data,
  title,
  borderRadius = 20,
  showIndicator = false,
  autoScrollTimer = 3000,
  navigation,
  padding = 0,
  peekValue = 0,
  section_id,
}: PortraitCarouselProps) => {
  const carouselHeight = useCarouselHeight(data, padding);
  const scrollViewRef = useRef<ScrollView>(null);

  // Video state management
  const [currentIndex, setCurrentIndex] = useState(0);
  const [mutedVideos, setMutedVideos] = useState<{[key: number]: boolean}>({});
  const [pausedVideos, setPausedVideos] = useState<{[key: number]: boolean}>(
    {},
  );
  const [playedOnceVideos, setPlayedOnceVideos] = useState<{
    [key: number]: boolean;
  }>({});
  const [forceRerenderKey, setForceRerenderKey] = useState(0);
  const hasTriggeredFirstRender = useRef(false);

  // Calculate item width for peek carousel
  const itemWidth = screenWidth - (peekValue > 0 ? peekValue + 20 : 0); // 20px for spacing
  const itemSpacing = 10;

  useEffect(() => {
    // Force re-render for first video after initialization
    if (!hasTriggeredFirstRender.current) {
      const timer = setTimeout(() => {
        console.log(`🔄 Force re-rendering first video for better playback`);
        setForceRerenderKey(prev => prev + 1);
        hasTriggeredFirstRender.current = true;
      }, 400); // Small delay to ensure component is mounted

      return () => clearTimeout(timer);
    }
  }, []);

  // Initialize all videos as muted and not paused
  useEffect(() => {
    const initialMutedState: {[key: number]: boolean} = {};
    const initialPausedState: {[key: number]: boolean} = {};
    const initialPlayedOnceState: {[key: number]: boolean} = {};
    data.forEach((_, index) => {
      initialMutedState[index] = true;
      initialPausedState[index] = false; // Videos start playing
      initialPlayedOnceState[index] = false; // Videos haven't played yet
    });
    setMutedVideos(initialMutedState);
    setPausedVideos(initialPausedState);
    setPlayedOnceVideos(initialPlayedOnceState);
    console.log(`🔇 Initialized ${data.length} videos as muted and playing`);
  }, [data]);

  // Debug current index changes
  useEffect(() => {
    console.log(`🎯 Current video index: ${currentIndex}`);
  }, [currentIndex]);

  // Ensure first video gets proper initialization
  useEffect(() => {
    if (currentIndex === 0 && forceRerenderKey > 0) {
      console.log(`🎬 First video re-render completed, should be playing now`);
    }
  }, [currentIndex, forceRerenderKey]);

  const handlePress = useCallback(
    (url: string) => {
      console.log('Pressed URL:', url);
      navigateTo(
        navBeanObj({
          source: '',
          destination: 'CommonWebViewScreen',
          actionType: 'T003',
          headerType: 9,
          headerVisibility: 2,
          navigationType: NavigationType.PUSH,
          actionUrl: url,
          loginRequired: false,
        }),
        navigation,
      );
    },
    [navigation],
  );

  // Real-time scroll tracking for instant indicator updates
  const handleScroll = useCallback(
    (event: NativeSyntheticEvent<NativeScrollEvent>) => {
      const offsetX = event.nativeEvent.contentOffset.x;

      // Calculate index based on whether we have peek value or not
      let newIndex;
      if (peekValue > 0) {
        // With peek, account for spacing and centering
        newIndex = Math.round(offsetX / (itemWidth + itemSpacing));
      } else {
        // Without peek, simple calculation
        newIndex = Math.round(offsetX / itemWidth);
      }

      // Ensure index is within bounds
      const videoCount = data.filter(
        item => item.slide_type_name === JMSliderTypeName.VIDEO,
      ).length;
      newIndex = Math.max(0, Math.min(newIndex, videoCount - 1));

      if (newIndex !== currentIndex) {
        console.log(
          `⚡ Real-time scroll update: ${currentIndex} → ${newIndex} (offsetX: ${offsetX})`,
        );
        setCurrentIndex(newIndex);
      }
    },
    [currentIndex, itemWidth, peekValue, data],
  );

  // Handle scroll end for final position confirmation
  const handleScrollEnd = useCallback(
    (event: NativeSyntheticEvent<NativeScrollEvent>) => {
      const offsetX = event.nativeEvent.contentOffset.x;

      // Calculate index based on whether we have peek value or not
      let newIndex;
      if (peekValue > 0) {
        // With peek, account for spacing and centering
        newIndex = Math.round(offsetX / (itemWidth + itemSpacing));
      } else {
        // Without peek, simple calculation
        newIndex = Math.round(offsetX / itemWidth);
      }

      // Ensure index is within bounds
      const videoCount = data.filter(
        item => item.slide_type_name === JMSliderTypeName.VIDEO,
      ).length;
      newIndex = Math.max(0, Math.min(newIndex, videoCount - 1));

      if (newIndex !== currentIndex) {
        console.log(
          `🔄 Scroll ended, final index: ${newIndex} (offsetX: ${offsetX})`,
        );
        setCurrentIndex(newIndex);
      }
    },
    [currentIndex, itemWidth, peekValue, data],
  );

  // Toggle mute for current video
  const toggleMute = useCallback((videoIndex: number) => {
    console.log(`🔊 Toggling mute for video ${videoIndex}`);
    setMutedVideos(prev => ({
      ...prev,
      [videoIndex]: !prev[videoIndex],
    }));
  }, []);

  // Toggle pause/play for current video
  const togglePlayPause = useCallback((videoIndex: number) => {
    console.log(`⏯️ Toggling play/pause for video ${videoIndex}`);
    setPausedVideos(prev => ({
      ...prev,
      [videoIndex]: !prev[videoIndex],
    }));
  }, []);

  // Manual index change (when user taps play button)
  const changeToIndex = useCallback(
    (index: number) => {
      console.log(`🎬 Manual change to index ${index}`);
      setCurrentIndex(index);

      // Calculate proper scroll position based on peek value
      let scrollX;
      if (peekValue > 0) {
        // With peek, scroll to center the item with spacing
        scrollX = index * (itemWidth + itemSpacing);
      } else {
        // Without peek, simple calculation
        scrollX = index * itemWidth;
      }

      scrollViewRef.current?.scrollTo({
        x: Math.max(0, scrollX),
        animated: true,
      });
    },
    [itemWidth, peekValue],
  );

  const renderVideoItem = useCallback(
    (item: SliderDetails, index: number) => {
      if (item.slide_type_name !== JMSliderTypeName.VIDEO) {
        return null;
      }

      const isCurrentVideo = index === currentIndex;
      const isMuted = mutedVideos[index] ?? true;
      const isManuallyPaused = pausedVideos[index] ?? false;
      const hasPlayedOnce = playedOnceVideos[index] ?? false;
      const shouldPause = !isCurrentVideo || isManuallyPaused;
      const showThumbnail =
        !hasPlayedOnce || (hasPlayedOnce && isManuallyPaused);

      console.log(`🎥 Rendering video ${index}:`, {
        isCurrentVideo,
        isMuted,
        isManuallyPaused,
        hasPlayedOnce,
        showThumbnail,
        currentIndex,
        videoFile: item.video_file,
        paused: shouldPause,
      });

      // Generate video URL
      const videoUrl = item.video_file
        ? item.video_file.startsWith('http://') ||
          item.video_file.startsWith('https://')
          ? item.video_file
          : `${getBaseURL()}/images/cms/aw_rbslider/slides/videos/${
              item.video_file
            }`
        : null;

      console.log(`🔗 Video URL for index ${index}:`, videoUrl);

      if (!videoUrl) {
        console.log(`❌ No video URL for index ${index}`);
        return (
          <View
            style={[
              styles.videoContainer,
              {
                width: itemWidth,
                borderRadius: borderRadius,
                height: carouselHeight,
                backgroundColor: '#f0f0f0',
                alignItems: 'center',
                justifyContent: 'center',
              },
            ]}>
            <JioText
              text="Video not available"
              appearance={JioTypography.BODY_S}
            />
          </View>
        );
      }

      return (
        <View
          key={`video-${index}`}
          style={[
            styles.videoContainer,
            {
              width: itemWidth,
              borderRadius: borderRadius,
              height: carouselHeight,
              marginRight: peekValue > 0 ? itemSpacing : 0,
            },
          ]}>
          <Video
            key={`${section_id}-${index}-${index === 0 ? forceRerenderKey : 0}`}
            source={{uri: videoUrl}}
            onEnd={() => {
              if (index < data.length - 1) {
                changeToIndex(index + 1);
              } else {
                changeToIndex(0);
              }
            }}
            style={styles.video}
            resizeMode="cover"
            controls={false}
            paused={shouldPause}
            repeat={true}
            muted={isMuted}
            playInBackground={false}
            playWhenInactive={false}
            ignoreSilentSwitch="ignore"
            mixWithOthers="duck"
            progressUpdateInterval={1000}
            bufferConfig={{
              minBufferMs: 15000,
              maxBufferMs: 50000,
              bufferForPlaybackMs: 2500,
              bufferForPlaybackAfterRebufferMs: 5000,
            }}
            onError={e => {
              console.log(`❌ Video error for index ${index}:`, e);
            }}
            onLoad={data => {
              console.log(`✅ Video loaded for index ${index}:`, {
                duration: data.duration,
                naturalSize: data.naturalSize,
              });
            }}
            onLoadStart={() => {
              console.log(`🔄 Video load started for index ${index}`);
              // Hide thumbnail as soon as video starts loading if it's the current video
              if (isCurrentVideo && !shouldPause && !hasPlayedOnce) {
                console.log(
                  `⚡ Video ${index} loading - hiding thumbnail early`,
                );
                setPlayedOnceVideos(prev => ({
                  ...prev,
                  [index]: true,
                }));
              }
            }}
            onBuffer={({isBuffering}) =>
              console.log(`📡 Video buffering for index ${index}:`, isBuffering)
            }
            onProgress={progress => {
              if (index === currentIndex && !shouldPause) {
                // Mark video as played once when it starts progressing (faster threshold)
                if (!hasPlayedOnce && progress.currentTime > 0.01) {
                  console.log(
                    `🎬 Video ${index} started playing - hiding thumbnail`,
                  );
                  setPlayedOnceVideos(prev => ({
                    ...prev,
                    [index]: true,
                  }));
                }
                console.log(
                  `⏯️ Video progress for index ${index}:`,
                  progress.currentTime.toFixed(1),
                );
              }
            }}
            onReadyForDisplay={() => {
              console.log(
                `🎬 Video ready for display for index ${index}, isCurrentVideo: ${isCurrentVideo}`,
              );
              // Hide thumbnail immediately when video is ready to display and should be playing
              if (isCurrentVideo && !shouldPause && !hasPlayedOnce) {
                console.log(
                  `🚀 Video ${index} ready - hiding thumbnail immediately`,
                );
                setPlayedOnceVideos(prev => ({
                  ...prev,
                  [index]: true,
                }));
              }
            }}
            onPlaybackStateChanged={({isPlaying}) => {
              console.log(
                `▶️ Video ${index} playback state: isPlaying=${isPlaying}, isCurrentVideo=${isCurrentVideo}`,
              );
            }}
          />

          {/* Thumbnail overlay */}
          {showThumbnail && item.image && (
            <FastImage
              source={{
                uri: `${item.image}`,
                priority: FastImage.priority.normal,
              }}
              style={styles.thumbnail}
              resizeMode={FastImage.resizeMode.cover}
            />
          )}

          {/* Invisible overlay for touch handling */}
          <Pressable
            style={styles.videoOverlay}
            onPress={() => {
              console.log(`🎬 Video ${index} play/pause clicked!`);
              togglePlayPause(index);
            }}
          />
          <Pressable
            style={styles.onPressOverlay}
            onPress={() => {
              console.log(`🎬 Video ${index} clicked!`);
              item.url ? handlePress(item.url) : null;
            }}
          />

          {/* Mute/Unmute Button */}
          {isCurrentVideo && (
            <TouchableOpacity
              style={styles.muteButton}
              onPress={() => toggleMute(index)}
              activeOpacity={0.7}>
              <View style={styles.muteButtonBackground}>
                {isMuted ? (
                  <View style={styles.muteIndicator}>
                    <JioIcon ic="IcSoundDisabled" color={IconColor.INVERSE} />
                  </View>
                ) : (
                  <View style={styles.muteIndicator}>
                    <JioIcon ic="IcSound" color={IconColor.INVERSE} />
                  </View>
                )}
              </View>
            </TouchableOpacity>
          )}

          {/* Play indicator for non-current videos or paused current video */}
          {(!isCurrentVideo || (isCurrentVideo && isManuallyPaused)) && (
            <TouchableOpacity
              style={styles.playIndicator}
              onPress={() => {
                if (!isCurrentVideo) {
                  changeToIndex(index);
                } else {
                  togglePlayPause(index);
                }
              }}
              activeOpacity={0.7}>
              <View style={styles.playButtonBackground}>
                <JioIcon ic="IcPlay" color={IconColor.INVERSE} />
              </View>
            </TouchableOpacity>
          )}
        </View>
      );
    },
    [
      currentIndex,
      mutedVideos,
      pausedVideos,
      playedOnceVideos,
      borderRadius,
      carouselHeight,
      itemWidth,
      toggleMute,
      togglePlayPause,
      changeToIndex,
    ],
  );

  // Filter only video items
  const videoItems = useMemo(() => {
    return data.filter(item => item.slide_type_name === JMSliderTypeName.VIDEO);
  }, [data]);

  console.log(`📊 Video items count: ${videoItems.length}`);

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <JioText
          text={title}
          color="black"
          appearance={JioTypography.HEADING_XXS}
          style={styles.title}
        />
      </View>

      <ScrollView
        ref={scrollViewRef}
        horizontal
        pagingEnabled={peekValue === 0}
        showsHorizontalScrollIndicator={false}
        decelerationRate="fast"
        snapToInterval={peekValue > 0 ? itemWidth + itemSpacing : undefined}
        snapToAlignment="start"
        contentContainerStyle={
          peekValue > 0
            ? {paddingLeft: peekValue / 2, paddingRight: peekValue}
            : undefined
        }
        onScroll={handleScroll}
        onMomentumScrollEnd={handleScrollEnd}
        scrollEventThrottle={16}
        style={{height: carouselHeight}}>
        {videoItems.map((item, index) => renderVideoItem(item, index))}
      </ScrollView>

      {/* Indicators */}
      {showIndicator && videoItems.length > 1 && (
        <View style={styles.indicatorContainer}>
          {videoItems.map((_, index) => (
            <TouchableOpacity
              key={index}
              onPress={() => changeToIndex(index)}
              style={[
                styles.indicator,
                index === currentIndex && styles.activeIndicator,
              ]}
            />
          ))}
        </View>
      )}
    </View>
  );
};

export default React.memo(PortraitVideoCarousel);

const styles = StyleSheet.create({
  container: {
    // height will be set dynamically
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignContent: 'center',
  },
  title: {
    paddingLeft: 16,
    flex: 1,
    paddingBottom: 8,
  },
  videoContainer: {
    position: 'relative',
    overflow: 'hidden',
    backgroundColor: '#000000',
  },
  video: {
    width: '100%',
    height: '100%',
    backgroundColor: '#000000',
  },
  thumbnail: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 2,
  },
  onPressOverlay: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    zIndex: 3,
  },
  videoOverlay: {
    position: 'absolute',
    width: '40%',
    height: '30%',
    top: '35%',
    left: '30%',
    zIndex: 5,
  },
  muteButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    zIndex: 6,
    padding: 8,
  },
  muteButtonBackground: {
    borderRadius: 20,
    padding: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  muteIndicator: {
    position: 'absolute',
    top: -2,
    right: -2,
  },
  playIndicator: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{translateX: -20}, {translateY: -20}],
    zIndex: 4,
  },
  playButtonBackground: {
    // backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 25,
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  indicatorContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 12,
    gap: 8,
  },
  indicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(12, 82, 115, 0.3)',
  },
  activeIndicator: {
    width: 24,
    backgroundColor: '#0C5273',
  },
});
