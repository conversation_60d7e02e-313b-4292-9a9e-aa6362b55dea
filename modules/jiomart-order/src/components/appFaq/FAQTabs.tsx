import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
} from 'react-native';
import Divider, { DividerGap, DividerType } from '../../../../jiomart-general/src/ui/Divider';

interface Tab {
  id: string;
  title: string;
}

interface FAQTabsProps {
  tabs: Tab[];
  activeTab: string;
  onTabPress: (tabId: string) => void;
  backgroundColor?: string;
  activeColor?: string;
  inactiveColor?: string;
}

const FAQTabs: React.FC<FAQTabsProps> = ({
  tabs,
  activeTab,
  onTabPress,
  backgroundColor = '#0078AD',
  activeColor = '#FFFFFF',
  inactiveColor = 'rgba(255, 255, 255, 0.7)',
}) => {
  return (
    <View style={[styles.container, { backgroundColor }]}>

      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.tabsContainer}
      >
        {tabs.map((tab, index) => (
          <TouchableOpacity
            key={tab.id}
            style={[
              styles.tab,
              index === 0 && styles.firstTab,
              index === tabs.length - 1 && styles.lastTab,
            ]}
            onPress={() => onTabPress(tab.id)}
            activeOpacity={0.7}
          >
            <Text
              style={[
                styles.tabText,
                {
                  color: activeColor,
                },
              ]}
            >
              {tab.title}
            </Text>
            {activeTab === tab.id && (
              <View style={[styles.activeIndicator, { backgroundColor: inactiveColor }]} />
            )}
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingTop: 9,
    paddingBottom: 1,
  },
  tabsContainer: {
    paddingHorizontal: 16,
  },
  tab: {
    paddingHorizontal: 12,
    paddingVertical: 10,
    marginRight: 16,
    position: 'relative',
  },
  firstTab: {
    marginLeft: 16,
  },
  lastTab: {
    marginRight: 16,
  },
  tabText: {
    fontFamily: 'Bold',
    fontSize: 17,
    bottom: 5,
    color: '#000000',
    fontWeight: 'bold',
  },
  activeIndicator: {
    position: 'absolute',
    top: 35,
    left: 5,
    right: 5,
    height: 5,
    borderRadius: 3,
  },
});

export default FAQTabs; 