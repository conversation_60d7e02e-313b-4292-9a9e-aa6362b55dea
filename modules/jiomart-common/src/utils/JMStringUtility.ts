export const capitalizeFirstLetter = (str: string) => {
  if (!str) {
    return '';
  }
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
};

export const isValidString = (value: any): boolean => {
  try {
    if (typeof value !== 'string') return false;

    const trimmed = value.trim().toLowerCase();
    return trimmed !== '' && trimmed !== 'null' && trimmed !== 'undefined';
  } catch (error) {
    return false;
  }
};

export const getValidString = (value: any): string => {
  try {
    if (typeof value !== 'string') return '';

    const trimmed = value.trim();
    const trimmerLowerCase = trimmed?.toLowerCase();
    return (trimmerLowerCase !== '' && trimmerLowerCase !== 'null' && trimmerLowerCase !== 'undefined') ? trimmed : ''
  } catch (error) {
    return '';
  }
};

export const splitStringIntoLowerCase = (
  value: any,
  separator: string = ',',
): string[] => {
  try {
    if (typeof value !== 'string') return [];

    const trimmed = value.trim().toLowerCase();
    return trimmed.split(separator).map(item => item.trim().toLowerCase());
  } catch (error) {
    return [];
  }
};

export const getItemByViewType = (viewType: string, helpFaqConfig: any) => {
  console.warn(
    'helpFaqConfig--',
    viewType + '||' + JSON.stringify(helpFaqConfig.needHelpScreen),
  );

  let result = null;

  helpFaqConfig.needHelpScreen.forEach((item: any) => {
    if (item.viewType === viewType) {
      switch (item.viewType) {
        case 'order_card':
          console.log('🔹 Order Card Title:', item.title);
          console.log('   Destination:', item.viewAllCta?.destination);
          result = item;
          break;

        case 'service_request':
          console.log('🔹 Service Request Title:', item.title);
          console.log('   Navigation Target:', item.viewAllCta?.destination);
          result = item;
          break;

        case 'quick_links':
          console.log('🔹 Quick Links Section:', item.title);
          item.items?.forEach(link => {
            console.log('   ▪️ Link Title:', link.title);
            console.log('      - Icon:', link.iconName);
            console.log('      - Destination:', link.cta?.destination);
            console.log('      - Nav Title:', link.cta?.navTitle);
          });
          result = item;
          break;

        case 'need_more_help':
          console.log('🔹 Need More Help Title:', item.title);
          console.log('   Destination:', item.viewAllCta?.destination);
          result = item;
          break;

        case 'connect_with_us':
          console.log('🔹 Connect With Us Section:', item.title);
          result = item;
        default:
          console.log('🔹 Unknown ViewType:', item.viewType);
      }
    }
  });
  return result;
};
