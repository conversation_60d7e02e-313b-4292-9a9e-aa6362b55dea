import React, { useState, useMemo, useCallback } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  RefreshControl,
  BackHandler,
  Alert,
} from 'react-native';
import {
  AppScreens,
  type ScreenProps,
} from '../../../jiomart-common/src/JMAppScreenEntry';
import { useFocusEffect } from '@react-navigation/native';

// Import our optimized components
import FAQHeader from '../components/appFaq/FAQHeader';
import FAQTabs from '../components/appFaq/FAQTabs';
import { FAQSection, FAQItem } from '../components/appFaq/FAQAccordion';
import { ContactSection, ServiceRequestSection } from '../components/appFaq/ContactSection';
import ScreenSlot, { DeeplinkHandler } from '../../../jiomart-general/src/ui/JMScreenSlot';
import useJMAppFAQScreenController from '../controllers/useJMAppFAQScreenController';
import { JMAppFAQScreenProps } from '../types/JMAppFAQScreenType.d';
import { useConfigFile } from '../../../jiomart-general/src/hooks/useJMConfig';
import { JMConfigFileName } from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import useTransitionState from '../../../jiomart-general/src/hooks/useTransitionState';
import { NavigationBeanType } from '../../../jiomart-general/src/ui/Header/JioMartHeader';
import { JMLogger } from '../../../jiomart-common/src/utils/JMLogger';
import { title } from 'node:process';
import { NavigationBean } from '../../../jiomart-common/src/JMNavGraphUtil';

interface Tab {
  id: string;
  title: string;
  faqTypes: number;
  order: number;
}

interface FAQCategory {
  id: string;
  title: string;
  items: FAQItem[];
}

interface ConfigFAQScreenItem {
  title: string;
  order: number;
  rnVersion?: string;
  iosVersion?: string;
  androidVersion?: string;
  faqTypes: number;
  faqType?: string;
  commonActionUrl?: string;
  body: ConfigBodyItem[];
}

interface ConfigBodyItem {
  viewType: string;
  rnVersion?: string;
  iosVersion?: string;
  androidVersion?: string;
  title?: string;
  iconUrl?: string;
  actionTAG?: string;
  commonActionUrl?: string;
  order?: number;
  faqs?: ConfigFAQItem[];
}

interface ConfigFAQItem {
  title: string;
  subTitle: string;
  order?: number;
  viewType: string;
  rnVersion?: string;
  iosVersion?: string;
  androidVersion?: string;
  more_icon?: string;
  less_icon?: string;
  type?: string;
  iconUrl?: string;
  cta : NavigationBean;
}

interface ContactActionBlock {
  title: string;
  body: ContactAction[];
}

interface ContactAction {
  title: string;
  subTitle?: string;
  iconUrl?: string;
  backgroundColor?: string;
  order?: number;
  cta? : NavigationBean;
}

interface HeaderConfig {
  header_type: number;
  title: string;
  analytics?: {
    firebase?: {
      header_event?: {
        back?: {
          action: string;
          label: string;
          header_page_type: string;
        };
        cart?: {
          action: string;
          label: string;
          header_page_type: string;
          screen: string;
        };
      };
    };
  };
}

const JMAppFAQScreen: React.FC<JMAppFAQScreenProps> = (props: JMAppFAQScreenProps) => {
  const config = useConfigFile(JMConfigFileName.JMAppFAQConfigurationFileName);
  const categoriesConfig = useConfigFile(JMConfigFileName.JMAllCategoriesConfigurationFileName);
  const [activeTab, setActiveTab] = useState('all');
  const [refreshing, setRefreshing] = useState(false);
  const { navigation, navigationBean } = useJMAppFAQScreenController(props);
  const isTransisitonComplete = useTransitionState();

  const [filterBtmSheet, setFilterFilterBtmSheet] = useState(false);

  const openFilterBtmSheet = () => {
    setFilterFilterBtmSheet(true);
  };
  const closeFilterBtmSheet = () => {
    setFilterFilterBtmSheet(false);
  };

  // Helper functions for contact actions
  const getIconForAction = (title: string): string => {
    const lowerTitle = title.toLowerCase();
    if (lowerTitle.includes('chat') && !lowerTitle.includes('whatsapp')) return '💬';
    if (lowerTitle.includes('request')) return '📝';
    if (lowerTitle.includes('call')) return '📞';
    if (lowerTitle.includes('whatsapp')) return '💬';
    return '📧';
  };

  const getBackgroundColorForAction = (title: string): string => {
    const lowerTitle = title.toLowerCase();
    if (lowerTitle.includes('chat') && !lowerTitle.includes('whatsapp')) return '#E3F2FD';
    if (lowerTitle.includes('request')) return '#E8F5E8';
    if (lowerTitle.includes('call')) return '#FFF3E0';
    if (lowerTitle.includes('whatsapp')) return '#E8F5E8';
    return '#F5F5F5';
  };

  const getTextColorForAction = (title: string): string => {
    const lowerTitle = title.toLowerCase();
    if (lowerTitle.includes('chat') && !lowerTitle.includes('whatsapp')) return '#1976D2';
    if (lowerTitle.includes('request')) return '#388E3C';
    if (lowerTitle.includes('call')) return '#F57C00';
    if (lowerTitle.includes('whatsapp')) return '#25D366';
    return '#333333';
  };

  // Analytics helper functions
  const logAnalyticsEvent = useCallback((eventType: string, action: string, label: string, additionalParams?: any) => {
    const analyticsData = {
      action,
      label,
      page_type: 'faq',
      screen: 'faq_screen',
      ...additionalParams
    };
    
    JMLogger.log('FAQ_Analytics', `${eventType}: ${JSON.stringify(analyticsData)}`);
    
    // Here you would integrate with your actual analytics service
    // Example: Analytics.track(eventType, analyticsData);
  }, []);

  // Get header configuration
  const headerConfig: HeaderConfig | null = useMemo(() => {
    return categoriesConfig?.jiomart_header || null;
  }, [categoriesConfig]);

  // Parse tabs from config - only items with faqTypes: 0 are category tabs
  const tabs: Tab[] = useMemo(() => {
    if (!config?.faqScreen) return [];
    
    return config.faqScreen
      .filter((item: ConfigFAQScreenItem) => item.faqTypes === 0)
      .sort((a: ConfigFAQScreenItem, b: ConfigFAQScreenItem) => a.order - b.order)
      .map((item: ConfigFAQScreenItem) => ({
        id: item.title.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, ''),
        title: item.title,
        faqTypes: item.faqTypes,
        order: item.order
      })).sort((a: Tab, b: Tab) => a.order - b.order);
  }, [config]);

  // Extract all FAQs from config structure
  const allFAQs: FAQItem[] = useMemo(() => {
    if (!config?.faqScreen) return [];
    
    const faqs: FAQItem[] = [];
    
    config.faqScreen.forEach((faqScreen: ConfigFAQScreenItem) => {
      faqScreen.body.forEach((bodyItem: ConfigBodyItem) => {
        if (bodyItem.faqs && bodyItem.faqs.length > 0) {
          bodyItem.faqs.forEach((faq: ConfigFAQItem, index: number) => {
            if (faq.viewType === 'faq' || faq.type === 'faq') {
              faqs.push({
                id: `${faqScreen.title.toLowerCase()}-faq-${index}`,
                question: faq.title,
                answer: faq.subTitle,
                category: faqScreen.title,
                order: faq.order || index,
                cta: faq.cta
              });
            }
          });
        }
      });
    });
    
    return faqs.sort((a, b) => (a.order || 0) - (b.order || 0));
  }, [config]);

  // Parse FAQ data by category
  const faqData: FAQCategory[] = useMemo(() => {
    if (!config?.faqScreen) return [];

    const categories: FAQCategory[] = [];

    config.faqScreen.forEach((faqScreen: ConfigFAQScreenItem) => {
      // Skip "All" category and non-category items
      if (faqScreen.title.toLowerCase() === 'all' || faqScreen.faqTypes !== 0) {
        return;
      }

      const categoryId = faqScreen.title.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');
      const faqItems: FAQItem[] = [];
      
      faqScreen.body.forEach((bodyItem: ConfigBodyItem) => {
        if (bodyItem.viewType === 'faq_view' && bodyItem.faqs) {
          bodyItem.faqs.forEach((faq: ConfigFAQItem, index: number) => {
            if (faq.viewType === 'faq' || faq.type === 'faq') {
              faqItems.push({
                id: `${categoryId}-faq-${index}`,
                question: faq.title,
                answer: faq.subTitle,
                category: faqScreen.title,
                order: faq.order || index,
                cta: faq.cta
              });
            }
          });
        }
      });

      if (faqItems.length > 0) {
        categories.push({
          id: categoryId,
          title: faqScreen.title,
          items: faqItems.sort((a, b) => (a.order || 0) - (b.order || 0)),
        });
      }
    });

    return categories;
  }, [config]);

  // Get contact actions from "connect With Us" section
  const contactActionBlock: ContactActionBlock = useMemo(() => {
    if (!config?.faqScreen) return { title: '', body: [] };
    
    const connectSection = config.faqScreen.find((item: ConfigFAQScreenItem) => 
      item.faqTypes === 1
    );
    if (!connectSection) return { title: '', body: [] };
    const contactBlock : ContactActionBlock = {
      title: connectSection.title,
      body: connectSection.body
        .filter((item: ConfigBodyItem) => item.viewType === 'connect_us')
        .flatMap((item: ConfigBodyItem) => 
          item.faqs?.map((faq: ConfigFAQItem) => ({
            title: faq.title,
            subTitle: faq.subTitle,
            iconUrl: faq.iconUrl,
            order: faq.order,
        
          })) || []
        )
    };
    return contactBlock;
  }, [config]); 

  const contactActions: ContactAction[] = useMemo(() => {
    if (!config?.faqScreen) return [];

    const connectSection = config.faqScreen.find((item: ConfigFAQScreenItem) => 
      item.faqTypes === 1
    );
    
    if (!connectSection) return [];

    const actions: ContactAction[] = [];
    
    connectSection.body.forEach((bodyItem: ConfigBodyItem) => {
      if (bodyItem.viewType === 'connect_us' && bodyItem.faqs) {
        bodyItem.faqs.forEach((faq: ConfigFAQItem) => {
          if (faq.viewType === 'action') {
            actions.push({
              title: faq.title,
              subTitle: faq.subTitle,
              iconUrl: faq.iconUrl,
              order: faq.order || 0,
            });
          }
        });
      }
    });

    return actions.sort((a, b) => (a.order || 0) - (b.order || 0));
  }, [config]);

  // Check if service request section should be shown
  const showServiceRequest = useMemo(() => {
    if (!config?.faqScreen) return false;
    
    return config.faqScreen.some((faqScreen: ConfigFAQScreenItem) => 
      faqScreen.body.some((bodyItem: ConfigBodyItem) => bodyItem.viewType === 'service_request')
    );
  }, [config]);

  // Filter FAQ data based on active tab
  const filteredFAQData = useMemo(() => {
    if (activeTab === 'all') {
      // For "All" tab, group all FAQs by category
      const groupedFAQs: FAQCategory[] = [];
      
      faqData.forEach(category => {
        groupedFAQs.push(category);
      });
      
      return groupedFAQs;
    }
    
    return faqData.filter(category => category.id === activeTab);
  }, [faqData, activeTab]);

  // Handle back button with analytics
  const handleBackPress = useCallback(() => {
    // Log analytics event for back navigation
    if (headerConfig?.analytics?.firebase?.header_event?.back) {
      const backEvent = headerConfig.analytics.firebase.header_event.back;
      logAnalyticsEvent('header_back_click', backEvent.action, backEvent.label, {
        header_page_type: 'faq'
      });
    }
    
    // In real app, use navigation.goBack()
    navigation.goBack();
    return true;
  }, [headerConfig, logAnalyticsEvent, navigation]);

  // Handle cart button with analytics
  const handleCartPress = useCallback(() => {
    // Log analytics event for cart navigation
    if (headerConfig?.analytics?.firebase?.header_event?.cart) {
      const cartEvent = headerConfig.analytics.firebase.header_event.cart;
      logAnalyticsEvent('header_cart_click', cartEvent.action, cartEvent.label, {
        header_page_type: 'faq',
        screen: 'faq_screen'
      });
    }
    
    Alert.alert('Cart', 'Navigating to cart...');
  }, [headerConfig, logAnalyticsEvent]);

  // Handle tab change with analytics
  const handleTabPress = useCallback((tabId: string) => {
    const selectedTab = tabs.find(tab => tab.id === tabId);
    
    // Log tab change analytics
    logAnalyticsEvent('faq_tab_click', 'tab_changed', `faq_tab_${tabId}`, {
      tab_name: selectedTab?.title,
      tab_type: selectedTab?.faqTypes
    });
    
    setActiveTab(tabId);
  }, [tabs, logAnalyticsEvent]);

  // Handle refresh
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    
    // Log refresh analytics
    logAnalyticsEvent('faq_refresh', 'pull_to_refresh', 'faq_refreshed');
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    setRefreshing(false);
  }, [logAnalyticsEvent]);

  // Handle contact actions based on config with analytics
  const handleContactAction = useCallback((action: ContactAction) => {
    // Log contact action analytics
    logAnalyticsEvent('faq_contact_action', action.title.toLowerCase().replace(/\s+/g, '_'), `contact_${action.title.toLowerCase().replace(/\s+/g, '_')}`, {
      action_type: action.title,
    });
    
    switch (action.title.toLowerCase()) {
      case 'chat with us':
        Alert.alert('Chat', 'Opening chat support...');
        break;
      case 'raise a request':
        Alert.alert('Request', 'Opening support request form...');
        break;
      case 'call us':
        Alert.alert('Call', `Calling customer support...`);
        break;
      case 'whatsapp chat':
        Alert.alert('WhatsApp', 'Opening WhatsApp chat...');
        break;
      default:
        Alert.alert('Action', `${action.title} pressed`);
    }
  }, [logAnalyticsEvent]);

  const handleViewAllRequests = useCallback(() => {
    // Log service requests analytics
    logAnalyticsEvent('faq_service_request', 'view_all_requests', 'service_requests_viewed');
    
    Alert.alert('Requests', 'Navigating to all service requests...');
  }, [logAnalyticsEvent]);

  // Handle FAQ item interaction with analytics
  const handleFAQItemPress = useCallback((questionId: string, question: string) => {
    logAnalyticsEvent('faq_item_interaction', 'faq_item_expanded', 'faq_item_clicked', {
      question_id: questionId,
      question_text: question,
      category: activeTab
    });
  }, [logAnalyticsEvent, activeTab]);

  // Handle hardware back button on Android
  useFocusEffect(
    useCallback(() => {
      const onBackPress = () => {
        return handleBackPress();
      };

      BackHandler.addEventListener('hardwareBackPress', onBackPress);
      return () => BackHandler.removeEventListener('hardwareBackPress', onBackPress);
    }, [handleBackPress])
  );

  // Log screen view analytics on mount
  useFocusEffect(
    useCallback(() => {
      logAnalyticsEvent('screen_view', 'faq_screen_viewed', 'faq_screen_loaded', {
        total_tabs: tabs.length,
        total_categories: faqData.length,
        total_faqs: allFAQs.length,
        has_contact_actions: contactActions.length > 0
      });
    }, [logAnalyticsEvent, tabs.length, faqData.length, allFAQs.length, contactActions.length])
  );

  JMLogger.log('JMAppFAQScreen', 'render config as $config' + JSON.stringify(config));
  JMLogger.log('JMAppFAQScreen', 'categories config as $categoriesConfig' + JSON.stringify(categoriesConfig));
  JMLogger.log('JMAppFAQScreen', 'parsed tabs: ' + JSON.stringify(tabs));
  JMLogger.log('JMAppFAQScreen', 'parsed faqData: ' + JSON.stringify(faqData));
  JMLogger.log('JMAppFAQScreen', 'parsed contactActions: ' + JSON.stringify(contactActionBlock));
  JMLogger.log('JMAppFAQScreen', 'currentTab: ' + activeTab);

  return (
    <DeeplinkHandler
      navigationBean={navigationBean}
      navigation={navigation}
      children={bean => (
        <ScreenSlot
          navigationBean={bean}
          navigation={navigation}
          header={{
            customFunctionality: {
              [NavigationBeanType.BACK]: {
                disableDefaultCall: true,
                onPress: handleBackPress,
              },
              [NavigationBeanType.CART]: {
                disableDefaultCall: true,
                onPress: handleCartPress,
              },
            },
          }}
          children={_ => (
            isTransisitonComplete ? (
              <View style={styles.container}>
                <FAQTabs
                  tabs={tabs}
                  activeTab={activeTab}
                  onTabPress={handleTabPress}
                />
                <ScrollView
                  style={styles.content}
                  showsVerticalScrollIndicator={false}
                  refreshControl={
                    <RefreshControl
                      refreshing={refreshing}
                      onRefresh={handleRefresh}
                      colors={['#0078AD']}
                      tintColor="#0078AD"
                    />
                  }
                >
                  {/* FAQ Sections */}
                  {filteredFAQData.map((category) => (
                    <FAQSection
                      key={category.id}
                      title={category.title}
                      items={category.items}
                    />
                  ))}

                  {/* Service Requests Section - only show if configured */}
                  {showServiceRequest && activeTab === 'all' && (
                    <ServiceRequestSection
                      title="Service Requests"
                      description="View and manage all your requests here."
                      buttonText="View all requests"
                      onViewAllPress={handleViewAllRequests}
                    />
                  )}

                  {/* Contact Section based on config */}
                  {contactActionBlock.body.length > 0 && activeTab === 'all' && (
                    <View style={styles.contactSection}>
                      <ContactSection
                        title={contactActionBlock.title}
                        options={contactActions.map(action => ({
                          id: action.title.toLowerCase().replace(/\s+/g, '-'),
                          title: action.title,
                          subtitle: action.subTitle,
                          icon: action.iconUrl || "",
                          action: () => handleContactAction(action),
                          cta: action.cta
                        }))}
                        onChatPress={() => {
                          const chatAction = contactActions.find(action => 
                            action.title.toLowerCase().includes('chat') && 
                            !action.title.toLowerCase().includes('whatsapp')
                          );
                          if (chatAction) handleContactAction(chatAction);
                        }}
                        onRaiseRequestPress={() => {
                          const requestAction = contactActions.find(action => 
                            action.title.toLowerCase().includes('request')
                          );
                          if (requestAction) handleContactAction(requestAction);
                        }}
                      />
                    </View>
                  )}

                  {/* Bottom padding for better scrolling */}
                  <View style={styles.bottomPadding} />
                </ScrollView>
              </View>
            ) : null
          )}
        />
      )}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  content: {
    flex: 1,
  },
  contactSection: {
    marginTop: 16,
  },
  bottomPadding: {
    height: 32,
  },
});

export default JMAppFAQScreen; 