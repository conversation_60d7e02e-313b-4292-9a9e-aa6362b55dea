import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import type {NavigationStackData} from '../../jiomart-common/src/JMNavGraphUtil';
import {AppScreens} from '../../jiomart-common/src/JMAppScreenEntry';
import JMOrderListScreen from './screens/JMOrderListScreen';
import JMRefundDetailScreen from './screens/JMRefundDetailScreen';
import JMAppFAQScreen from './screens/JMAppFAQScreen';
import {Platform} from 'react-native';
import {StackAnimationTypes} from 'react-native-screens';
import JMServiceScreen from './screens/JMServiceScreen';

const Stack = createNativeStackNavigator<NavigationStackData>();

const JMOrderNavigation = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        gestureEnabled: Platform.OS === 'ios' ? false : true,
      }}>
      <Stack.Screen
        name={AppScreens.ORDER_LIST_SCREEN}
        component={JMOrderListScreen}
        options={({route}) => {
          const navigationBean = route.params || {};
          return {
            animation:
              (navigationBean.animation as StackAnimationTypes) ||
              'slide_from_right',
          };
        }}
      />
      <Stack.Screen
        name={AppScreens.REFUND_DETAIL_SCREEN}
        component={JMRefundDetailScreen}
        options={({route}) => {
          const navigationBean = route.params || {};
          return {
            animation:
              (navigationBean.animation as StackAnimationTypes) ||
              'slide_from_right',
          };
        }}
      />
      <Stack.Screen
        name={AppScreens.APP_FAQ_SCREEN}
        component={JMAppFAQScreen}
      />
      <Stack.Screen
        name={AppScreens.SERVICE_SCREEN}
        component={JMServiceScreen}
        options={({route}) => {
          const navigationBean = route.params || {};
          return {
            animation:
              (navigationBean.animation as StackAnimationTypes) ||
              'slide_from_right',
          };
        }}
      />
    </Stack.Navigator>
  );
};

export default JMOrderNavigation;
