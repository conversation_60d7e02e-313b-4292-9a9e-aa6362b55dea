import React, {useEffect} from 'react';
import {JioText} from '@jio/rn_components';
import {JioTypography} from '@jio/rn_components/src/index.types';
import ScreenSlot, {
  DeeplinkHandler,
} from '../../../jiomart-general/src/ui/JMScreenSlot';
import {FlatList, Platform, View} from 'react-native';
import {Float} from 'react-native/Libraries/Types/CodegenTypes';
import AccountAccordianView from '../components/AccountAccordianView';
import ProfileView from '../components/AccountCard';
import AccountMenuCard from '../components/AccountMenuCard';
import AccountUserDetialLoadingShimmer from '../components/AccountUserDetialLoadingShimmer';
import useAccountScreenController from '../controllers/useAccountScreenController';
import {JMAccountScreenProps} from '../types/JMAccountScreenType';
import JMDeviceInfoData from '../../../jiomart-common/src/JMDeviceInfo';
import BottomSheet from '../../../jiomart-general/src/ui/BottomSheet/BottomSheet';
import JMSignOutBtmSheet from '../components/JMSignOutBtmSheet';

const Account = (props: JMAccountScreenProps) => {
  const {
    navigation,
    navigationBean,
    userDataResponse,
    myProfileConfig,
    loading,
    insets,
    redirectTo,
    notificationCount,
    signOutBtmSheet,
    closeSignOutBtmSheet,
    signOutUser,
  } = useAccountScreenController(props);

  const renderItem = ({item}: {item: any}) => {
    switch (item.type) {
      case 'AccountCard':
        if (loading) {
          return <AccountUserDetialLoadingShimmer />;
        }
        return (
          <ProfileView
            item={item}
            userData={userDataResponse}
            onPress={item => redirectTo(item)}
          />
        );
      case 'AccountMenuCard':
        return (
          <AccountMenuCard
            item={item}
            onPress={item => {
              redirectTo(item);
            }}
          />
        );
      case 'AccountAccordianView':
        return (
          <AccountAccordianView
            item={item.item}
            onPress={item => {
              redirectTo(item);
            }}
            notificationCount={notificationCount}
          />
        );
      case 'CloseAccountAccordianView':
        if (Platform.OS == 'ios') {
          return (
            <AccountAccordianView
              item={item.item}
              onPress={item => {
                redirectTo(item);
              }}
            />
          );
        }
        return null;
      case 'AppVersionView':
        return (
          <JioText
            text={
              `${item?.item.title} ${
                JMDeviceInfoData.getVersion() ?? '1.0'
              }` /*${route.params?.versionNumber}`*/
            }
            appearance={JioTypography.BODY_XS}
            style={{alignSelf: 'center', marginTop: 16, marginBottom: 40}}
            color="primary_grey_80"
          />
        );
      default:
        return null;
    }
  };

  var previousOffset: Float = 0.0;
  var previousPostion: string = '';

  const handleScroll = (event: any) => {
    const yOffset: Float = event.nativeEvent.contentOffset.y;
    if (yOffset > previousOffset && previousPostion != '1') {
      if (Platform.OS == 'ios') {
        // JMRNNavigatorModule.animateTabbarAsPerScrollPosition('1');
      }
      previousPostion = '1';
    } else if (yOffset < previousOffset && previousPostion != '0') {
      if (Platform.OS == 'ios') {
        // JMRNNavigatorModule.animateTabbarAsPerScrollPosition('0');
      }
      previousPostion = '0';
    }
    previousOffset = yOffset;
  };

  // const insets = useSafeAreaInsets();

  const bottomSheetContent = () => {
    return (
      <>
        <BottomSheet
          onBackDropClick={() => {
            closeSignOutBtmSheet();
          }}
          onDrag={() => {
            closeSignOutBtmSheet();
          }}
          visible={signOutBtmSheet}>
          <JMSignOutBtmSheet
            onCancel={closeSignOutBtmSheet}
            onSignOut={() => {
              signOutUser();
              closeSignOutBtmSheet();
            }}
          />
        </BottomSheet>
      </>
    );
  };

  return (
    <DeeplinkHandler
      navigationBean={navigationBean}
      navigation={navigation}
      children={bean => (
        <ScreenSlot
          navigationBean={bean}
          navigation={navigation}
          bottomSheetContent={bottomSheetContent}
          children={_ => {
            return (
              <View style={{backgroundColor: 'white', flex: 1}}>
                <FlatList
                  onScroll={handleScroll}
                  data={myProfileConfig?.myProfile?.[0]?.profileContent}
                  keyExtractor={(item, index) =>
                    item?.id ? item?.id?.toString() : String(index)
                  }
                  renderItem={renderItem}
                  showsVerticalScrollIndicator={false}
                  bounces={false}
                  style={{
                    paddingBottom: insets.bottom,
                  }}
                />
              </View>
            );
          }}
        />
      )}
    />
  );
};

export default Account;
