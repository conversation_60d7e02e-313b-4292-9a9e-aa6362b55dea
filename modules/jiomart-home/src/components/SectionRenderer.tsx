import React, {useState} from 'react';
import AllCatogeriesHome from '../../../jiomart-main/src/components/AllCatogeriesHome';
import GridCategoryCard from '../../../jiomart-main/src/components/GridCategoryCard';
import PortraitCarousel from '../../../jiomart-main/src/components/PortraitCarousel';
import {getBaseURL} from '../../../jiomart-networkmanager/src/JMEnvironmentConfig';
import BentoGridLayout from '../../../jiomart-main/src/components/BentoGridLayout';
import HeroTopCarousel from '../../../jiomart-main/src/components/HeroTopCarousel';
import SlimBanner from '../../../jiomart-main/src/components/SlimBanner';
import ShopByVericalBanner from '../../../jiomart-main/src/components/ShopByVerticalBanner';
import {WidgetWithProducts} from './WidgetWithProducts';
import {SECTION_DETAILS_IDENTIFIER} from '../utils/HomeSectionUtils';
import HorizontalProductListWithBackground from '../../../jiomart-main/src/components/HorizontalProductListWithBackground';
import {IconColor} from '@jio/rn_components/src/index.types';
import JioAds from '../../../jiomart-general/src/JioAds/JioAds';
import {extractJioAdsData, SPECIAL_IDENTIFIERS} from '../utils/HomeUtils';
import HorizontalScrollableCards from '../../../jiomart-main/src/components/HorizontalScrollableCards';
import LimitedProductsSection from './LimitedProductsSection';
import {JMDatabaseManager} from '../../../jiomart-networkmanager/src/db/JMDatabaseManager';
import SaledayCountdownTimer from '../../../jiomart-main/src/components/SaledayCountdownTimer';
import {JMSpecialSectionRenderer} from './JMSpecialSectionRenderer';
import LocalShopsListWidget from './LocalShopsListWidget';
import isEqual from 'lodash.isequal';
import PortraitVideoCarousel from '../../../jiomart-main/src/components/PortraitVideoCarousel';
import {JMSliderTypeName} from '../types/JMHomeComponentType';
import {rh, rw} from '../../../jiomart-common/src/JMResponsive';

interface SectionRendererProps {
  item: {data: any; section_details: any};
  onClick: any;
  onWidgetLoadingChange?: (loading: boolean) => void;
  navigation?: any;
  route?: any;
  adMetaData: any;
}

export const SectionRenderer: React.FC<SectionRendererProps> = ({
  item,
  onWidgetLoadingChange,
  navigation,
  route,
  onClick,
  adMetaData,
}) => {
  const [showLimitedSection, setShowLimitedSection] = useState<boolean>(true);
  if (
    Object.keys(SPECIAL_IDENTIFIERS).includes(item?.section_details?.identifier)
  ) {
    return (
      <JMSpecialSectionRenderer
        key={item?.section_details?.identifier}
        section={item}
        onWidgetLoadingChange={onWidgetLoadingChange}
        onClick={onClick}
      />
    );
  } else if (item?.section_details?.identifier === 'cart_items_ds2.0') {
    return (
      <WidgetWithProducts
        widget={{
          title: item?.section_details?.section_title || '',
          title_url: 'checkout/cart',
          title_type: 'cart_items',
        }}
        onClick={onClick}
        slug=""
        customStyles={{
          backgroundColor: '#E5F1F7',
          paddingTop: 30,
          paddingBottom: 30,
        }}
      />
    );
  } else if (item?.section_details?.identifier === 'category_tree_ds2.0') {
    return (
      <AllCatogeriesHome
        onClick={onClick}
        newplp={(() => {
          try {
            return JSON.parse(item?.section_details?.value)?.newplp;
          } catch {
            return undefined;
          }
        })()}
        l1={(() => {
          try {
            return JSON.parse(item?.section_details?.value)?.L1;
          } catch {
            return undefined;
          }
        })()}
        categoryType={item?.section_details?.section_vertical}
      />
    );
  } else if (
    item?.section_details?.identifier === '3X3_grid_card_ds2.0' ||
    item?.section_details?.identifier === 'inner_banners_3_5'
  ) {
    const bannerUrl = item?.section_details.section_bg_image
      ? item?.section_details.section_bg_image?.startsWith('https://') ||
        item?.section_details?.section_bg_image?.startsWith('http://')
        ? item?.section_details.section_bg_image
        : `${getBaseURL()}/images/cms/${item?.section_details.section_bg_image}`
      : undefined;
    return (
      <GridCategoryCard
        title={item?.section_details?.section_title}
        onClick={onClick}
        items={item?.data?.slider_details}
        banner={bannerUrl}
        paddingHorizontal={
          item?.section_details?.design_type.includes('grid') ? 0 : 0
        }
        imageGap={item?.section_details?.design_type.includes('grid') ? 0 : 10}
        imageAspectRatio={
          item?.section_details?.identifier === 'inner_banners_3_5'
            ? 1.255
            : 1.5
        }
        columns={3}
        imageMarginTop={0}
        imageMarginBottom={0}
        margin={10}
        paddingTop={0}
        paddingBottom={0}
        imageBorderRadius={
          item?.section_details?.design_type.includes('grid') ? 0 : 10
        }
        sliceToFullRows={true}
      />
    );
  } else if (item?.section_details?.identifier === '2X2_grid_card_ds2.0') {
    const bannerUrl = item?.section_details.section_bg_image
      ? item?.section_details.section_bg_image?.startsWith('https://') ||
        item?.section_details?.section_bg_image?.startsWith('http://')
        ? item?.section_details.section_bg_image
        : `${getBaseURL()}/images/cms/${item?.section_details.section_bg_image}`
      : undefined;
    return (
      <GridCategoryCard
        title={item?.section_details?.section_title}
        onClick={onClick}
        items={item?.data?.slider_details}
        banner={bannerUrl}
        paddingHorizontal={
          item?.section_details?.design_type.includes('grid') ? 0 : 0
        }
        imageGap={item?.section_details?.design_type.includes('grid') ? 0 : 10}
        imageBorderRadius={
          item?.section_details?.design_type.includes('grid') ? 0 : 16
        }
        margin={16}
        borderRadius={16}
        imageAspectRatio={1}
        columns={2}
        bannerHeight={100}
        bannerCornerRounded={true}
        showGridTitle={false}
        imageMarginTop={0}
        imageMarginBottom={0}
        paddingBottom={0}
        paddingTop={0}
        sliceToFullRows={true}
      />
    );
  } else if (item?.section_details?.identifier === '4X4_grid_card_ds2.0') {
    const bannerUrl = item?.section_details.section_bg_image
      ? item?.section_details.section_bg_image?.startsWith('https://') ||
        item?.section_details?.section_bg_image?.startsWith('http://')
        ? item?.section_details.section_bg_image
        : `${getBaseURL()}/images/cms/${item?.section_details.section_bg_image}`
      : undefined;
    return (
      <GridCategoryCard
        onClick={onClick}
        items={item?.data?.slider_details}
        banner={bannerUrl}
        title={item?.section_details?.section_title}
        paddingHorizontal={
          item?.section_details?.design_type.includes('grid') ? 0 : 0
        }
        imageBorderRadius={
          item?.section_details?.design_type.includes('grid') ? 0 : 10
        }
        imageGap={item?.section_details?.design_type.includes('grid') ? 0 : 10}
        imageAspectRatio={1}
        columns={4}
        imageMarginTop={20}
        imageMarginBottom={20}
        backgroundColor=""
        itemTitleColor="primary_grey_100"
        paddingTop={0}
        showGridTitle={true}
        sliceToFullRows={true}
        margin={10}
      />
    );
  } else if (
    item?.section_details?.identifier === 'portrait_image_banner_ds2.0'
  ) {
    // Stabilize computed props to prevent unnecessary re-renders
    const sliderLength = item?.data?.slider_details?.length || 0;
    const showIndicator = sliderLength > 3;
    const peekValue = sliderLength > 1 ? 16 : 0;

    return (
      <PortraitCarousel
        section_id={item?.section_details?.section_id}
        data={item?.data?.slider_details}
        title={item?.section_details?.section_title}
        borderRadius={20}
        showIndicator={showIndicator}
        autoScrollTimer={
          item?.data?.banner_details?.pause_time_between_transitions
        }
        navigation={navigation}
        padding={6}
        peekValue={peekValue}
        adMetaData={adMetaData}
      />
    );
  } else if (
    item?.section_details?.identifier === 'portrait_video_carousel_ds2.0'
  ) {
    const sliderTypeArr = item?.data?.slider_details.filter(
      (data: any) =>
        data.slide_type_name === JMSliderTypeName.IMAGE ||
        data.slide_type_name === JMSliderTypeName.JIO_ADS ||
        data.slide_type_name === JMSliderTypeName.ANIMATION,
    );
    const isVidComp = !sliderTypeArr.length;
    return isVidComp ? (
      <PortraitVideoCarousel
        section_id={item?.section_details?.section_id}
        data={item?.data?.slider_details}
        title={item?.section_details?.section_title}
        borderRadius={20}
        showIndicator={item?.data?.slider_details?.length > 3 ? true : false}
        navigation={navigation}
        peekValue={item?.data?.slider_details?.length > 1 ? 16 : 0}
      />
    ) : (
      <PortraitCarousel
        section_id={item?.section_details?.section_id}
        data={item?.data?.slider_details}
        title={item?.section_details?.section_title}
        borderRadius={20}
        showIndicator={item?.data?.slider_details?.length > 3 ? true : false}
        autoScrollTimer={
          item?.data?.banner_details?.pause_time_between_transitions
        }
        navigation={navigation}
        padding={item?.data?.slider_details?.length > 1 ? 6 : 10}
        peekValue={item?.data?.slider_details?.length > 1 ? 16 : 0}
        adMetaData={adMetaData}
      />
    );
  } else if (item?.section_details?.identifier === 'bento_grid_layout_ds2.0') {
    return (
      <BentoGridLayout
        images={item?.data?.slider_details}
        title={item?.section_details?.section_title}
        headerImage={item?.section_details?.section_bg_image}
        backgroundColor=""
        onClick={onClick}
      />
    );
  } else if (
    item?.section_details?.identifier === 'products_ds2.0' ||
    item?.section_details?.identifier === 'products'
  ) {
    return (
      <WidgetWithProducts
        widget={{
          title: item?.section_details?.section_title || '',
          title_url: item?.section_details?.viewall_link,
          title_type: item?.section_details?.identifier,
          product_codes: item?.data?.products || [],
        }}
        onClick={onClick}
        slug=""
      />
    );
  } else if (item?.section_details?.identifier === 'limited_products_ds2.0') {
    const bannerUrl = item?.section_details.banner_image
      ? item?.section_details.banner_image?.startsWith('https://') ||
        item?.section_details?.banner_image?.startsWith('http://')
        ? item?.section_details?.banner_image
        : `${getBaseURL()}/images/cms/${item?.section_details?.banner_image}`
      : undefined;
    return (
      showLimitedSection && (
        <LimitedProductsSection
          headerBanner={bannerUrl}
          headerText={item?.section_details?.section_title || ''}
          headerSubText={item?.section_details?.section_tagline || ''}
          backgroundColor={item?.section_details?.section_bg_color}
          titleUrl={item?.section_details?.viewall_link}
          onClick={onClick}>
          <WidgetWithProducts
            widget={{
              title: '',
              title_url: '',
              title_type: 'limited_products_ds2.0',
              product_codes: item?.data?.products || [],
            }}
            onClick={onClick}
            slug=""
            onSuccess={(val: boolean) => setShowLimitedSection(val)}
          />
        </LimitedProductsSection>
      )
    );
  } else if (
    item?.section_details?.identifier === 'buy_again_products_ds2.0' &&
    JMDatabaseManager.user.isUserLoggedInFlag()
  ) {
    return (
      <WidgetWithProducts
        widget={{
          title:
            item?.section_details?.section_title ||
            item?.section_details?.section_name,
          title_url: item?.section_details?.viewall_link,
          title_type: 'buy_again_products_ds2.0',
        }}
        onClick={onClick}
        slug=""
      />
    );
  } else if (
    item?.section_details?.identifier === 'slim_end_to_end_ds2.0' ||
    item?.section_details?.identifier === 'slim_banner_ds2.0' ||
    item?.section_details?.identifier === 'single_slim_banner_ds2.0' ||
    item?.section_details?.identifier === 'main_banner_new'
  ) {
    return (
      <SlimBanner
        title={item?.section_details?.section_title}
        images={item?.data?.slider_details}
        onClick={onClick}
        paddingHorizontal={0}
        borderRadius={
          item?.section_details?.identifier === 'slim_banner_ds2.0' ||
          item?.section_details?.identifier === 'single_slim_banner_ds2.0'
            ? 16
            : 0
        }
        imagePadding={(() => {
          const identifier = item?.section_details?.identifier;
          const hasMultipleSlides = item?.data?.slider_details?.length > 1;

          if (identifier === 'slim_banner_ds2.0' && hasMultipleSlides) return 6;
          if (
            identifier === 'slim_banner_ds2.0' ||
            identifier === 'single_slim_banner_ds2.0'
          )
            return 10;
          return 0;
        })()}
        peekValue={
          item?.section_details?.identifier === 'slim_banner_ds2.0' &&
          item?.data?.slider_details?.length > 1
            ? 16
            : 0
        }
        adMetaData={adMetaData}
        navigation={navigation}
      />
    );
  } else if (
    item?.section_details?.identifier === 'shop_by_vertical_banner_ds2.0' ||
    item?.section_details?.identifier === 'shop_by_vertical_banner'
  ) {
    return (
      <ShopByVericalBanner
        title={item?.section_details?.section_title}
        data={item?.data?.slider_details}
        onClick={onClick}
        hideTitle={
          item?.section_details?.identifier === 'shop_by_vertical_banner'
        }
      />
    );
  } else if (
    item?.section_details?.identifier === 'single_landscape_animation_ds2.0' ||
    item?.section_details?.identifier === 'hero_top_carousel_ds2.0' ||
    item?.section_details?.identifier === 'carousel_banner_ds2.0'
  ) {
    return (
      <HeroTopCarousel
        title={item?.section_details?.section_title}
        data={item?.data?.slider_details}
        navigation={navigation}
        autoScroll={true}
        showIndicator={
          item?.section_details?.identifier === 'hero_top_carousel_ds2.0'
        }
        autoScrollTimer={
          item?.data?.banner_details?.pause_time_between_transitions
        }
        peekValue={item?.data?.slider_details?.length > 1 ? 16 : 0}
        adMetaData={adMetaData}
      />
    );
  } else if (
    item?.section_details?.identifier === 'single_portrait_banner_ds2.0' ||
    item?.section_details?.identifier === 'page_end_banner_ds2.0' ||
    item?.section_details?.identifier === 'portrait_end_to_end_video_ds2.0'
  ) {
    return (
      <PortraitCarousel
        section_id={item?.section_details?.section_id}
        data={item?.data?.slider_details}
        title={item?.section_details?.section_title}
        borderRadius={
          item?.section_details?.identifier === 'single_portrait_banner_ds2.0'
            ? 20
            : 0
        }
        showIndicator={false}
        autoScrollTimer={
          item?.data?.banner_details?.pause_time_between_transitions
        }
        navigation={navigation}
        padding={
          item?.section_details?.identifier === 'single_portrait_banner_ds2.0'
            ? 10
            : 0
        }
        adMetaData={adMetaData}
      />
    );
  } else if (
    item?.section_details?.identifier ===
    SECTION_DETAILS_IDENTIFIER.MY_LIST_OFFERS
  ) {
    return (
      <HorizontalProductListWithBackground
        title="My List"
        backgroundColor="#E5F7EE"
        seeAllTextColor="primary_30"
        seeAllBackgroundColor="#E5F7EE"
        seeAllArrowIconColor={IconColor.ERROR}
        widgetData={item}
        navigation={navigation}
        paddingTop={20}
        jioAds={{
          adHeight: 478,
          adWidth: 240,
          adType: 5,
          adjWidth: 132,
          adjHeight: 478,
          adMetaData: adMetaData,
        }}
      />
    );
  } else if (
    item?.section_details?.identifier === 'home_top_banner_jioads_ds2.0' ||
    item?.section_details?.identifier === 'inner_banners_jioads_ds2.0' ||
    item?.section_details?.identifier === 'main_banner_new_jioads_ds2.0'
  ) {
    const jioadData = extractJioAdsData(item?.section_details?.value);
    return (
      <JioAds
        {...jioadData}
        adMetaData={{
          ...jioadData.adMetaData,
          ...adMetaData,
        }}
        navigation={navigation}
      />
    );
  } else if (item?.section_details?.identifier === 'carousel_card_ds2.0') {
    return (
      <HorizontalScrollableCards
        title={item?.section_details?.section_title}
        images={item?.data?.slider_details}
        onClick={onClick}
        imageHeight={rh(172)}
        imageWidth={rw(140)}
        onSeeAllClick={
          item?.section_details?.viewall_link !== ''
            ? onClick(item?.section_details?.viewall_link)
            : undefined
        }
      />
    );
  } else if (
    item?.section_details?.identifier === 'saleday_countdown_timer_ds2.0'
  ) {
    const bannerImage = item?.section_details?.section_bg_image
      ? item?.section_details?.section_bg_image?.startsWith('https://') ||
        item?.section_details?.section_bg_image?.startsWith('http://')
        ? item?.section_details?.section_bg_image
        : `${getBaseURL()}/images/cms/${
            item?.section_details?.section_bg_image
          }`
      : undefined;
    return (
      <SaledayCountdownTimer
        saleDateTime={item?.section_details?.value}
        bannerImage={bannerImage}
        margin={0}
        borderRadius={0}
      />
    );
  } else if (
    item?.section_details?.identifier === SECTION_DETAILS_IDENTIFIER.LOCAL_SHOPS
  ) {
    return (
      <LocalShopsListWidget
        title="Select a store to view products"
        backgroundColor="#fff"
        widgetData={item}
        navigation={navigation}
      />
    );
  } else {
    console.log('sponsored product ');
    return null;
  }
};

// Add React.memo for optimization
function areEqual(
  prevProps: SectionRendererProps,
  nextProps: SectionRendererProps,
) {
  // Shallow compare item and onClick (customize as needed)
  return (
    isEqual(prevProps.item, nextProps.item) &&
    prevProps.onClick === nextProps.onClick &&
    prevProps.onWidgetLoadingChange === nextProps.onWidgetLoadingChange &&
    prevProps.navigation === nextProps.navigation &&
    prevProps.route === nextProps.route
  );
}

export default React.memo(SectionRenderer, areEqual);
