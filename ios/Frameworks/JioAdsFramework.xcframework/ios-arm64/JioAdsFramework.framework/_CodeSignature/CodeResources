<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Assets.car</key>
		<data>
		HxuGJL82fjBEsiBRGsdsEfLCKqs=
		</data>
		<key>CampaignsData</key>
		<data>
		8GX+Tovbo7rKki5QYASZRHgE8q8=
		</data>
		<key>DefaultInstreamLayout.nib</key>
		<data>
		Cyoel+5uAQCVU+FSuHlwHb2tjGw=
		</data>
		<key>DefaultInterstitialMediaLayout.nib</key>
		<data>
		lhYBqjzfr7jiKK+FHahF4Brt7xk=
		</data>
		<key>DefaultVideoLayout.nib</key>
		<data>
		+oSqipcDvobYPYfbtO2zIvU0RAU=
		</data>
		<key>Headers/JioAdsFramework-Swift.h</key>
		<data>
		JCVQ3zpq8/H7+Vijkxjb2++cNg8=
		</data>
		<key>Info.plist</key>
		<data>
		eQ5EKDbGxTl1kDHdEARuAugleIU=
		</data>
		<key>InterstitialVideoLayout.nib</key>
		<data>
		sgH7PQ08IPn+VvQS8jWWMVd8y+s=
		</data>
		<key>JioCarouselCellItemViewLayout.nib</key>
		<data>
		wZokd1IwgW0I2CJs3h6h9cY2/A8=
		</data>
		<key>JioCollectionCellViewLayout.nib</key>
		<data>
		A9tZuWfqtrTYnBCY7/6QUNqaP1w=
		</data>
		<key>JioCollectionViewLayout.nib</key>
		<data>
		nwLy+c4lIuwXuhWdgV6VddfgQFI=
		</data>
		<key>JioDefaultVideoLayout.nib</key>
		<data>
		2/slP8JSk6YHOQp9OXdZHyKgeII=
		</data>
		<key>JioInstreamAudioLayout.nib</key>
		<data>
		KpU0kgD1nmZFkYf8YcWZqrg5BOs=
		</data>
		<key>JioInstreamLayout.nib</key>
		<data>
		Z6v/Ivlor3mnni6MHBFgcoBndqw=
		</data>
		<key>Modules/JioAdsFramework.swiftmodule/arm64-apple-ios.abi.json</key>
		<data>
		QNZs04ijMlfQ6m4ZH38mTuBMHDw=
		</data>
		<key>Modules/JioAdsFramework.swiftmodule/arm64-apple-ios.private.swiftinterface</key>
		<data>
		w0SIW0/x8iOwpOP2r9YRQ+JZcgM=
		</data>
		<key>Modules/JioAdsFramework.swiftmodule/arm64-apple-ios.swiftdoc</key>
		<data>
		E8hwc9AjL7ILDpd4sUfTfuIXb0U=
		</data>
		<key>Modules/JioAdsFramework.swiftmodule/arm64-apple-ios.swiftinterface</key>
		<data>
		w0SIW0/x8iOwpOP2r9YRQ+JZcgM=
		</data>
		<key>Modules/JioAdsFramework.swiftmodule/arm64-apple-ios.swiftmodule</key>
		<data>
		gi2ebPwa0L+Spmf0RbA0QkHjcPE=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		BW2dx+NI/SvQcLr/4dzLGjDNgOc=
		</data>
		<key>NativeContentStreamLayout.nib</key>
		<data>
		5ioHhTgad4L4dDo7dSK5ksIWUeE=
		</data>
		<key>NativeInFeed300x50Layout.nib</key>
		<data>
		775HhViEKTTUhfIM86v+4SIZFh4=
		</data>
		<key>NativeInFeed320x100Layout.nib</key>
		<data>
		MX/QjI7scBe5FGGeZ5D/JqA9N70=
		</data>
		<key>NativeInFeedLayout.nib</key>
		<data>
		cToV7nsZzyw4kimAx2jEj0GZhLE=
		</data>
		<key>PrivateHeaders/JioAdsFramework.h</key>
		<data>
		Xkh5jnIu9qxEcEfxG3ptDO3ondE=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			iOTDBfYTcfWxUmGCYCCWDxMRxY+7VYutFnHK5iTnV5Q=
			</data>
		</dict>
		<key>CampaignsData</key>
		<dict>
			<key>hash2</key>
			<data>
			pq6m5cS+URu5Pc5gNIrnaKiZOeJiUzu/6XDdJnZruMg=
			</data>
		</dict>
		<key>DefaultInstreamLayout.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			h8KgtVdNG/Qz3uHypHilbZS6RabV1OZbHO1FvDAgBJQ=
			</data>
		</dict>
		<key>DefaultInterstitialMediaLayout.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			0T43rCYEYMXQhdVnzkjB5Ts/9bbyvIqRJSp/5Gmi1KE=
			</data>
		</dict>
		<key>DefaultVideoLayout.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			VXcYYmhPECw8T/1cXJoYLAXJFu7feVXCg0O8Fz8YNXk=
			</data>
		</dict>
		<key>Headers/JioAdsFramework-Swift.h</key>
		<dict>
			<key>hash2</key>
			<data>
			DofRn29WCI60P1uMh4mU3g7/4kfQIjDUW7uo+dAIMY8=
			</data>
		</dict>
		<key>InterstitialVideoLayout.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			CCw0nvwZr/T1vfOKKp+A/KNqKl4W4ryF8Geynzd0RmQ=
			</data>
		</dict>
		<key>JioCarouselCellItemViewLayout.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			dXkCrZ4NWOTTS5vn7Ax0v32EwJM5xmA5h1gaQ2i895o=
			</data>
		</dict>
		<key>JioCollectionCellViewLayout.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			EVczE5jopUv0HQBsQRumaXvYkcMvXbVjr/1rZ9PkqGw=
			</data>
		</dict>
		<key>JioCollectionViewLayout.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			KcUhOhzusKbgwM4mnkRyj9Py3b1+5diG1oEWoTl/Vsw=
			</data>
		</dict>
		<key>JioDefaultVideoLayout.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			UKcR9fKqghKo/Y/cWv/giaqqu1YephWg+kyYIW8Y05s=
			</data>
		</dict>
		<key>JioInstreamAudioLayout.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			amwMfzx5INxxSoKyMDuZr3n1edRpdZphmQCtXhmEvug=
			</data>
		</dict>
		<key>JioInstreamLayout.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			O+j0U1pZYiqzgHZ6hYmVpGmNebGKebDUNB7XU5mV8kc=
			</data>
		</dict>
		<key>Modules/JioAdsFramework.swiftmodule/arm64-apple-ios.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			R/Z3eY8izuWIXksTVyD9ay30U1pkepX5Ji64nv6Un78=
			</data>
		</dict>
		<key>Modules/JioAdsFramework.swiftmodule/arm64-apple-ios.private.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			jFzJl6dnVsqIbn3Oe93vN/JvraZamcnsACMoypwstNk=
			</data>
		</dict>
		<key>Modules/JioAdsFramework.swiftmodule/arm64-apple-ios.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			YYlHGdeeWG91DY26gDDiHNRi/4UtH/Bfv838zdwDYFw=
			</data>
		</dict>
		<key>Modules/JioAdsFramework.swiftmodule/arm64-apple-ios.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			jFzJl6dnVsqIbn3Oe93vN/JvraZamcnsACMoypwstNk=
			</data>
		</dict>
		<key>Modules/JioAdsFramework.swiftmodule/arm64-apple-ios.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			TuIMhU6zgMnwvtkjiGsUelNftiwSm7o4e1sSoCd76Dc=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			Hnfi4KqfkYZ7GgBtlP6rZKpSoOB5SgCWAD4GQvrhdOg=
			</data>
		</dict>
		<key>NativeContentStreamLayout.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			+jbkkJ9K7flrLtSD2Axl3K2OhCIvuGhT0/2VnDBHJQY=
			</data>
		</dict>
		<key>NativeInFeed300x50Layout.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			4VUjAhImH/7OY8fIaev5FpD3VIhEf66KMJb3YhmcXsU=
			</data>
		</dict>
		<key>NativeInFeed320x100Layout.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			lRi0zq/X2CLdX7y5UXWr7TUlNlqm0dnjmA4gGkGcebY=
			</data>
		</dict>
		<key>NativeInFeedLayout.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			mhzvZzfM1tYoVrkPYP/Y2Ntf/K1YIxUeSnlz1HJzSoE=
			</data>
		</dict>
		<key>PrivateHeaders/JioAdsFramework.h</key>
		<dict>
			<key>hash2</key>
			<data>
			FAdvJvwsXZxOHS2K2t+Lhh33ZdlIW7mfsQZmtXiW/84=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
