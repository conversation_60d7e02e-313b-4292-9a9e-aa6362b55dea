import type {
  JMCartModel,
  JMCartItemModel,
} from '../../../jiomart-networkmanager/src/models/Cart/JMCartModel';
import {createSlice, type PayloadAction} from '@reduxjs/toolkit';

const initialState: JMCartModel = {
  cart_id: '',
  success: '',
  message: '',
  items: [],
  cartCount: 0,
};

interface OptimisticUpdatePayload {
  uid: number;
  quantity: number;
  product_name?: string;
  minQty?: number;
  maxQty?: number;
}

const cartSlice = createSlice({
  name: 'cart',
  initialState,
  reducers: {
    setCart: (_, action: PayloadAction<JMCartModel>) => {
      return action.payload;
    },
    resetCart: () => {
      return initialState;
    },
    // Optimistic update for adding/updating item quantity
    optimisticUpdateItem: (
      state,
      action: PayloadAction<OptimisticUpdatePayload>,
    ) => {
      const {uid, quantity, product_name, minQty, maxQty} = action.payload;
      const existingItemIndex = state.items.findIndex(
        (item: JMCartItemModel) => item?.uid === uid,
      );

      if (existingItemIndex >= 0) {
        // Update existing item
        state.items[existingItemIndex].quantity = quantity;
        if (quantity <= 0) {
          // Remove item if quantity is 0 or less
          state.items = state.items.filter(
            (item: JMCartItemModel) => item?.uid !== uid,
          );
          state.cartCount = Math.max(0, state.cartCount - 1);
        }
      } else if (quantity > 0) {
        // Add new item
        const newItem: JMCartItemModel = {
          uid,
          quantity,
          product_name: product_name || '',
          moq: {
            min: minQty || 1,
            max: maxQty || 999,
            increment: 1,
          },
        };
        state.items.push(newItem);
        state.cartCount = state.cartCount + 1;
      }
    },
    // Rollback optimistic update when API fails
    rollbackOptimisticUpdate: (
      state,
      action: PayloadAction<{
        uid: number;
        previousQuantity: number;
        wasInCart: boolean;
      }>,
    ) => {
      const {uid, previousQuantity, wasInCart} = action.payload;
      const existingItemIndex = state.items.findIndex(
        (item: JMCartItemModel) => item?.uid === uid,
      );

      if (wasInCart && previousQuantity > 0) {
        if (existingItemIndex >= 0) {
          // Restore previous quantity
          state.items[existingItemIndex].quantity = previousQuantity;
        } else {
          // Item was removed optimistically but should be restored
          const restoredItem: JMCartItemModel = {
            uid,
            quantity: previousQuantity,
            product_name: '',
            moq: {min: 1, max: 999, increment: 1},
          };
          state.items.push(restoredItem);
          state.cartCount = state.cartCount + 1;
        }
      } else if (!wasInCart && existingItemIndex >= 0) {
        // Item was added optimistically but should be removed
        state.items = state.items.filter(
          (item: JMCartItemModel) => item?.uid !== uid,
        );
        state.cartCount = Math.max(0, state.cartCount - 1);
      }
    },
  },
});

export const cartCount = (state: any) => state.cart.cartCount;
export const cartItems = (state: any) => state.cart.items;
export const cartId = (state: any) => state.cart.cart_id;
export const cart = (state: any) => state.cart;
export const {
  setCart,
  resetCart,
  optimisticUpdateItem,
  rollbackOptimisticUpdate,
} = cartSlice.actions;
export default cartSlice.reducer;
