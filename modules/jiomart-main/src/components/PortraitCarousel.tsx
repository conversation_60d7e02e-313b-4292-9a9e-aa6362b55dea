import React, {useCallback, useEffect, useState} from 'react';
import {View, StyleSheet, Pressable} from 'react-native';
import FastImage from 'react-native-fast-image';
import CustomCarousel from './CustomCarousel';
import {navigateTo} from '../../../jiomart-general/src/navigation/JMNavGraph';
import {
  navBeanObj,
  NavigationType,
} from '../../../jiomart-common/src/JMNavGraphUtil';
import {JioTypography} from '@jio/rn_components/src/index.types';
import {SliderDetails} from '../../../jiomart-home/src/utils/HomePageTypes';
import JioText from '@jio/rn_components/src/components/JioText/JioText';
import {JMSliderTypeName} from '../../../jiomart-home/src/types/JMHomeComponentType';
import JioAds from '../../../jiomart-general/src/JioAds/JioAds';
import {
  extractJioAdsData,
  getAdHeight,
} from '../../../jiomart-home/src/utils/HomeUtils';
import Video from 'react-native-video';
import {getBaseURL} from '../../../jiomart-networkmanager/src/JMEnvironmentConfig';
import LazyFastImage from './LazyFastImage';
import {getScreenDim} from '../../../jiomart-common/src/JMResponsive';
import {checkAdStatusNB} from '../../../jiomart-general/src/bridge/JMRNBridge';
import isEqual from 'lodash.isequal';
import {getImageHeight} from '../../../jiomart-common/src/utils/JMImageUtility';
interface PortraitCarouselProps {
  section_id?: any;
  data: SliderDetails[];
  title: string;
  titleMaxLine?: number;
  borderRadius: number;
  showIndicator: boolean;
  autoScrollTimer?: number;
  navigation: any;
  padding?: number;
  peekValue?: number;
  adMetaData?: any;
}

function useCarouselHeight(data: any[], padding: number) {
  const [carouselHeight, setCarouselHeight] = useState<number>(0);

  useEffect(() => {
    if (!data || data.length === 0) return;

    const calculate = async () => {
      const heightPromises = data.map(async item => {
        try {
          if (item.slide_type_name === JMSliderTypeName.IMAGE && item.image) {
            return await getImageHeight(item.image);
          }
          if (item.slide_type_name === JMSliderTypeName.VIDEO) {
            return await getImageHeight(item?.image);
          }
          if (item.slide_type_name === JMSliderTypeName.JIO_ADS) {
            return getAdHeight(item?.img_alt, padding);
          }
        } catch (err) {
          console.warn('Skipping item with error:', err);
        }
        return 0;
      });

      const heights = await Promise.all(heightPromises);
      const validHeights = heights.filter(h => h > 0);
      if (validHeights.length > 0) {
        setCarouselHeight(Math.min(...validHeights));
      }
    };

    calculate();
  }, [data]);

  return carouselHeight;
}

const PortraitCarousel = ({
  data,
  title,
  titleMaxLine = 2,
  borderRadius = 20,
  showIndicator = false,
  autoScrollTimer = 3000,
  navigation,
  padding = 0,
  peekValue = 0,
  adMetaData,
  section_id,
}: PortraitCarouselProps) => {
  const [filteredData, setFilteredData] = useState<SliderDetails[]>([]);
  const carouselHeight = useCarouselHeight(filteredData, padding);

  const handlePress = (url: string) => {
    // TODO: Replace with navigation or linking logic as needed
    console.log('Pressed URL:', url);
    navigateTo(
      navBeanObj({
        source: '',
        destination: 'CommonWebViewScreen',
        actionType: 'T003',
        headerType: 9,
        headerVisibility: 2,
        navigationType: NavigationType.PUSH,
        actionUrl: url,
        loginRequired: false,
      }),
      navigation,
    );
  };
  const filterAdsFromRenderItemData = useCallback(
    async (items: SliderDetails[]) => {
      const results = await Promise.all(
        items.map(async item => {
          if (item?.slide_type_name === JMSliderTypeName.JIO_ADS) {
            const adsData = extractJioAdsData(item?.img_alt);
            const isValid = await checkAdStatusNB({
              adType: adsData.adType,
              adspotKey: adsData.adSpotId,
              adHeight: adsData.adHeight,
              adWidth: adsData.adWidth,
              adCustomHeight: adsData.adjHeight,
              adCustomWidth: 0,
              adMetaData: {...adsData.adMetaData, ...adMetaData},
            });
            return isValid ? item : null;
          }
          return item; // keep non-JioAds as is
        }),
      );

      // Now filter out nulls
      const filtered = results.filter(
        (item): item is SliderDetails => item !== null,
      );
      return filtered;
    },
    [],
  );

  useEffect(() => {
    if (data && data.length > 0) {
      filterAdsFromRenderItemData(data).then(setFilteredData);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  const renderItem = (item: SliderDetails) => {
    switch (item?.slide_type_name) {
      case JMSliderTypeName.JIO_ADS:
        const adsData = extractJioAdsData(item?.img_alt);
        return (
          <JioAds
            style={{margin: padding}}
            {...adsData}
            adMetaData={{
              ...adsData.adMetaData,
              ...adMetaData,
            }}
            adjWidth={getScreenDim.width - (peekValue + padding) * 2}
            adjHeight={carouselHeight - (padding + peekValue) * 2}
            navigation={navigation}
          />
        );
      case JMSliderTypeName.IMAGE: {
        return (
          <Pressable
            style={[
              styles.image,
              {
                borderRadius: borderRadius,
                padding: padding,
                height: carouselHeight,
              },
            ]}
            onPress={() => item.url && handlePress(item.url)}>
            <LazyFastImage
              imageUrl={item.image}
              style={{
                width: '100%',
                height: '100%',
                borderRadius: borderRadius,
              }}
              resizeMode={FastImage.resizeMode.cover}
            />
          </Pressable>
        );
      }
      case JMSliderTypeName.VIDEO: {
        return (
          <View
            style={[
              styles.image,
              {
                borderRadius: borderRadius,
                padding: padding,
                height: carouselHeight,
              },
            ]}>
            <Pressable
              style={{
                width: '100%',
                height: '100%',
                borderRadius: borderRadius,
              }}
              onPress={() => item.url && handlePress(item.url)}>
              <Video
                key={section_id}
                source={{
                  uri:
                    item.video_file &&
                    (item.video_file.startsWith('http://') ||
                      item.video_file.startsWith('https://'))
                      ? item.video_file
                      : `${getBaseURL()}/images/cms/aw_rbslider/slides/videos/${
                          item.video_file
                        }`,
                }}
                style={{
                  width: '100%',
                  height: '100%',
                  borderRadius: borderRadius,
                  backgroundColor: 'black',
                  overflow: 'hidden'
                }}
                resizeMode="stretch"
                controls={false}
                paused={false}
                repeat={true}
                muted={true}
                playInBackground={true}
                playWhenInactive={true}
                onError={e => console.log('Video error:', e)}
                onLoad={data => console.log('Video loaded successfully:', data)}
                onBuffer={({isBuffering}) =>
                  console.log('Video buffering:', isBuffering)
                }
              />
            </Pressable>
          </View>
        );
      }
      default:
        return null;
    }
  };

  if (
    carouselHeight === 0 ||
    !filteredData ||
    (Array.isArray(filteredData) && filteredData.length === 0)
  ) {
    return null;
  }

  console.log('portrait carousel re-render');
  return (
    <View style={[styles.container]}>
      <View style={styles.header}>
        <JioText
          text={title}
          color="black"
          appearance={JioTypography.HEADING_XXS}
          style={styles.title}
          maxLines={titleMaxLine}
        />
      </View>
      <CustomCarousel
        autoScroll
        items={filteredData}
        RenderItem={renderItem}
        customStyles={{
          itemList: {
            backgroundColor: 'white',
            height: carouselHeight,
          },
          indicatorList: {
            ...styles.carouselIndicatorsView,
          },
          indicatorItemList: {
            ...styles.carouselIndicatorItem,
          },
        }}
        showIndicator={showIndicator}
        autoScrollTimer={autoScrollTimer}
        peek={peekValue ? peekValue : 0}
      />
    </View>
  );
};

// Add React.memo for optimization
function areEqual(
  prevProps: PortraitCarouselProps,
  nextProps: PortraitCarouselProps,
) {
  return (
    isEqual(prevProps.data, nextProps.data) &&
    prevProps.title === nextProps.title &&
    prevProps.borderRadius === nextProps.borderRadius &&
    prevProps.showIndicator === nextProps.showIndicator &&
    prevProps.autoScrollTimer === nextProps.autoScrollTimer &&
    isEqual(prevProps.padding, nextProps.padding) &&
    isEqual(prevProps.peekValue, nextProps.peekValue)
  );
}

export default React.memo(PortraitCarousel, areEqual);

const styles = StyleSheet.create({
  container: {
    paddingVertical: 12
  },
  image: {
    // height will be set dynamically
    // marginHorizontal:8,
    // backgroundColor: 'grey',
  },
  carouselIndicatorsView: {
    paddingTop: 4,
  },
  carouselIndicatorItem: {},
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignContent: 'center',
  },
  title: {
    paddingLeft: 16,
    paddingBottom: 12,
  },
});
