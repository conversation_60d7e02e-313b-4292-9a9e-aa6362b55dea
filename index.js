/**
 * @format
 */

import {AppRegistry} from 'react-native';
import App from './App';
import {name as appName} from './app.json';
import JioCodePush from '@jio/codepush';
import {Text, TextInput} from 'react-native';

const BaseApp = props => {
  Text.defaultProps = Text.defaultProps || {};
  Text.defaultProps.allowFontScaling = false;

  TextInput.defaultProps = TextInput.defaultProps || {};
  TextInput.defaultProps.allowFontScaling = false;
  return <App props={props} />;
};

AppRegistry.registerComponent(appName, () => {
  return initialProps => {
    const AppWrapper = JioCodePush(initialProps)(BaseApp);
    return <AppWrapper {...initialProps} />;
  };
});
