import {useConfigFile} from '../../../jiomart-general/src/hooks/useJMConfig';
import {JMConfigFileName} from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import {useCallback, useEffect, useState} from 'react';
import {
  ActionSheetIOS,
  BackHandler,
  NativeModules,
  Platform,
} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {JMAccountScreenProps} from '../types/JMAccountScreenType';
import JMUserApiNetworkController from '../../../jiomart-networkmanager/src/JMNetworkController/JMUserAPINetworkController';
import {getPrefString} from '../../../jiomart-common/src/JMAsyncStorageHelper';
import {AsyncStorageKeys} from '../../../jiomart-common/src/JMConstants';
import {
  getOneRetailActionUrl,
  navigateTo,
} from '../../../jiomart-general/src/navigation/JMNavGraph';
import {
  ActionType,
  getHomeAppScreenDestination,
  navBeanObj,
  NavigationBean,
  NavigationType,
} from '../../../jiomart-common/src/JMNavGraphUtil';
import {getBaseURL} from '../../../jiomart-networkmanager/src/JMEnvironmentConfig';
import {JMSharedViewModel} from '../../../jiomart-common/src/JMSharedViewModel';
import {AppSourceType} from '../../../jiomart-common/src/SourceType';
import {useGlobalState} from '../../../jiomart-general/src/context/JMGlobalStateProvider';
import {useDispatch} from 'react-redux';
import {resetCart} from '../../../jiomart-cart/src/slices/cartSlice';
import {HeaderType} from '../../../jiomart-common/src/JMScreenSlot.types';
import {resetWishlist} from '../../../jiomart-wishlist/src/slices/wishlistSlice';
import {logAnalyticsEvent} from '../../../jiomart-common/src/JMAnalyticsUtility';
import {EventTriggerChannel} from '../../../jiomart-common/src/AnalyticsParams';
import {JMStorageService} from '../../../jiomart-networkmanager/src/api/utils/JMStorageService';
import {JMDatabaseManager} from '../../../jiomart-networkmanager/src/db/JMDatabaseManager';
import {useFocusEffect} from '@react-navigation/native';
import {useQueryClient} from '@tanstack/react-query';

const {JMRNNavigatorModule} = NativeModules;
const userApiNetworkController = new JMUserApiNetworkController();

const useAccountScreenController = (props: JMAccountScreenProps) => {
  const {setUserInitials, notificationCount} = useGlobalState();
  const dispatch = useDispatch();
  const {navigation} = props;

  const myProfileConfig = useConfigFile(
    JMConfigFileName.JMProfileConfigurationFileName,
  );

  const [userDataResponse, setUserData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [signOutBtmSheet, setSignOutBtmSheet] = useState(false);

  useEffect(() => {
    const backAction = () => {
      if (navigation.canGoBack()) {
        navigation.goBack();
        return true;
      }
      return false;
    };
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction,
    );

    return () => {
      backHandler.remove();
    };
  }, []);

  useFocusEffect(
    useCallback(() => {
      fetchUserDetials();
    }, []),
  );

  const fetchUserDetials = async () => {
    const storedData = await getPrefString(AsyncStorageKeys.PROFILE_DETAILS);

    if (storedData) {
      setUserData(JSON.parse(storedData));
      setLoading(false);
    }

    const userDetails = await userApiNetworkController.fetchUserDetails();
    if (userDetails) {
      setUserData(userDetails);
      JMDatabaseManager.user.saveUserDetails(userDetails);
      setUserInitials(userDetails?.full_name);
      setLoading(false);
    }
  };
  const {setAddress} = useGlobalState();
  const queryClient = useQueryClient();
  const clearOnLogout = async () => {
    await JMStorageService.clearAllKeys();
    setUserInitials('');
    dispatch(resetCart());
    dispatch(resetWishlist());
    queryClient.clear();
    userApiNetworkController.fetchGuestUserSession();
  };

  const handleAddress = async () => {
    let address: any = await JMDatabaseManager.address.getDefaultAddress();
    address = JSON.parse(address ?? '');
    setAddress({
      pin: address?.pin,
      city: address?.city,
      state: address?.state,
    });
  };

  const signOutUser = async () => {
    try {
      if (JMSharedViewModel.Instance.appSource === AppSourceType.JM_BAU) {
        await userApiNetworkController.logoutUser();
      }
      handleAddress();
      await clearOnLogout();
      await logAnalyticsEvent({
        eventName: 'logout',
        payload: {
          category: 'authentication',
          action: 'logout',
          label: 'user logout',
        },
        channel: `${EventTriggerChannel.FIREBASE},${EventTriggerChannel.CLEVER_TAP}`,
      });
      if (navigation && navigation.canGoBack!!) {
        navigateTo(
          navBeanObj({
            actionType: ActionType.OPEN_WEB_URL,
            destination: getHomeAppScreenDestination(),
            headerVisibility: HeaderType.CUSTOM,
            navigationType: NavigationType.RESET,
            loginRequired: false,
            actionUrl: getBaseURL(),
            shouldShowBottomNavBar: false,
            shouldShowDeliverToBar: true,
            headerType: 5,
          }),
          navigation,
        );
      }
    } catch (error) {}
  };
  var statusBarColor = '#0078ac';
  const insets = useSafeAreaInsets();
  const statusBarHeight = insets.top;

  const showDeleteAccountActionSheet = (
    config: any,
    navigationBean: NavigationBean,
  ) => {
    ActionSheetIOS.showActionSheetWithOptions(
      {
        title: config?.title,
        options: [config?.deleteAccountBtn, config?.dismissBtn],
        cancelButtonIndex: 1,
      },
      buttonIndex => {
        if (buttonIndex === 0) {
          navigateTo(navigationBean, navigation);
        }
      },
    );
  };

  function redirectTo(item: any) {
    if (item?.title) {
      logAnalyticsEvent({
        eventName: 'my_account',
        payload: {
          category: 'my account',
          action: item?.title + ' clicked',
          label: 'my_account_' + item?.title + '_clicked',
          pageType: 'my account page',
        },
        channel: EventTriggerChannel.FIREBASE,
      });
    }
    if (item?.deeplink?.DeeplinkIdentifier === 'sign_out') {
      openSignOutBtmSheet();
      return;
    }

    if (item?.type === 'AccountCard') {
      navigateTo(
        navBeanObj({
          ...item.cta,
          actionUrl: getOneRetailActionUrl('PROFILE'),
        }),
        navigation,
      );
      return;
    }

    if (item?.type === 'CloseAccount' && Platform.OS === 'ios') {
      showDeleteAccountActionSheet(item?.actionSheet, item?.cta);
      return;
    } else if (item?.type === 'CloseAccount' && Platform.OS === 'android') {
      return;
    }

    navigateTo(
      navBeanObj({
        ...item?.cta,
        actionUrl: `${item?.cta?.actionUrl}`,
      }),
      navigation,
    );
  }

  const openSignOutBtmSheet = () => {
    setSignOutBtmSheet(true);
  };
  const closeSignOutBtmSheet = () => {
    setSignOutBtmSheet(false);
  };

  return {
    statusBarHeight,
    statusBarColor,
    shouldShowDeliverToBar,
    userDataResponse,
    JMRNNavigatorModule,
    myProfileConfig,
    loading,
    insets,
    ...props,
    navigationBean: props.route.params,
    redirectTo,
    notificationCount,
    openSignOutBtmSheet,
    signOutBtmSheet,
    closeSignOutBtmSheet,
    signOutUser,
  };
};

const shouldShowDeliverToBar = false;

export default useAccountScreenController;
