import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {Platform} from 'react-native';
import {StackAnimationTypes} from 'react-native-screens';
import {
  getHomeAppScreenDestination,
  NavigationStackData,
} from '../../../jiomart-common/src/JMNavGraphUtil';
import CommonWebViewScreen from '../../../jiomart-webmanager/src/WebViewScreen';
import AllCategoriesScreen from '../../../jiomart-category/src/AllCategoriesScreen';
import {AppScreens} from '../../../jiomart-common/src/JMAppScreenEntry';
import {getDestinationFromDeeplinkUrl} from '../../../jiomart-general/src/deeplink/JMDeeplinkUtility';
import JMQRScannerScreen from '../../../jiomart-general/src/ui/QrScanner/screen/JMQRScannerScreen';
import JMOrderNavigation from '../../../jiomart-order/src/JMOrderNavigation';
import JMAddressNavigation from '../../../jiomart-address/src/JMAddressNavigation';
import JMProductListingNavigation from '../../../jiomart-product-list/src/JMProductListingNavigation';
import JMSearchScreen from '../../../jiomart-search/src/JMSearchScreen';
import Account from '../../../jiomart-account/src/screens/Account';
import JMFeedbackNavigation from '../../../jiomart-feedback/src/JMFeedbackNavigation';
import NotificationScreen from '../../../jiomart-account/src/screens/NotificationScreen';
import JMHomeDashboardScreen from '../../../jiomart-home/src/screen/homesection/JMHomeDashboardScreen';
import JMHomeScreen from '../../../jiomart-home/src/screen/JMHomeScreen';
import JMSplashScreen from '../features/Splash/JMSplashScreen';
import JMHomeScreenImpl from '../../../jiomart-home/src/screen/homesection/JMHomeDashboardScreen';
import JMAppFAQ from '../../../jiomart-order/src/screens/JMAppFAQScreen';
import JMHelpScreen from '../../../jiomart-order/src/screens/JMHelpScreen';

const NavStack = createNativeStackNavigator<NavigationStackData>();

const JMNavigationStack = () => {
  return (
    <NavStack.Navigator
      initialRouteName={
        getDestinationFromDeeplinkUrl(
          '',
          getHomeAppScreenDestination(),
          true,
        ) as keyof NavigationStackData
      }
      screenOptions={{
        headerShown: false,
        gestureEnabled: Platform.OS === 'ios' ? false : true,
      }}>
      <NavStack.Screen
        name={AppScreens.SPLASH_SCREEN}
        component={JMSplashScreen}
        options={{
          animation: 'none',
        }}
      />
      <NavStack.Screen
        name={AppScreens.HOME_SCREEN}
        component={JMHomeScreen}
        options={({route}) => {
          const navigationBean = route.params || {};
          return {
            animation:
              (navigationBean.animation as StackAnimationTypes) || 'none',
          };
        }}
      />
      <NavStack.Screen
        name={AppScreens.COMMON_WEB_VIEW}
        component={CommonWebViewScreen}
        options={({route}) => {
          const navigationBean = route.params || {};
          return {
            animation:
              (navigationBean.animation as StackAnimationTypes) || 'slide_from_right',
          };
        }}
      />
      <NavStack.Screen
        name={AppScreens.ADDRESS_SCREEN}
        component={JMAddressNavigation}
        options={({route}) => {
          const navigationBean = route.params || {};
          return {
            animation:
              (navigationBean.animation as StackAnimationTypes) || 'slide_from_right',
          };
        }}
      />
      <NavStack.Screen
        name={AppScreens.ORDER_SCREEN}
        component={JMOrderNavigation}
        options={({route}) => {
          const navigationBean = route.params || {};
          return {
            animation:
              (navigationBean.animation as StackAnimationTypes) || 'slide_from_right',
          };
        }}
      />
      <NavStack.Screen
        name={AppScreens.PRODUCT_LISTING_SCREEN_START}
        component={JMProductListingNavigation}
        options={({route}) => {
          const navigationBean = route.params || {};
          return {
            animation:
              (navigationBean.animation as StackAnimationTypes) || 'slide_from_right',
          };
        }}
      />
      {/* <NavStack.Screen
        name={AppScreens.ONE_RETAIL_SCREEN}
        component={OneRetailUI}
        options={{
          animation: 'default',
        }}
      /> */}
      <NavStack.Screen
        name={AppScreens.ALL_CATEGORIES}
        component={AllCategoriesScreen}
        options={({route}) => {
          const navigationBean = route.params || {};
          return {
            animation:
              (navigationBean.animation as StackAnimationTypes) || 'slide_from_right',
          };
        }}
      />
      <NavStack.Screen
        name={AppScreens.SEARCH_SCREEN}
        component={JMSearchScreen}
        options={({route}) => {
          const navigationBean = route.params || {};
          return {
            animation:
              (navigationBean.animation as StackAnimationTypes) || 'slide_from_right',
          };
        }}
      />
      <NavStack.Screen
        name={AppScreens.ACCOUNT_SCREEN}
        component={Account}
        options={({route}) => {
          const navigationBean = route.params || {};
          return {
            animation:
              (navigationBean.animation as StackAnimationTypes) || 'slide_from_right',
          };
        }}
      />
      <NavStack.Screen
        name={AppScreens.FEEDBACK_SCREEN}
        component={JMFeedbackNavigation}
        options={({route}) => {
          const navigationBean = route.params || {};
          return {
            animation:
              (navigationBean.animation as StackAnimationTypes) || 'slide_from_right',
          };
        }}
      />
      <NavStack.Screen
        name={AppScreens.NOTIFICATION_SCREEN}
        component={NotificationScreen}
        options={({route}) => {
          const navigationBean = route.params || {};
          return {
            animation:
              (navigationBean.animation as StackAnimationTypes) || 'slide_from_right',
          };
        }}
      />
      <NavStack.Screen
        name={AppScreens.HOME_DASHBOARD_SCREEN}
        component={JMHomeScreenImpl}
        options={({route}) => {
          const navigationBean = route.params || {};
          return {
            animation:
              (navigationBean.animation as StackAnimationTypes) || 'none',
          };
        }}
      />
      <NavStack.Screen
        name={AppScreens.SCANNER_SCREEN}
        component={JMQRScannerScreen}
        options={({route}) => {
          const navigationBean = route.params || {};
          return {
            animation:
              (navigationBean.animation as StackAnimationTypes) || 'slide_from_right',
          };
        }}
      />

      <NavStack.Screen
        name={AppScreens.HELP_SCREEN}
        component={JMHelpScreen}
        options={{
          animation: 'default',
        }}
      />
    </NavStack.Navigator>
  );
};

export default JMNavigationStack;
