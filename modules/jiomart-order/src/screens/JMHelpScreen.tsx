import React, {useRef, useEffect, useState} from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Easing,
  ActivityIndicator,
} from 'react-native';
import useJM<PERSON>elpScreenController, {
  QuickLink,
  FAQ,
  TabType,
} from '../controllers/useJMHelpScreenController';
import {JMHelpScreenProps} from '../types/JMHelpScreenType';
import ScreenSlot, {
  DeeplinkHandler,
} from '../../../jiomart-general/src/ui/JMScreenSlot';
import {JioButton, JioIcon, JioText} from '@jio/rn_components';
import {
  ButtonKind,
  ButtonSize,
  IconSize,
  JioTypography,
} from '@jio/rn_components/src/index.types';
import JMHelpOrderCard from '../components/JMHelpOrderCard';
import JMHelpServiceRequestCard from '../components/JMHelpServiceRequestCard';
import {
  capitalizeFirstLetter,
  getItemByViewType,
} from '../../../jiomart-common/src/utils/JMStringUtility';
import {useConfigFile} from '../../../jiomart-general/src/hooks/useJMConfig';
import {JMConfigFileName} from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import {formatDateFromString} from '../../../jiomart-common/src/utils/JMDateUtility';

const JMHelpScreen = (props: JMHelpScreenProps) => {
  const {
    navigationBean,
    navigation,
    selectedTab,
    showFloatingButton,
    visible,
    orderVisible,
    orderData,
    serviceRequestData,
    tabs,
    getCurrentFAQs,
    handleTabPress,
    handleFAQToggle,
    handleQuickLinkPress,
    handleOrderCardPress,
    handleServiceRequestCardPress,
    handleViewAllOrdersPress,
    handleViewAllServiceRequestsPress,
    handleViewAllFaqsPress,
    handleViewAllPress,
    handleLetsChatPress,
    handleScroll,
    helpFaqConfig,
    isOrderLoading,
    isServiceRequestLoading,
    orderBlockVisible,
    serviceRequestBlockVisible,
  } = useJMHelpScreenController(props);

  const fadeAnim = useRef(new Animated.Value(0)).current;
  const config = useConfigFile(JMConfigFileName.JMOrdersConfigurationFileName);

  const srDetails = getItemByViewType('service_request', helpFaqConfig);
  const orderDetails = getItemByViewType('order_card', helpFaqConfig);
  const quickLinkDetails = getItemByViewType('quick_links', helpFaqConfig);

  const fadeAnimSR = useRef(new Animated.Value(0)).current;

  const fadeAnimOrder = useRef(new Animated.Value(0)).current;

  console.log('srDetails', srDetails);

  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: showFloatingButton ? 1 : 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, [showFloatingButton, fadeAnim]);

  useEffect(() => {
    if (visible) {
      Animated.timing(fadeAnimSR, {
        toValue: 1,
        duration: 300,
        easing: Easing.out(Easing.ease),
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(fadeAnimSR, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [visible]);

  useEffect(() => {
    if (orderVisible) {
      Animated.timing(fadeAnimOrder, {
        toValue: 1,
        duration: 300,
        easing: Easing.out(Easing.ease),
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(fadeAnimOrder, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [orderVisible]);

  const renderOrderCard = (item: any) => {
    const display_status = item?.grouping
      ? item?.display_status
      : item?.item_details[0]?.display_status;
    let header = {
      ...config?.Orders?.card?.header,
      title: {
        text: display_status?.header_status,
        style: {
          color: display_status?.color,
        },
        ...config?.Orders?.card?.header?.title,
      },
      subTitle: {
        text: item?.purchased_date,
        ...config?.Orders?.card?.header?.subTitle,
      },
    };
    let listItem = [];

    const image =
      config?.Orders?.card?.verticalCode?.includes(
        item?.vertical_code?.toLowerCase(),
      ) &&
      !item?.is_3p &&
      !config?.Orders?.card?.channelType?.includes(
        item?.channel_type?.toLowerCase(),
      )
        ? config?.Orders?.card?.smartBazzarImage
        : {};

    if (item?.grouping) {
      const child = item?.item_details.reduce((acc: any[], curr: any) => {
        if (!acc.find((item: any) => item?.skucode === curr?.skucode)) {
          acc.push(curr);
        }
        return acc;
      }, []);
      listItem.push({
        productStatus: {
          text: item?.display_status?.header_status,
        },
        productTitle: {
          text: capitalizeFirstLetter(item?.vertical_code),
        },
        productSubTitle: {
          ...config?.Orders?.card?.grouping?.productSubTitle,
          text: config?.Orders?.card?.grouping?.productSubTitle?.text?.replace(
            '[ITEM_LENGTH]',
            item?.item_details?.length,
          ),
        },
        child,
        qty: child?.length === 1 ? item?.item_details?.length : null,
        image,
      });
    } else {
      item?.item_details?.map?.((list: any) => {
        listItem.push({
          child: [list],
          productTitle: {
            text: list?.product_name,
          },
          productStatus: {
            text: item?.display_status?.header_status,
          },
          image,
        });
      });
    }
    return (
      <JMHelpOrderCard
        style={styles.orderCard}
        {...header}
        item={listItem}
        onPress={() => handleOrderCardPress(item.order_id)}
      />
    );
  };

  const renderServiceRequestCard = (serviceRequest: any) => {
    console.warn('serviceRequest passed--', JSON.stringify(serviceRequest));

    let header = {
      taskTitle: serviceRequest?.taskTitle ?? '',
      ticketId: `Request ID: ${serviceRequest?.ticketId ?? ''}`,
      badgeTitle: serviceRequest?.status === 'Pending' ? 'Active' : 'Resolved',
      badgeColor:
        serviceRequest?.status === 'Pending' ? 'primary_60' : 'sparkle_60',
      date: `Created: ${formatDateFromString(serviceRequest?.date ?? '')}`,
      subPrefixTitle: 'Follow up date: ',
      subTitleDate: formatDateFromString(serviceRequest?.lastFollowUp ?? ''),
    };

    return (
      <JMHelpServiceRequestCard
        style={{paddingHorizontal: 24}}
        {...header}
        onPress={() =>
          handleServiceRequestCardPress(
            serviceRequest.ticketId,
            srDetails?.serviceRequestDetailCta,
          )
        }
      />
    );
  };

  const renderQuickLink = (link: QuickLink) => (
    console.warn('link passed--', JSON.stringify(link)),
    (
      <TouchableOpacity
        key={link.title + '--' + link?.iconName}
        style={styles.quickLinkItem}
        onPress={() => handleQuickLinkPress(link?.cta)}>
        <View style={styles.quickLinkIconContainer}>
          <JioIcon ic={link?.iconName as any} size={IconSize.MEDIUM} />
        </View>
        <JioText
          style={{textAlign: 'center'}}
          text={link?.title}
          color={'primary_grey_80'}
          appearance={JioTypography.BODY_XXS}
        />
      </TouchableOpacity>
    )
  );

  const renderTab = (tab: TabType) => (
    <TouchableOpacity
      key={tab}
      style={[styles.tab, selectedTab === tab && styles.selectedTab]}
      onPress={() => handleTabPress(tab)}>
      <Text
        style={[styles.tabText, selectedTab === tab && styles.selectedTabText]}>
        {tab}
      </Text>
    </TouchableOpacity>
  );

  const renderFAQ = (faq: FAQ) => (
    <View key={faq.id} style={styles.faqItem}>
      <TouchableOpacity
        style={styles.faqQuestion}
        onPress={() => handleFAQToggle(faq.id)}>
        <JioText
          style={styles.itemSection}
          text={faq.question}
          color={'primary_grey_80'}
          appearance={JioTypography.BODY_XS_LINK}
        />

        <Text style={styles.chevronIcon}>{faq.isExpanded ? '⌄' : '›'}</Text>
      </TouchableOpacity>
      {faq.isExpanded && (
        <View style={styles.faqAnswer}>
          <JioText
            style={styles.itemSection}
            text={faq.answer}
            color={'primary_grey_80'}
            appearance={JioTypography.BODY_XS}
          />
        </View>
      )}
    </View>
  );

  return (
    <DeeplinkHandler
      navigationBean={navigationBean}
      navigation={navigation}
      children={bean => (
        <ScreenSlot
          navigationBean={bean}
          navigation={navigation}
          children={() => (
            <View style={styles.container}>
              <ScrollView
                style={styles.scrollView}
                showsVerticalScrollIndicator={false}
                onScroll={handleScroll}
                scrollEventThrottle={16}>
                {/* Header Section */}

                {serviceRequestBlockVisible && (
                  <View style={styles.headerSection}>
                    <View style={{flexDirection: 'row', alignItems: 'center'}}>
                      <JioText
                        style={styles.itemSection}
                        text={srDetails?.title ?? 'Service Requests'}
                        color={'primary_grey_100'}
                        appearance={JioTypography.HEADING_XXS}
                      />
                      {isServiceRequestLoading && (
                        <ActivityIndicator
                          size="small"
                          color="#999" // or any theme color
                          style={{marginLeft: 8}} // space between text and loader
                        />
                      )}
                    </View>
                    {(serviceRequestData || serviceRequestData?.length > 0) && (
                      <Animated.View style={[{opacity: fadeAnimSR}]}>
                        <ScrollView
                          horizontal
                          showsHorizontalScrollIndicator={false}
                          style={styles.ordersScrollView}
                          contentContainerStyle={styles.ordersContainer}>
                          {serviceRequestData
                            ?.slice(0, 3)
                            ?.map(renderServiceRequestCard)}
                        </ScrollView>

                        <JioButton
                          title={srDetails?.viewAllCta.buttonText ?? 'View all'}
                          stretch={false}
                          size={ButtonSize.SMALL}
                          style={{marginLeft: 24, marginBottom: 12}}
                          onClick={() =>
                            handleViewAllServiceRequestsPress(
                              srDetails?.viewAllCta,
                            )
                          }
                          kind={ButtonKind.SECONDARY}
                        />
                      </Animated.View>
                    )}
                  </View>
                )}

                {orderBlockVisible && (
                  <View style={styles.headerSection}>
                    <View style={{flexDirection: 'row', alignItems: 'center'}}>
                      <JioText
                        style={styles.itemSection}
                        text={orderDetails?.title ?? 'Help with recent orders?'}
                        color={'primary_grey_100'}
                        appearance={JioTypography.HEADING_XXS}
                      />
                      {isOrderLoading && (
                        <ActivityIndicator
                          size="small"
                          color="#999" // or any theme color
                          style={{marginLeft: 8}} // space between text and loader
                        />
                      )}
                    </View>
                    {(orderData || orderData?.length > 0) && (
                      <Animated.View style={[{opacity: fadeAnimOrder}]}>
                        <ScrollView
                          horizontal
                          showsHorizontalScrollIndicator={false}
                          style={styles.ordersScrollView}
                          contentContainerStyle={styles.ordersContainer}>
                          {orderData.map(renderOrderCard)}
                        </ScrollView>

                        <JioButton
                          title={
                            orderDetails?.viewAllCta.buttonText ?? 'View all'
                          }
                          stretch={false}
                          size={ButtonSize.SMALL}
                          style={{marginLeft: 24, marginBottom: 12}}
                          onClick={() =>
                            handleViewAllOrdersPress(orderDetails?.viewAllCta)
                          }
                          kind={ButtonKind.SECONDARY}
                        />
                      </Animated.View>
                    )}
                  </View>
                )}

                {/* Quick Links Section */}
                <View style={styles.quickLinksSection}>
                  <JioText
                    style={styles.itemSection}
                    text={
                      quickLinkDetails?.title ?? 'Here are some quick links'
                    }
                    color={'primary_grey_100'}
                    appearance={JioTypography.HEADING_XXS}
                  />
                  <JioText
                    style={styles.itemSectionSubtitle}
                    text={
                      quickLinkDetails?.subTitle ??
                      'Track your orders, returns, refunds, and others.'
                    }
                    color={'primary_grey_80'}
                    appearance={JioTypography.BODY_XXS}
                  />

                  <ScrollView
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    style={styles.quickLinksScrollView}
                    contentContainerStyle={styles.quickLinksContainer}>
                    {quickLinkDetails?.items?.map(renderQuickLink)}
                  </ScrollView>
                </View>

                {/* More Help Section */}
                <View style={styles.moreHelpSection}>
                  <JioText
                    style={styles.itemSection}
                    text="Need more help?"
                    color={'primary_grey_100'}
                    appearance={JioTypography.HEADING_XXS}
                  />

                  {/* Tabs */}
                  <View style={styles.tabsContainer}>
                    {tabs.map(renderTab)}
                  </View>

                  {/* FAQs */}
                  <View style={styles.faqsContainer}>
                    {getCurrentFAQs().map(renderFAQ)}
                  </View>

                  <JioButton
                    title="View all"
                    stretch={false}
                    size={ButtonSize.SMALL}
                    style={{margin: 24}}
                    onClick={handleViewAllPress}
                    kind={ButtonKind.SECONDARY}
                  />
                </View>

                {/* Add bottom padding to ensure content is not hidden behind floating button */}
                <View style={styles.bottomPadding} />
              </ScrollView>

              {/* Floating Chat Button */}
              <Animated.View
                style={[
                  styles.floatingChatButtonContainer,
                  {
                    opacity: fadeAnim,
                    transform: [
                      {
                        scale: fadeAnim.interpolate({
                          inputRange: [0, 1],
                          outputRange: [0.8, 1],
                        }),
                      },
                    ],
                  },
                ]}
                pointerEvents={showFloatingButton ? 'auto' : 'none'}>
                <TouchableOpacity
                  style={styles.floatingChatButton}
                  onPress={handleLetsChatPress}
                  activeOpacity={0.8}>
                  <Text style={styles.floatingChatButtonText}>
                    💬 Let's Chat
                  </Text>
                </TouchableOpacity>
              </Animated.View>
            </View>
          )}
        />
      )}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  headerSection: {
    rowGap: 12,
    paddingTop: 16,
  },

  itemSectionSubtitle: {
    paddingHorizontal: 24,
    paddingBottom: 12,
  },
  itemSection: {
    paddingHorizontal: 24,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 20,
  },
  ordersScrollView: {
    marginBottom: 15,
    paddingHorizontal: 24,
  },
  ordersContainer: {
    paddingRight: 20,
  },
  orderCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 16,
    marginVertical: 4,
    elevation: 2,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowOffset: {width: 0, height: 2},
    shadowRadius: 4,
  },
  orderCardContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  orderCardLeft: {
    flex: 1,
  },
  statusTag: {
    alignSelf: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginBottom: 10,
  },
  statusText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  orderInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  orderImage: {
    width: 50,
    height: 50,
    borderRadius: 8,
    marginRight: 12,
  },
  orderDetails: {
    flex: 1,
  },
  orderDescription: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 4,
  },
  storeName: {
    fontSize: 14,
    color: '#666666',
  },
  arrowIcon: {
    fontSize: 24,
    color: '#CCCCCC',
    marginLeft: 10,
  },
  viewAllButton: {
    alignSelf: 'flex-start',
    paddingHorizontal: 24,
    marginHorizontal: 24,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  viewAllText: {
    fontSize: 14,
    color: '#666666',
    fontWeight: '500',
  },
  quickLinksGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickLinkCard: {
    width: '48%',
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#E9ECEF',
  },
  moreHelpSection: {
    //padding: 20,
    paddingTop: 16,
    rowGap: 12,
  },
  tabsContainer: {
    flexDirection: 'row',
    marginBottom: 20,
    backgroundColor: '#F5F5F5',
    paddingLeft: 24,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 20,
    alignItems: 'center',
  },
  selectedTab: {
    backgroundColor: '#0078AD',
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#0C5273',
  },
  selectedTabText: {
    color: '#FFFFFF',
  },
  faqsContainer: {
    marginBottom: 20,
    paddingLeft: 24,
    paddingRight: 24,
  },
  faqItem: {
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
    paddingVertical: 16,
  },
  faqQuestion: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  faqQuestionText: {
    fontSize: 16,
    color: '#000000',
    flex: 1,
    marginRight: 10,
  },
  chevronIcon: {
    fontSize: 18,
    color: '#CCCCCC',
  },
  faqAnswer: {
    marginTop: 12,
    paddingLeft: 0,
  },
  faqAnswerText: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  bottomPadding: {
    height: 100, // Adjust as needed to prevent content from being hidden
  },
  floatingChatButton: {
    backgroundColor: '#28A745',
    borderRadius: 25,
    paddingVertical: 16,
    paddingHorizontal: 24,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 5,
    elevation: 8,
    zIndex: 1000,
  },
  floatingChatButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  floatingChatButtonContainer: {
    position: 'absolute',
    bottom: 20,
    right: 20,
  },

  quickLinksSection: {
    paddingTop: 10,
    paddingBottom: 16,
    rowGap: 8,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 20,
  },
  quickLinksScrollView: {
    marginHorizontal: 20, // Extend to screen edges
  },
  quickLinksContainer: {
    //paddingHorizontal: 20,
    //paddingRight: 40, // Extra padding for last item
  },
  quickLinkItem: {
    alignItems: 'center',
    marginRight: 24, // Horizontal spacing between items
    maxWidth: 65, // Minimum width to ensure consistent spacing
    width: 65,
  },
  quickLinkIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20, // Perfect circle
    backgroundColor: '#E5F1F7', // Solid blue background
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
    shadowColor: '#E5F1F7',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  quickLinkIcon: {
    fontSize: 20,
    color: '#FFFFFF', // White icon color
  },
  quickLinkLabel: {
    fontSize: 12,
    fontWeight: '500',
    color: '#000000',
    textAlign: 'center',
    maxWidth: 80, // Prevent text from being too wide
    lineHeight: 16,
  },
});

export default JMHelpScreen;
