import { StyleSheet } from "react-native";

export const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: 8,
        width: '100%',
        position: 'relative',
    },
    leftSection: {
        flexDirection: 'row',
        alignItems: 'center',
        flex: 1,
    },
    leftText: {
        marginRight: 2,
        marginLeft: 10
    },
    rightSection: {
        flexDirection: 'row',
        justifyContent: 'flex-end',
        flex: 1.4,
    },
    badgeButton: {
        flexDirection: 'row',
        alignItems: 'center',
        marginHorizontal: 4,
        paddingVertical: 6,
        paddingHorizontal: 12,
        borderRadius: 16,
        borderWidth: 1,
        borderColor: '#aaa',
        backgroundColor: '#fff',
    },
    badgeButtonText: {
        marginRight: 4,
        marginLeft: 5
    },
    badge: {
        justifyContent: 'center',
        alignItems: 'center',
    },
    dot: {
        width: 6,
        height: 6,
        borderRadius: 3,
        backgroundColor: '#f00',
    },
    circle: {
        width: 16,
        height: 16,
        borderRadius: 8,
        backgroundColor: '#f00',
    },
    badgeText: {
        textAlign: 'center',
    },
    scrim: {
        position: 'absolute',
        top: 0,
        height: 46,
        width: '100%',
        backgroundColor: 'rgba(18,18,18,0.22)',
    },
    badgeButtonDisabled: {
        opacity: 0.5,
    },
});