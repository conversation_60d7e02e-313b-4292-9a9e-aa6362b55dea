import {requireNativeComponent, StyleSheet, View} from 'react-native';
import React from 'react';
import useJioAds from './controllers/useJioAds';
import {
  JioAdsProps,
  JioADsNativeComponentIOSProps,
  JioADsNativeComponentAndroidProps,
} from './types/JioAds';
import {getScreenDim} from '../../../jiomart-common/src/JMResponsive';
import {JioText} from '@jio/rn_components';
import {JioTypography} from '@jio/rn_components/src/index.types';

const JioADsNativeComponent = requireNativeComponent<
  JioADsNativeComponentIOSProps | JioADsNativeComponentAndroidProps
>('JioADsNativeComponent');

const JioAds = (props: JioAdsProps) => {
  const {
    adType,
    adSpotId,
    adHeight,
    adWidth,
    adjHeight,
    adjWidth,
    style,
    nativeStyle,
    HeaderComponent,
    FooterComponent,
  } = props;
  const {adSuccess, adMetaData, handleAdEvent} = useJioAds(props);

  if (!adSuccess && adSuccess != null) {
    return null;
  }

  console.log('jioAds re-render');

  return (
    <>
      {React.isValidElement(HeaderComponent) &&
        adSuccess &&
        React.cloneElement(HeaderComponent)}
      <View
        style={[
          style,
          {
            height: adjHeight,
            width: !adjWidth ? Math.floor(getScreenDim?.width) : adjWidth,
          },
        ]}>
        <View style={styles.tag}>
          <JioText
            text="Ad"
            color={'primary_grey_80'}
            appearance={JioTypography.BODY_XS_BOLD}
          />
        </View>
        <JioADsNativeComponent
          style={[styles.container, nativeStyle]}
          data={{
            adType,
            adspotKey: adSpotId,
            adHeight,
            adWidth,
            adCustomWidth: !adjWidth ? getScreenDim?.width : adjWidth,
            adCustomHeight: adjHeight,
            adMetaData: adMetaData,
          }}
          onChange={handleAdEvent}
        />
      </View>
      {React.isValidElement(FooterComponent) &&
        adSuccess &&
        React.cloneElement(FooterComponent)}
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 24,
  },
  tag: {
    position: 'absolute',
    top: 0,
    right: 0,
    zIndex: 1,
    paddingHorizontal: 8,
    paddingVertical: 2,
    backgroundColor: '#ffffff',
    borderBottomLeftRadius: 12,
    borderBottomStartRadius: 12,
    opacity: 0.65,
  },
});

export default JioAds;
