import {FlatList, Pressable, StyleSheet, View} from 'react-native';
import FastImage from 'react-native-fast-image';
import {JioText} from '@jio/rn_components';
import {JioTypography} from '@jio/rn_components/src/index.types';
import {SliderDetails} from '../../../jiomart-home/src/utils/HomePageTypes';
import {
  navBeanObj,
  NavigationType,
} from '../../../jiomart-common/src/JMNavGraphUtil';
import React, {useCallback, useMemo} from 'react';
import isEqual from 'lodash.isequal';
import { rw } from '../../../jiomart-common/src/JMResponsive';

interface ShopByVerticalBannerProps {
  data: SliderDetails[];
  itemWidth?: number;
  onClick: any;
  hideTitle?: boolean;
  title?: string;
}

// Custom comparison function for React.memo
const areEqual = (
  prevProps: ShopByVerticalBannerProps,
  nextProps: ShopByVerticalBannerProps,
) => {
  return (
    isEqual(prevProps.data, nextProps.data) &&
    prevProps.itemWidth === nextProps.itemWidth &&
    prevProps.onClick === nextProps.onClick &&
    prevProps.hideTitle === nextProps.hideTitle &&
    prevProps.title === nextProps.title
  );
};

// Memoized renderItem component
const RenderItem = React.memo<{
  item: any;
  itemWidth: number;
  onClick: any;
  hideTitle: boolean;
}>(
  ({item, itemWidth, onClick, hideTitle}) => {
    const handlePress = useCallback(() => {
      item.url &&
        onClick(
          navBeanObj({
            source: '',
            destination: 'CommonWebViewScreen',
            actionType: 'T003',
            headerType: 9,
            headerVisibility: 2,
            navigationType: NavigationType.PUSH,
            actionUrl: item.url,
            loginRequired: false,
          }),
        );
    }, [item.url, onClick]);

    const itemStyle = useMemo(
      () => [styles.itemContainer, {width: itemWidth}],
      [itemWidth],
    );

    const imageStyle = useMemo(
      () => [styles.image, {width: itemWidth, height: itemWidth}],
      [itemWidth],
    );

    const maxLines = useMemo(
      () => (item.name && item.name.trim().split(/\s+/).length > 1 ? 2 : 1),
      [item.name],
    );

    return (
      <Pressable key={item.url} onPress={handlePress} style={itemStyle}>
        <FastImage
          source={{
            uri: item.image,
            //   uri: `${getBaseURL()}/images/product/original/${item.image_path}`,
          }}
          style={imageStyle}
          resizeMode={FastImage.resizeMode.cover}
        />
        {!hideTitle && (
          <JioText
            appearance={JioTypography.BODY_XXS}
            text={item.name}
            maxLines={maxLines}
            style={styles.title}
            color="primary_grey_80"
          />
        )}
      </Pressable>
    );
  },
  (prevProps, nextProps) => {
    return (
      isEqual(prevProps.item, nextProps.item) &&
      prevProps.itemWidth === nextProps.itemWidth &&
      prevProps.onClick === nextProps.onClick &&
      prevProps.hideTitle === nextProps.hideTitle
    );
  },
);

RenderItem.displayName = 'RenderItem';

const ShopByVericalBanner: React.FC<ShopByVerticalBannerProps> = ({
  data,
  itemWidth = 60,
  onClick,
  hideTitle = false,
  title = '',
}) => {
  // Memoize renderItem function for FlatList
  const renderItem = useCallback(
    ({item}: {item: any}) => (
      <RenderItem
        item={item}
        itemWidth={rw(itemWidth)}
        onClick={onClick}
        hideTitle={hideTitle}
      />
    ),
    [itemWidth, onClick, hideTitle],
  );

  // Memoize keyExtractor
  const keyExtractor = useCallback(
    (item: any, index: number) => item.url || item.id || index.toString(),
    [],
  );

  console.log('shop by vertical banner re-render');

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <JioText
          text={title}
          color="black"
          appearance={JioTypography.HEADING_XXS}
          style={styles.headtitle}
        />
      </View>
      <FlatList
        data={data}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={[
          styles.flatListContent,
          !hideTitle ? {columnGap: 8} : null,
        ]}
        // removeClippedSubviews={true}
        // maxToRenderPerBatch={5}
        // windowSize={10}
        // initialNumToRender={3}
      />
    </View>
  );
};

export default React.memo(ShopByVericalBanner, areEqual);

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
  },
  flatListContent: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  itemContainer: {},
  image: {
    borderRadius: 8,
  },
  title: {
    marginTop: 4,
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignContent: 'center',
  },
  headtitle: {
    paddingLeft: 16,
    paddingBottom: 12,
  },
});
