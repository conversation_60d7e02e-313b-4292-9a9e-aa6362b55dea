import React, {useCallback, useMemo} from 'react';
import {StyleSheet, View, Pressable} from 'react-native';
import {SliderDetails} from '../../../jiomart-home/src/utils/HomePageTypes';
import {
  navBeanObj,
  NavigationType,
} from '../../../jiomart-common/src/JMNavGraphUtil';
import {JMSliderTypeName} from '../../../jiomart-home/src/types/JMHomeComponentType';
import {
  extractJioAdsData,
  getAdHeight,
} from '../../../jiomart-home/src/utils/HomeUtils';
import JioAds from '../../../jiomart-general/src/JioAds/JioAds';
import {useEffect, useState} from 'react';
import CustomCarousel from './CustomCarousel';
import {JioText} from '@jio/rn_components';
import {JioTypography} from '@jio/rn_components/src/index.types';
import LazyFastImage from './LazyFastImage';
import {getImageHeight} from '../../../jiomart-common/src/utils/JMImageUtility';
import {checkAdStatusNB} from '../../../jiomart-general/src/bridge/JMRNBridge';
import {getScreenDim} from '../../../jiomart-common/src/JMResponsive';
import isEqual from 'lodash.isequal';

interface SlimBannerProps {
  images: SliderDetails[];
  paddingVertical?: number;
  paddingHorizontal?: number;
  borderRadius?: number;
  imagePadding?: number;
  onClick: any;
  title?: string;
  peekValue?: number;
  adMetaData?: any;
  navigation?: any;
}

function useCarouselHeight(data: any[]) {
  const [carouselHeight, setCarouselHeight] = useState<number>(0);

  useEffect(() => {
    if (!data || data.length === 0) return;

    const calculate = async () => {
      const heightPromises = data.map(async item => {
        try {
          if (item.slide_type_name === JMSliderTypeName.IMAGE && item.image) {
            return await getImageHeight(item.image);
          }
          if (item.slide_type_name === JMSliderTypeName.JIO_ADS) {
            return getAdHeight(item?.img_alt);
          }
        } catch (err) {
          console.warn('Skipping item with error:', err);
        }
        return 0;
      });

      const heights = await Promise.all(heightPromises);
      const validHeights = heights.filter(h => h > 0);
      if (validHeights.length > 0) {
        setCarouselHeight(Math.max(...validHeights));
      }
    };

    calculate();
  }, [data]);

  return carouselHeight;
}

// Custom comparison function for React.memo
const areEqual = (prevProps: SlimBannerProps, nextProps: SlimBannerProps) => {
  return (
    isEqual(prevProps.images, nextProps.images) &&
    prevProps.paddingVertical === nextProps.paddingVertical &&
    prevProps.paddingHorizontal === nextProps.paddingHorizontal &&
    prevProps.borderRadius === nextProps.borderRadius &&
    prevProps.imagePadding === nextProps.imagePadding &&
    prevProps.onClick === nextProps.onClick &&
    prevProps.title === nextProps.title &&
    prevProps.peekValue === nextProps.peekValue
  );
};

const SlimBanner: React.FC<SlimBannerProps> = (props: SlimBannerProps) => {
  const {
    images,
    paddingHorizontal = 12,
    borderRadius = 16,
    imagePadding = 0,
    peekValue,
    adMetaData,
    navigation
  } = props;

  // Memoize handlePress to prevent re-creation on every render
  const handlePress = useCallback(
    (url: string) => {
      console.log('Pressed URL:', url);
      url &&
        props?.onClick(
          navBeanObj({
            source: '',
            destination: 'CommonWebViewScreen',
            actionType: 'T003',
            headerType: 9,
            headerVisibility: 2,
            navigationType: NavigationType.PUSH,
            actionUrl: url,
            loginRequired: false,
          }),
        );
    },
    [props],
  );

  const [filteredData, setFilteredData] = useState<SliderDetails[]>([]);
  const carouselHeight = useCarouselHeight(filteredData);

  const filterAdsFromRenderItemData = useCallback(
    async (items: SliderDetails[]) => {
      const results = await Promise.all(
        items.map(async item => {
          if (item?.slide_type_name === JMSliderTypeName.JIO_ADS) {
            const adsData = extractJioAdsData(item?.img_alt);
            const isValid = await checkAdStatusNB({
              adType: adsData.adType,
              adspotKey: adsData.adSpotId,
              adHeight: adsData.adHeight,
              adWidth: adsData.adWidth,
              adCustomHeight: adsData.adjHeight,
              adCustomWidth: 0,
              adMetaData: {...adsData.adMetaData, ...adMetaData},
            });
            return isValid ? item : null;
          }
          return item;
        }),
      );
      return results.filter((item): item is SliderDetails => item !== null);
    },
    [],
  );

  const dynamicStyles = StyleSheet.create({
    container: {
      backgroundColor: 'white',
      paddingHorizontal,
      height: carouselHeight,
    },
    image: {
      width: '100%',
      height: '100%',
      borderRadius: borderRadius,
      // backgroundColor: 'grey',
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignContent: 'center',
    },
    title: {
      paddingLeft: 16,
      paddingBottom: 12,
      // fontWeight: '900',
    },
  });

  const renderItem = useCallback(
    ({item, index}: {item: SliderDetails; index: number}) => {
      switch (item?.slide_type_name) {
        case JMSliderTypeName.JIO_ADS:
          const adsData = extractJioAdsData(item?.img_alt);
          return (
            <JioAds
              {...adsData}
              adMetaData={{
                ...adsData.adMetaData,
                ...adMetaData,
              }}
              navigation={navigation}
            />
          );
        case JMSliderTypeName.IMAGE:
          return (
            <Pressable
              onPress={() => handlePress(item.url)}
              key={item.id}
              style={{
                width:
                  peekValue && peekValue > 0 ? '100%' : getScreenDim?.width,
                padding: imagePadding,
                borderRadius: borderRadius,
                height: carouselHeight,
              }}>
              <LazyFastImage
                //source={{uri: item.image}}
                imageUrl={item.image}
                style={[dynamicStyles.image]}
                resizeMode="cover"
              />
            </Pressable>
          );
        default:
          return null;
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      adMetaData,
      borderRadius,
      carouselHeight,
      dynamicStyles.image,
      imagePadding,
      peekValue,
    ],
  );

  const customStyles = useMemo(
    () => ({
      itemList: {
        backgroundColor: 'white',
        gap: 10,
      },
      indicatorList: {},
      indicatorItemList: {},
    }),
    [],
  );

  // Effect to handle async filtering of ads
  useEffect(() => {
    if (images && images.length > 0) {
      filterAdsFromRenderItemData(images).then(setFilteredData);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [images]);

  if (
    carouselHeight === 0 ||
    !filteredData ||
    (Array.isArray(filteredData) && filteredData.length === 0)
  ) {
    return null;
  }

  console.log('slim banner re-render');

  return (
    <View style={{paddingVertical: 8}}>
      <View style={dynamicStyles.header}>
        <JioText
          text={props?.title}
          color="black"
          appearance={JioTypography.HEADING_XXS}
          style={dynamicStyles.title}
        />
      </View>
      <View style={dynamicStyles.container}>
        <CustomCarousel
          autoScroll={true}
          showIndicator={false}
          items={filteredData?.map((item, index) => ({item, index}))}
          RenderItem={renderItem}
          customStyles={customStyles}
          peek={peekValue}
        />
      </View>
    </View>
  );
};

export default React.memo(SlimBanner, areEqual);
