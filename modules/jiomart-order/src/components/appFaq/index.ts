// FAQ Component Collection
// Optimized and reusable components for FAQ functionality

// Header Components
export { default as <PERSON>QHeader } from './FAQHeader';

// Navigation Components  
export { default as FAQTabs } from './FAQTabs';

// Content Components
export { FAQAccordion, FAQSection } from './FAQAccordion';
export type { FAQItem } from './FAQAccordion';

// Contact Components
export { ContactSection, ServiceRequestSection } from './ContactSection';
export type { ContactOption } from './ContactSection';

// Re-export main screen for convenience
export { default as JMAppFAQ } from '../screens/JMAppFAQ'; 