package com.jiomart.module

import android.app.Activity
import android.app.PendingIntent
import android.content.*
import android.content.IntentSender
import android.os.Build
import android.util.Log
import com.facebook.react.bridge.*
import com.google.android.gms.auth.api.phone.SmsRetriever
import com.google.android.gms.common.api.CommonStatusCodes
import com.google.android.gms.common.api.Status

class JMSmsRetrieverModule(
    private val reactContext: ReactApplicationContext
) : ReactContextBaseJavaModule(reactContext), ActivityEventListener {

    private var smsPromise: Promise? = null
    private var isReceiverRegistered = false
    private var useRetrieverApi = true
    override fun getName(): String = "JMSmsRetrieverModule"
    init {
        reactContext.addActivityEventListener(this)
    }
    private val smsReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent?.action != SmsRetriever.SMS_RETRIEVED_ACTION) return

            val extras = intent.extras ?: return
            val status = extras.get(SmsRetriever.EXTRA_STATUS) as? Status ?: return

            when (status.statusCode) {
                CommonStatusCodes.SUCCESS -> {
                    if (useRetrieverApi) {
                        val message = extras.getString(SmsRetriever.EXTRA_SMS_MESSAGE).orEmpty()
                        val otp = extractOtp(message)
                        smsPromise?.resolve(otp)
                        cleanup()
                    } else {
                        // Consent Intent is directly an Intent, not PendingIntent
                        val consentIntent: Intent? = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                            extras.getParcelable(SmsRetriever.EXTRA_CONSENT_INTENT, Intent::class.java)
                        } else {
                            @Suppress("DEPRECATION")
                            extras.getParcelable(SmsRetriever.EXTRA_CONSENT_INTENT)
                        }

                        if (consentIntent != null) {
                            try {
                                currentActivity?.startActivityForResult(consentIntent, SMS_CONSENT_REQUEST)
                            } catch (e: Exception) {
                                smsPromise?.reject("ERROR_CONSENT_LAUNCH_FAILED", e.message)
                                cleanup()
                            }
                        } else {
                            smsPromise?.reject("ERROR_NO_CONSENT_INTENT", "Consent intent was null")
                            cleanup()
                        }
                    }
                }

                CommonStatusCodes.TIMEOUT -> {
                    smsPromise?.reject("ERROR_TIMEOUT", "SMS retrieval timed out")
                    cleanup()
                }
            }
        }
    }

    @ReactMethod
    fun startSmsListener(enableRetriever: Boolean, promise: Promise) {
        val activity = currentActivity
        if (activity == null) {
            promise.reject("NO_ACTIVITY", "Activity doesn't exist")
            return
        }

        smsPromise = promise
        useRetrieverApi = enableRetriever
        Log.d("startSmsListener ", "enableRetriever "+enableRetriever + activity)
        val client = SmsRetriever.getClient(activity)

        val task = if (enableRetriever) {
            client.startSmsRetriever()
        } else {
            client.startSmsUserConsent(null) // or provide sender phone number if known
        }

        task.addOnSuccessListener {
            Log.d("startSmsListener ", "registerReceiver ")
            registerReceiver()
        }.addOnFailureListener {
            Log.d("startSmsListener ", "registerReceiver failed")
            promise.reject("START_FAILED", "Failed to start SMS retriever or consent")
        }
    }

    override fun onActivityResult(activity: Activity?, requestCode: Int, resultCode: Int, data: Intent?) {
        if (requestCode == SMS_CONSENT_REQUEST && resultCode == Activity.RESULT_OK && data != null) {
            Log.d("startSmsListener ", "registerReceiver onActivityResult")
            val message = data.getStringExtra(SmsRetriever.EXTRA_SMS_MESSAGE) ?: ""
            val otp = extractOtp(message)
            smsPromise?.resolve(otp)
        } else {
            Log.d("startSmsListener ", "registerReceiver onActivityResult fail ")
            smsPromise?.reject("CONSENT_DENIED", "User denied SMS read permission")
        }
        cleanup()
    }

    override fun onNewIntent(intent: Intent?) {}

    private fun extractOtp(message: String): String {
        return message.filter { it.isDigit() }.take(6)
    }

    private fun registerReceiver() {
        if (isReceiverRegistered) return
        val intentFilter = IntentFilter(SmsRetriever.SMS_RETRIEVED_ACTION)

        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                currentActivity?.registerReceiver(
                    smsReceiver,
                    intentFilter,
                    SmsRetriever.SEND_PERMISSION,
                    null,
                    Context.RECEIVER_EXPORTED
                )
            } else {
                currentActivity?.registerReceiver(smsReceiver, intentFilter)
            }
            isReceiverRegistered = true
        } catch (e: Exception) {
            smsPromise?.reject("RECEIVER_FAILED", "Receiver registration failed: ${e.message}")
        }
    }

    private fun cleanup() {
        if (isReceiverRegistered) {
            try {
                currentActivity?.unregisterReceiver(smsReceiver)
            } catch (_: Exception) {}
        }
        isReceiverRegistered = false
        smsPromise = null
    }

    companion object {
        private const val SMS_CONSENT_REQUEST = 1001
    }
}
