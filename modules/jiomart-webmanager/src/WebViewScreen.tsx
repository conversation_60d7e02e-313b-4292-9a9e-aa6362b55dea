import {JioIcon} from '@jio/rn_components';
import {IconColor, IconSize} from '@jio/rn_components/src/index.types';
import {useColor} from '@jio/rn_components/src/theme/color/useColor';
import LottieView from 'lottie-react-native';
import {useEffect, useState} from 'react';
import {Platform, Pressable, StyleSheet, View} from 'react-native';
import {
  AppScreens,
  type ScreenProps,
} from '../../jiomart-common/src/JMAppScreenEntry';
import {rh, rw} from '../../jiomart-common/src/JMResponsive';
import {JMSharedViewModel} from '../../jiomart-common/src/JMSharedViewModel';
import {useGlobalState} from '../../jiomart-general/src/context/JMGlobalStateProvider';
import ScreenSlot, {
  createEventFinalBean,
  DeeplinkHandler,
  SendWebJavaScriptFunctions,
} from '../../jiomart-general/src/ui/JMScreenSlot';
import SortFilterBar from '../../jiomart-general/src/ui/SortFilterBar/SortFilterBar';
import deeplinkAnimationAssetData from '../../jiomart-main/src/features/Splash/assets/jiomart_deeplink_animation.json';
import {getBaseURL} from '../../jiomart-networkmanager/src/JMEnvironmentConfig';
import WebViewUtility from './WebViewUtility';
import useWebViewController from './useWebViewController';

export type JProps = ScreenProps<typeof AppScreens.COMMON_WEB_VIEW>;

const CommonWebViewScreen = (props: JProps) => {
  const {
    navigation,
    navigationBean,
    eventTriggerBean,
    webState,
    webViewRef,
    statusBarColor,
    handleJSEvents,
    onUrlOverride,
    onError,
    handleBackPress,
    handleNavigationStateChange,
    screenSpecificQcMessage,
    scrollBnb,
    handleWebViewLoadEnd,
    webViewKey,
    isScrollUpFabVisible,
    isSortFilterBarVisible,
    sortFilterBarData,
    setIsScrollUpFabVisible,
  } = useWebViewController(props);

  const {setEvent} = useGlobalState();

  const [isDeeplinkAnimationVisible, setDeeplinkAnimationVisible] = useState(
    JMSharedViewModel.Instance.getShowAnimationOnDeeplink() === 2,
  );

  const [isSortfilterBarScrim, setIsSortfilterBarScrim] = useState(false);

  useEffect(() => {
    setIsSortfilterBarScrim(false);
  }, [sortFilterBarData]);

  const fabBackgroundColor = useColor('white');
  const fabShadowColor = useColor('primary_grey_80');

  console.log(
    '🚀 ~ CommonWebViewScreen ~ isDeeplinkAnimationVisible:',
    isDeeplinkAnimationVisible,
  );

  const handleFabPress = () => {
    webViewRef?.current?.injectJavaScript(`
      window.scroll({
        top: 0, 
        left: 0, 
        behavior: 'smooth'
      })
    `);
  };

  const webViewMainUi = () => {
    console.log('webState.content', webState.content, scrollBnb);
    return (
      <View
        style={{
          flex: 1,
          flexDirection: 'column',
        }}>
        {isSortFilterBarVisible && (
          <SortFilterBar
            sortApplied={sortFilterBarData.SortApplied}
            filterApplied={sortFilterBarData.FilterAppliedCount}
            isCategoryClickable={true}
            offsetState={'Start'}
            disableBar={isSortfilterBarScrim}
            category={sortFilterBarData.LeftMessage}
            onLeftPress={function (): void {
              setTimeout(() => {
                setIsSortfilterBarScrim(true);
              }, 700);
              setIsScrollUpFabVisible(false);
              SendWebJavaScriptFunctions(
                setEvent,
                navigation,
                'popUpViewClicked',
                {
                  popUpViewClicked: `'${JSON.stringify({
                    PopUpType: 2,
                    ButtonType: 0,
                  })}'`,
                },
              );
            }}
            onSortPress={function (): void {
              setTimeout(() => {
                setIsSortfilterBarScrim(true);
              }, 700);
              setIsScrollUpFabVisible(false);
              SendWebJavaScriptFunctions(
                setEvent,
                navigation,
                'popUpViewClicked',
                {
                  popUpViewClicked: `'${JSON.stringify({
                    PopUpType: 2,
                    ButtonType: 1,
                  })}'`,
                },
              );
            }}
            onFilterPress={function (): void {
              setTimeout(() => {
                setIsSortfilterBarScrim(true);
              }, 700);
              setIsScrollUpFabVisible(false);
              SendWebJavaScriptFunctions(
                setEvent,
                navigation,
                'popUpViewClicked',
                {
                  popUpViewClicked: `'${JSON.stringify({
                    PopUpType: 2,
                    ButtonType: 2,
                  })}'`,
                },
              );
            }}
          />
        )}
        <WebViewUtility
          key={Platform.OS === 'ios' ? String(webViewKey) : 'webview'}
          style={{
            paddingBottom:
              !scrollBnb && eventTriggerBean?.shouldShowBottomNavBar
                ? rw(60)
                : 0,
          }}
          source={{uri: webState.content || getBaseURL() || ''}}
          onMessage={handleJSEvents}
          onRef={ref => {
            webViewRef.current = ref;
          }}
          onError={onError}
          javaScriptEnabled={true}
          onNavigationStateChange={handleNavigationStateChange}
          onShouldStartLoadWithRequest={onUrlOverride}
          onLoadEnd={() => {
            JMSharedViewModel.Instance.setShowAnimationOnDeeplink(0);
            setDeeplinkAnimationVisible(false);
            handleWebViewLoadEnd();
          }}
        />
      </View>
    );
  };

  return (
    <DeeplinkHandler
      navigationBean={navigationBean}
      navigation={navigation}
      children={bean => (
        <View style={styles.container}>
          <ScreenSlot
            navigationBean={createEventFinalBean(bean, eventTriggerBean)}
            navigation={navigation}
            onCustomBackPress={handleBackPress}
            statusBarColor={statusBarColor}
            children={_ => {
              return webViewMainUi();
            }}
            deliverToBarData={{qcMessage: screenSpecificQcMessage}}
            enableOpenPincodeBtmSheetListener={true}
          />

          {isDeeplinkAnimationVisible ? (
            <View style={styles.lottieOverlay}>
              <LottieView
                source={deeplinkAnimationAssetData}
                autoPlay
                loop={true}
                resizeMode="cover"
                style={{width: 150, height: 150}}
              />
            </View>
          ) : null}

          {/* Floating Action Button */}
          {isScrollUpFabVisible ? (
            <Pressable
              style={[
                styles.fab,
                {
                  borderColor: useColor('primary_grey_40'),
                  backgroundColor: fabBackgroundColor,
                  shadowColor: fabShadowColor,
                  bottom: rh(150),
                },
              ]}
              onPress={handleFabPress}
              // android_ripple={{color: 'rgba(27, 25, 148, 0.3)', borderless: false}}
            >
              <JioIcon
                ic="IcArrowUp"
                color={IconColor.GREY100}
                size={IconSize.MEDIUM}
              />
            </Pressable>
          ) : null}
        </View>
      )}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  lottieOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: '#0078AD',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1001, // make sure it's on top
  },
  fab: {
    position: 'absolute',
    right: rw(16),
    width: rw(56),
    height: rw(56),
    borderRadius: rw(28),
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 8, // Android shadow
    borderWidth: 1,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    zIndex: 1000,
  },
});

export default CommonWebViewScreen;
