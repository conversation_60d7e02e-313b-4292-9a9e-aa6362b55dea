import type {UseJMHomeScreenProps} from '../screen/homesection/JMHomeDashboardScreen';
import {useCallback, useEffect, useRef, useState, useMemo} from 'react';
import {AppState} from 'react-native';
import JMHomeNetworkController from '../../../jiomart-networkmanager/src/JMNetworkController/JMHomeNetworkController';
import {useGlobalState} from '../../../jiomart-general/src/context/JMGlobalStateProvider';
import {
  navBeanObj,
  NavigationBean,
  NavigationType,
} from '../../../jiomart-common/src/JMNavGraphUtil';
import {useConfigFile} from '../../../jiomart-general/src/hooks/useJMConfig';
import {JMConfigFileName} from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import usePincodeChange from '../../../jiomart-address/src/hooks/usePincodeChange';
import {JMDatabaseManager} from '../../../jiomart-networkmanager/src/db/JMDatabaseManager';
import {
  DefaultHomeSlug,
  extractLastPathSegment,
  TabSelectionType,
  TOP_CATERORIES_STATES,
  updateHeaderJsonForMoonXHomeApi,
  VerticleTypes,
} from '../utils/HomeSectionUtils';
import {homepageQueryKeys, useHomepageQuery} from '../hooks/useHomepageQuery';
import {useQueryClient} from '@tanstack/react-query';
import JMServiceablityNetworkNewController from '../../../jiomart-networkmanager/src/JMNetworkController/JMServiceablityNetworkNewController';
import {addStringPref, getPrefString} from '../../../jiomart-common/src/JMAsyncStorageHelper';
import {AsyncStorageKeys} from '../../../jiomart-common/src/JMConstants';
import {
  DataResponse,
  HomeTopCategoriesTab,
  PinInfo,
  RegionInfo,
  VerticalDetails,
} from '../../../jiomart-networkmanager/src/models/home/<USER>';
import {
  arrayIntoLowerCase,
  arraysEqual,
} from '../../../jiomart-common/src/utils/JMArrayUtility';
import {JMHomeDBManager} from '../db/JMHomeDBManage';
import useQuickBtmSheet from '../hooks/useQuickBtmSheet';
import type {QuickBottomShetIdentifierBeanType} from '../types/QuickBottomSheetTypes';
import {getQuickBottomsheetTypeIdentifier} from '../../../jiomart-common/src/utils/JMCommonFunctions';
import {navigateTo} from '../../../jiomart-general/src/navigation/JMNavGraph';
import {AppScreens} from '../../../jiomart-common/src/JMAppScreenEntry';
import {getPersistString} from '../utils/HomeUtils';
import {generateDefaultAdMetaData} from '../../../jiomart-general/src/JioAds/utils/adUtility';
import {JMLogger} from '../../../jiomart-common/src/utils/JMLogger';

const homeController = new JMHomeNetworkController();
const serviciablityControllerNew = new JMServiceablityNetworkNewController();

// 1. Add this constant at the top of your file (after imports)
const PREFETCH_SLUGS = ['lowest-price-guarantee', 'mega-deals']; // Add more slugs as needed
const PREFETCH_SLUGS_JOURNEY = ['Quick', 'Scheduled']; // Add more slugs as needed

const useJMHomeScreenController = (props: UseJMHomeScreenProps) => {
  const {route, navigation} = props;
  const navigationBean = route.params;
  const [bean, setBean] = useState<NavigationBean>(navigationBean);
  const commonConfig = useConfigFile(JMConfigFileName.JMCommonContentFileName);
  const homeConfig = useConfigFile(
    JMConfigFileName.JMHomeDashboardConfigurationFileName,
  );
  const newTagImage =
    commonConfig?.quickCommerceConfig?.QuickBottomSheetViewDetails?.[0]
      ?.TopNewIconAsset;
  const {showQuickBtmSheet, openQuickBtmSheet, closeQuickBtmSheet} =
    useQuickBtmSheet();
  const {qcDetails, setQCDetails, selectedOption, setSelectedOption} =
    useGlobalState();
  const [serviceabilityCalled, setServiceabilityCalled] = useState(false);
  const headerJsonRef = useRef<{}>({});
  const fyndPromiseDataRef = useRef<DataResponse | null>(null);
  const [currentSelectedSlug, setCurrentSelectedSlug] = useState<string>();
  const [homepageTabSectionData, setHomepageTabSectionData] =
    useState<HomeTopCategoriesTab[]>();
  const [homepageTabSectionState, setHomepageTabSectionState] = useState<
    TOP_CATERORIES_STATES | undefined
  >(undefined);
  const queryClient = useQueryClient();
  // Flatten all page data into a single array for FlatList
  // const [flattenedData, setFlattenedData] = useState<Array<any>>([]);
  // const processedPagesRef = useRef(0);

  const [qcBannerHomeConditionData, setQcBannerHomeConditionData] = useState({
    city: null,
    verticalCode: [],
    qc: false,
  });
  const [conditionToShowQcBannerHome, setConditionToShowQcBannerHome] =
    useState(false);
  const [isBSNonCancellable, setBSNonCancellable] = useState(false);
  const [quickBottomSheetIdentifierBean, setQuickBottomSheetIdentifierBean] =
    useState<QuickBottomShetIdentifierBeanType | null>(null);

  const showBlockQcBannerHome = useRef(-1);
  const verticleDetailRef = useRef<Record<string, VerticalDetails>>(undefined);
  const verticalCodeRef = useRef<string[]>([]);
  const [activeSpecialIndex, setActiveSpecialIndex] = useState<number | null>(
    null,
  );
  const [isWidgetLoading, setIsWidgetLoading] = useState(false);
  const [appStateChangeCount, setAppStateChangeCount] = useState(0);
  const [adMetaData, setAdMetaData] = useState({
    vertical: '',
    region: '',
  });

  const checkServiceability = async (
    pincode: string,
    invalidateCacheAndRefetch: boolean,
  ) => {
    try {
      addStringPref('homepincode', pincode)
      console.log(
        'checkServiceability ------- pin changed or not',
        invalidateCacheAndRefetch,
      );
      ////// --------------- Check Serviceability -------------------- //////////////////////
      const {pincodeMetaData, fyndPromiseApiResponse} =
        await serviciablityControllerNew.checkServiceabilityWithPincode(
          pincode,
          homeConfig?.serviciablityRefreshIntervalInMin ?? 30,
          invalidateCacheAndRefetch,
        );

      // Only checking for "GROCERIES" verticle at the moment
      const verticals = fyndPromiseApiResponse?.promise_info?.vertical ?? {};
      const qc = Object.values(verticals).some(vertical => vertical.qc === 1);
      verticleDetailRef.current = verticals;
      verticalCodeRef.current = Object.keys(verticals);

      setQcBannerHomeConditionData((prev: any) => {
        return {
          ...prev,
          verticalCode: Object.keys(
            fyndPromiseApiResponse?.promise_info?.vertical ?? {},
          ),
          qc: qc,
        };
      });

      console.log('CheckServiceability : QC = ', qc);

      ////// --------------- Get MCAT Inventory -------------------- //////////////////////
      console.log('getMcatInventory : ', 'called');
      // We can make this in parallel
      const mcatInventoryResponse = await homeController.getMcatInventory(
        pincode,
        homeConfig?.serviciablityRefreshIntervalInMin ?? 30,
        invalidateCacheAndRefetch,
      );
      // console.log(
      //   'getMcatInventory : ',
      //   JSON.stringify(mcatInventoryResponse, null, 2),
      // );

      const keys = Object.keys(mcatInventoryResponse?.region_codes).join(',');
      const values = Object.values(mcatInventoryResponse?.region_codes)
        .flat() // flatten all arrays into one
        .join(',');

      const adMetaData = await generateDefaultAdMetaData();
      setAdMetaData(prev => {
        return {
          ...prev,
          ...adMetaData,
          vertical: keys,
          region: values,
        };
      });

      ////// --------------- Create Header From Serviceability & MCAT Inventory Apis -------------------- //////////////////////
      const headerJson = {
        // pin: pincodeMetaData?.pin,
        city: pincodeMetaData?.city,
        state_code: pincodeMetaData?.state_code,
        store_code: mcatInventoryResponse?.store_codes_classified ?? [],
        region_code: mcatInventoryResponse?.region_codes ?? {},
        vertical_code: Object.keys(mcatInventoryResponse?.region_codes),
      };

      headerJsonRef.current = headerJson; // This header is used in for all the home api's.
      fyndPromiseDataRef.current = fyndPromiseApiResponse;
      await addStringPref(
        AsyncStorageKeys.MOONX_LOCATION_HEADER,
        JSON.stringify(headerJsonRef.current),
      );

      //TODO : Payload For Search Suggestion.
      const data1 = await getAlgoliaConfigPayload(
        pincodeMetaData,
        fyndPromiseApiResponse,
        mcatInventoryResponse,
      );

      console.log('GetAlgoliaConfigPayload : ', data1);
      const data = JSON.stringify(data1);
      await addStringPref(AsyncStorageKeys.SEARCH_END_POINT_DETAILS, data);

      if (!qc) {
        showHeaderWithQcSection(qc);
      }
    } catch (error) {
      console.log('CheckServiceability Error', error);
      showHeaderWithQcSection(false);
    }
  };

  const showHeaderWithQcSection = (isQCAvailailbe: boolean) => {
    if (isQCAvailailbe) {
      setQCDetails({
        ...qcDetails,
        journey: commonConfig?.quickCommerceConfig?.quickDeliveryKey,
      });
      setBean(prev => {
        return {
          ...prev,
          headerType: 18,
        };
      });
      setSelectedOption('Quick'); // Need to think about this one.
    } else {
      setQCDetails({
        ...qcDetails,
        journey: commonConfig?.quickCommerceConfig?.scheduledDeliveryKey,
      });
      setBean(prev => {
        return {
          ...prev,
          headerType: 5,
        };
      });
      setSelectedOption('Scheduled'); // Need to think about this one.
    }
    setServiceabilityCalled(true);
  };

  const getAlgoliaConfigPayload = async (
    pincodeMetaData: PinInfo | null,
    fyndPromiseApiResponse: DataResponse | null,
    mcatInventoryResponse: any,
  ) => {
    const productVerticals = [
      ...Object.keys(mcatInventoryResponse?.store_codes_classified ?? {}).map(
        key => key.toLowerCase(),
      ),
    ];

    const analyticsTags = [
      'JioMartApp',
      pincodeMetaData?.pin ?? '',
      ...Object.keys(pincodeMetaData?.master_codes ?? {}).map(
        vertical => vertical,
      ),
    ];

    let polygonStore = {};
    const data = await serviciablityControllerNew.getPolygonStoreData();
    const serviceabilities = data?.serviceabilities ?? undefined;
    const storeData = data?.storeData ?? undefined;
    let matchingRegionInfo: RegionInfo | undefined = undefined;
    if (fyndPromiseApiResponse?.region_info && storeData?.store_code) {
      // As we dont have region_code in jionet store api we look for it in the
      // Fynd Promise Api with the store_code we already have in store api's response.
      matchingRegionInfo = fyndPromiseApiResponse?.region_info?.find(
        regionInfo => regionInfo?.store_code === storeData?.store_code,
      );
    }

    if (storeData && serviceabilities) {
      polygonStore = {
        store_code: storeData.store_code ?? '',
        area_code: storeData?.area_code ?? '',
        polygon_id: serviceabilities?.polygon_id ?? '',
        region_code: matchingRegionInfo ? matchingRegionInfo?.region_code : '',
        type: serviceabilities?.type ?? '',
      };
    }

    return {
      pincode: pincodeMetaData?.pin,
      regionCode: mcatInventoryResponse?.region_codes ?? {},
      storeCodeClassified: mcatInventoryResponse?.store_codes_classified ?? [],
      polygonStore: polygonStore ?? {},
      productVerticals: productVerticals ?? [],
      hyperlocalDeliveryMessage: 'Scheduled Delivery', // TODO : Need to add hyperlocal delivery message.
      endpointDetails: {
        hitsPerPage: 10,
        clickAnalytics: true,
        filters:"NOT category_level.level1:Alcohol", //As per nagarajan
        analyticsTags: analyticsTags ?? [],
        //TODO : Need to add plpFilters.
        plpFilters:
          '(available_stores:PANINDIABOOKS OR available_stores:PANINDIACOUPONS OR available_stores:PANINDIACRAFT OR available_stores:PANINDIADIGITAL OR available_stores:PANINDIAFASHION OR available_stores:PANINDIAFINANCE OR available_stores:PANINDIAFURNITURE OR available_stores:6210 OR available_stores:QCPANINDIAGROCERIES OR available_stores:6217 OR available_stores:PANINDIAGROCERIES OR available_stores:PANINDIAHOMEANDKITCHEN OR available_stores:PANINDIAHOMEIMPROVEMENT OR available_stores:PANINDIAJEWEL OR available_stores:S575 OR available_stores:PANINDIASTL OR available_stores:PANINDIAWELLNESS) AND (inventory_stores:ALL OR inventory_stores:SL9F OR inventory_stores:7278 OR inventory_stores:SFJJ OR inventory_stores:SURR OR inventory_stores:TMX5 OR inventory_stores:S1IQ OR inventory_stores:S575 OR inventory_stores:600 OR inventory_stores:WC05 OR inventory_stores:5518 OR inventory_stores:FN01 OR inventory_stores:769 OR inventory_stores:6460 OR inventory_stores:6155 OR inventory_stores:6782 OR inventory_stores:7474 OR inventory_stores:9134 OR inventory_stores:6850 OR inventory_stores:7501 OR inventory_stores:7885 OR inventory_stores:8797 OR inventory_stores:I002 OR inventory_stores:3561 OR inventory_stores:I004 OR inventory_stores:7570 OR inventory_stores:IA50 OR inventory_stores:7635 OR inventory_stores:7579 OR inventory_stores:6746 OR inventory_stores:7934 OR inventory_stores:3754 OR inventory_stores:8636 OR inventory_stores:6417 OR inventory_stores:IA48 OR inventory_stores:6925 OR inventory_stores_3p:ALL OR inventory_stores_3p:6210 OR inventory_stores_3p:S575 OR inventory_stores_3p:general_zone OR inventory_stores_3p:DMYSTR01 OR inventory_stores_3p:TXOW OR inventory_stores_3p:TMW6 OR inventory_stores_3p:mini OR inventory_stores_3p:7351 OR inventory_stores_3p:electronics_zone) AND (mart_availability:JIO OR mart_availability:JIO_WA)',
      },
      masterVerticalIndexSearch: {
        analytics: false,
        clickAnalytics: false,
      },
      DeliveryPromises: fyndPromiseApiResponse?.promise_info
        ? {
            status: 'success',
            code: 200,
            result: fyndPromiseApiResponse?.promise_info ?? {},
          }
        : {},
    };
  };

  const {
    data: homepageQueryData,
    isLoading: isHomeDataLoading,
    error: homepageError,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    refetch: refetchHomepageData,
    isSuccess: successHomeDataResponse,
    totalPagesLoaded,
  } = useHomepageQuery(
    currentSelectedSlug,
    selectedOption === 'Quick' ? 1 : 2,
    updateHeaderJsonForMoonXHomeApi(headerJsonRef, fyndPromiseDataRef),
    !!selectedOption &&
      serviceabilityCalled &&
      currentSelectedSlug !== undefined,
  );

  // Handle homepage data changes
  useEffect(() => {
    if (currentSelectedSlug && homepageQueryData) {
      if (
        currentSelectedSlug === DefaultHomeSlug &&
        homepageQueryData?.pages?.[0] &&
        !homepageTabSectionData
      ) {
        const firstPageData = homepageQueryData.pages[0];
        setHomepageTabSectionData(
          (firstPageData?.tophomebannertab as HomeTopCategoriesTab[]) ?? [],
        );
        setHomepageTabSectionState(TOP_CATERORIES_STATES.SHOW_TOPBAR);
      }
    }
  }, [homepageQueryData, currentSelectedSlug]);

  // Memoized flattening of homepage data
  const flattenedData = useMemo(() => {
    if (!homepageQueryData?.pages?.length||homepageQueryData?.pages?.[0]==undefined) return [];
    let allItems: any[] = [];
    homepageQueryData.pages.forEach((page: any, pageIndex: number) => {
      // Create page header
      const pageHeader = {
        type: 'page_header',
        pageIndex,
        page,
        id: `page_header_${pageIndex}`,
      };
      // Create component items
      const componentItems = (page?.resultData?.data || []).map(
        (item: any, itemIndex: number) => ({
          type: 'component',
          data: item,
          pageIndex,
          itemIndex,
          id: `component_${pageIndex}_${itemIndex}`,
        }),
      );
      allItems.push(pageHeader, ...componentItems);
    });
    return allItems;
  }, [homepageQueryData?.pages]);

  // Handle loading states for tab section
  useEffect(() => {
    if (
      isHomeDataLoading &&
      currentSelectedSlug === DefaultHomeSlug &&
      !homepageTabSectionData
    ) {
      setHomepageTabSectionState(TOP_CATERORIES_STATES.LOADING);
    }
  }, [isHomeDataLoading, currentSelectedSlug]);

  // Handle errors
  useEffect(() => {
    if (homepageError) {
      console.error('getHomepageData', homepageError);
      if (currentSelectedSlug === DefaultHomeSlug) {
        setHomepageTabSectionState(undefined);
      }
    }
  }, [homepageError, currentSelectedSlug]);

  // Function to load more data (pagination)
  const loadMoreHomepageData = () => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  };

  // Function to refresh homepage data
  const refreshHomepageData = async () => {
    await queryClient.invalidateQueries({
      queryKey: homepageQueryKeys.homepage(
        currentSelectedSlug ?? '',
        selectedOption === 'Quick' ? 1 : 2,
        JSON.stringify(headerJsonRef.current),
        getPersistString(currentSelectedSlug ?? ''),
      ),
    });
  };

  const resetDataAndStatesForNewTab = () => {
    // Reset the processed pages counter
    // processedPagesRef.current = 0; // This line is removed
    // setFlattenedData([]); // This line is removed
    // Reset special index when tab changes
    setActiveSpecialIndex(null);
    setIsWidgetLoading(false);
  };

  useEffect(() => {
    // Reset data when tab is switched : Quick <-> Scheduled
    if (selectedOption && serviceabilityCalled) {
      console.log('setCurrentSelectedSlug : flow change', currentSelectedSlug);
      resetDataAndStatesForNewTab();
      setCurrentSelectedSlug(DefaultHomeSlug);
    }
  }, [selectedOption, serviceabilityCalled]);

  const handleTabChange = (tabItem: HomeTopCategoriesTab) => {
    console.log('handleTabChange - Selected tab:', tabItem?.banner_link);
    console.log('handleTabChange - Current slug:', currentSelectedSlug);

    const selectedSlug = extractLastPathSegment(tabItem?.banner_link ?? '');
    const firstMatchingItem = getFirstMatchingItem();
    const selectionType = determineSelectionType(
      tabItem,
      selectedSlug,
      firstMatchingItem,
    );

    // Handle different selection scenarios
    switch (selectionType) {
      case TabSelectionType.ALREADY_SELECTED:
        console.log('handleTabChange - Tab already selected, skipping');
        return;

      case TabSelectionType.FIRST_POSITION:
        console.log(
          'handleTabChange - Loading default home slug for first position',
        );
        resetDataAndStatesForNewTab();
        setCurrentSelectedSlug(DefaultHomeSlug);
        break;

      case TabSelectionType.SPECIFIC_SLUG:
        console.log('handleTabChange - Loading specific slug:', selectedSlug);
        resetDataAndStatesForNewTab();
        setCurrentSelectedSlug(selectedSlug);
        break;
      case TabSelectionType.OPEN_WEB_PAGE_SLUG:
        console.log(
          'handleTabChange - Open url in webview:',
          tabItem?.banner_link,
        );
        // Open url in webview.
        navigateTo(
          navBeanObj({
            source: '',
            destination: AppScreens.COMMON_WEB_VIEW,
            actionType: 'T003',
            headerType: 9,
            headerVisibility: 2,
            loginRequired: false,
            actionUrl: `${tabItem?.banner_link ?? ''}`,
          }),
          navigation,
        );
        break;
    }
  };

  // Determine what type of selection this is
  const determineSelectionType = (
    tabItem: HomeTopCategoriesTab,
    selectedSlug: string,
    firstMatchingItem: any,
  ): TabSelectionType => {
    // Check if already selected
    if (isTabAlreadySelected(selectedSlug, firstMatchingItem, tabItem)) {
      return TabSelectionType.ALREADY_SELECTED;
    }

    // Check if first position
    if (isFirstPositionTab(tabItem, firstMatchingItem)) {
      return TabSelectionType.FIRST_POSITION;
    }

    if (tabItem.exttab && tabItem.exttab === 1)
      return TabSelectionType.OPEN_WEB_PAGE_SLUG;

    // Default to specific slug
    return TabSelectionType.SPECIFIC_SLUG;
  };

  // Helper function to check if the selected tab is already active
  const isTabAlreadySelected = (
    selectedSlug: string,
    firstMatchingItem: any,
    tabItem: any,
  ): boolean => {
    // Case 1: Same slug as current
    if (selectedSlug === currentSelectedSlug) {
      return true;
    }

    // Case 2: User is on DefaultHomeSlug and clicked the first position tab
    // (DefaultHomeSlug already contains data for the first position)
    if (
      currentSelectedSlug === DefaultHomeSlug &&
      tabItem?.banner_link === firstMatchingItem?.banner_link
    ) {
      return true;
    }

    return false;
  };

  // Helper function to check if this is the first position tab
  const isFirstPositionTab = (
    tabItem: any,
    firstMatchingItem: any,
  ): boolean => {
    return tabItem?.banner_link === firstMatchingItem?.banner_link;
  };

  const getFirstMatchingItem = () => {
    return homepageTabSectionData?.find(
      item =>
        (selectedOption === 'Quick'
          ? item.quick === 1
          : item.scheduled === 1) && item.type === 'banner',
    );
  };

  const getCurrentSelectedSlug = (): string => {
    if (currentSelectedSlug === DefaultHomeSlug) {
      const firstMatchingItem = getFirstMatchingItem();
      return firstMatchingItem
        ? extractLastPathSegment(firstMatchingItem.banner_link)
        : currentSelectedSlug;
    } else {
      return currentSelectedSlug ?? "";
    }
  };

  const checkHomeV1ShowCondition = useCallback(async () => {
    let {city, verticalCode} = qcBannerHomeConditionData;
    const isCityMatched = homeConfig?.city?.includes(city?.toUpperCase() ?? '');
    verticalCode = verticalCode?.map(item => item.toUpperCase());
    let isVerticalCodeMatched = false;
    let isSaveCityInStorage = await JMHomeDBManager.getCityName();
    for (let i = 0; i < homeConfig?.bannerInfo?.length; i++) {
      if (
        arraysEqual(
          homeConfig?.bannerInfo[i]?.verticalCodes ?? [],
          verticalCode ?? [],
        )
      ) {
        showBlockQcBannerHome.current = i;
        isVerticalCodeMatched = true;
        break;
      }
    }
    return !isSaveCityInStorage && isCityMatched && isVerticalCodeMatched;
  }, [homeConfig?.bannerInfo, homeConfig?.city, qcBannerHomeConditionData]);

  /**
   * Determines the appropriate delivery message based on the current tab's vertical.
   *
   * @param {Object} currentTab - The currently selected tab object
   * @param {Object} verticals - Available vertical configurations from Fynd Promise Api.
   * @returns {string} The delivery message to display
   *
   * Priority order for message selection:
   * 1. Current tab's vertical display message (if vertical exists)
   * 2. Groceries vertical display message (fallback)
   * 3. Quick commerce config default message (ultimate fallback)
   */
  const getCategoryBasedQcDeliveryMessage = (
    currentTab: HomeTopCategoriesTab | undefined,
    verticals: Record<string, VerticalDetails>,
  ) => {
    const currentVertical = currentTab?.verticals ?? undefined;

    // Use current vertical's message if available
    if (
      currentVertical &&
      verticals[currentVertical] &&
      verticals[currentVertical]?.global_display_message
    ) {
      return verticals[currentVertical].global_display_message;
    }

    // Fallback to groceries vertical or config default
    return (
      verticals[VerticleTypes.GROCERIES]?.global_display_message ?? ''
      //commonConfig?.quickCommerceConfig?.quickDeliveryMessage
    );
  };

  /**
   * Updates the quick commerce delivery message when the user switches tabs.
   *
   * This effect monitors changes to the selected tab slug and updates the delivery
   * message displayed in the address bar accordingly. Each tab may have different
   * vertical configurations with specific delivery messages.
   */
  useEffect(() => {
    if (
      !currentSelectedSlug ||
      !homepageTabSectionData?.length ||
      selectedOption === 'Scheduled'
    )
      return;

    const currentTab = homepageTabSectionData.find(item =>
      item?.banner_link?.includes(getCurrentSelectedSlug()),
    );

    const verticals = verticleDetailRef?.current ?? {};
    const message = getCategoryBasedQcDeliveryMessage(currentTab, verticals);

    setQCDetails(prev => ({...prev, qcMessage: message}));
  }, [currentSelectedSlug, homepageTabSectionData,selectedOption]);

  const clearHomepageAndReloadData = async (pin: string) => {
    console.log('clearHomepageData -- ');
    setCurrentSelectedSlug(DefaultHomeSlug);
    await new Promise(resolve => setTimeout(resolve, 300));
    
    setHomepageTabSectionData(undefined);
    setHomepageTabSectionState(TOP_CATERORIES_STATES.LOADING);
    // Clear all homepage related queries to force fresh data fetch
    await queryClient.removeQueries({ queryKey: ['homepage'] });

    await checkServiceability(pin, true); // we need hit api's and refresh in case pincode is changed.
  };

  usePincodeChange(() => {
    // callback when pin code is changed.
    JMDatabaseManager.address.getDefaultAddress().then(value => {
      const address = JSON.parse(value || '');
      const pin = address?.pin;
      JMHomeDBManager.getCityName().then(value => {
        if (value?.toUpperCase() !== address?.city?.toUpperCase()) {
          JMHomeDBManager.deleteCityName();
        }
      });
      setQcBannerHomeConditionData(prev => {
        return {
          ...prev,
          city: address?.city,
        };
      });
      clearHomepageAndReloadData(pin);
    });
  });

  useEffect(() => {
    loadHomepageData();
  }, []);

  const loadHomepageData = () => {
    console.log('loadHomepageData -- ');
    setServiceabilityCalled(false);
    JMDatabaseManager.address.getDefaultAddress().then(value => {
      const address = JSON.parse(value || '');
      const pin = address?.pin;
      JMHomeDBManager.getCityName().then(value => {
        if (value?.toUpperCase() !== address?.city?.toUpperCase()) {
          JMHomeDBManager.deleteCityName();
        }
      });
      setQcBannerHomeConditionData(prev => {
        return {
          ...prev,
          city: address?.city,
        };
      });
      JMLogger.log("usePincodeChange getDefaultAddress"+address?.pin)
      getPrefString("homepincode").then((lastPinCode) => {
        if(lastPinCode !== pin){
          clearHomepageAndReloadData(pin);
        }
        else{
          checkServiceability(pin ?? '400001', false);
        }
      })
    });
  };

  useEffect(() => {
     if(conditionToShowQcBannerHome){
        setQCDetails((prev: any) => {
          return {
            ...prev,
            tempQcMessage : qcDetails.qcMessage,
            qcMessage : "",
            scheduledMessage : ""
          };
        });
     }
  }, [conditionToShowQcBannerHome])

  useEffect(() => {
    if (qcBannerHomeConditionData && qcBannerHomeConditionData.qc) {
      checkHomeV1ShowCondition().then(val => {
        if (val) {
          setBean(prev => {
            return {
              ...prev,
              headerType: 17,
              navTitle: 'JioMart',
            };
          });
        } else {
          showHeaderWithQcSection(true)
        }
        setConditionToShowQcBannerHome(val);
      });
    }
  }, [qcBannerHomeConditionData]);

  useEffect(() => {
    if (!conditionToShowQcBannerHome && bean?.headerType === 18) {
      handleQuickBottomsheetTypeIdentifier(verticalCodeRef.current ?? []);
    }
  }, [bean, conditionToShowQcBannerHome]);

  /**
   * Filters the homepage top tab section data based on the selected delivery option and vertical availability.
   *
   * This function applies different filtering logic depending on whether the user has selected
   * "Quick" or "Scheduled" delivery options.
   *
   * @returns {HomeTopCategoriesTab[]} Array of filtered banner items that should be displayed
   *
   * @description
   * Filtering Logic:
   *
   * 1. **Base Filtering**: Only includes items where `type === 'banner'`
   *
   * 2. **Scheduled Delivery**:
   *    - Returns items where `scheduled === 1`
   *
   * 3. **Quick Delivery**:
   *    - First checks if `quick === 1`
   *    - Then applies additional vertical-based filtering:
   *      - If `verticals` is empty/null/undefined → include item (return true)
   *      - If `verticals` has value → check against `verticleDetailRef.current`
   *        - If vertical exists in reference data AND has `qc === 1` → include item
   *        - Otherwise → exclude item
   *
   */
  const getFilteredTopCategoriesData = useMemo((): HomeTopCategoriesTab[] => {
    if (!homepageTabSectionData) {
      return [];
    }

    return homepageTabSectionData.filter((item: HomeTopCategoriesTab) => {
      // First check if it's a banner type
      if (item?.type !== 'banner') {
        return false;
      }

      // Check based on selected option
      if (selectedOption === 'Scheduled') {
        return item?.scheduled === 1;
      } else {
        // For quick items, check if item.quick === 1
        if (item?.quick !== 1) {
          return false;
        }

        // Additional checking for scheduled items
        const verticals = item?.verticals ?? undefined;

        // If verticals doesn't exist or is null/undefined/empty string, return true
        if (!verticals || verticals.trim() === '') {
          return true;
        }

        // Check if verticals exists in verticleDetailRef and has qc === 1
        const verticalDetails = verticleDetailRef?.current?.[verticals];
        if (verticalDetails && verticalDetails?.qc === 1) {
          return true;
        }

        return false;
      }
    });
  }, [homepageTabSectionData, selectedOption, verticleDetailRef]);

  const handleQuickBottomsheetTypeIdentifier = (
    quickBtmSheetIdentifier: string[],
  ) => {
    try {
      const quickBtmSheetViewDetails =
        commonConfig?.quickCommerceConfig?.QuickBottomSheetViewDetails?.[0];
      const quickBtmSheetIdentifierBean = getQuickBottomsheetTypeIdentifier(
        arrayIntoLowerCase(quickBtmSheetIdentifier),
        quickBtmSheetViewDetails,
      );
      setQuickBottomSheetIdentifierBean(quickBtmSheetIdentifierBean);
    } catch (error) {}
  };

  return {
    ...props,
    navigationBean,
    eventTriggerBean: bean,
    homepageTabSectionData,
    isHomeDataLoading,
    successHomeDataResponse,
    selectedFlow: selectedOption,
    currentSelectedSlug,
    homepageTabSectionState,
    hasNextPage,
    isFetchingNextPage,
    homepageQueryData,
    homepageError,
    flattenedData,
    commonConfig,
    homeConfig,
    showBlockQcBannerHome,
    qcBannerHomeConditionData,
    conditionToShowQcBannerHome,
    setConditionToShowQcBannerHome,
    activeSpecialIndex,
    isWidgetLoading,
    setIsWidgetLoading,
    setActiveSpecialIndex,
    handleTabChange,
    getCurrentSelectedSlug,
    fetchNextPage,
    refetchHomepageData,
    loadMoreHomepageData,
    getFilteredTopCategoriesData,
    checkHomeV1ShowCondition,
    setQCDetails,
    setSelectedOption,
    isBSNonCancellable,
    setBSNonCancellable,
    showQuickBtmSheet,
    openQuickBtmSheet,
    closeQuickBtmSheet,
    quickBottomSheetIdentifierBean,
    setQuickBottomSheetIdentifierBean,
    newTagImage,
    adMetaData,
    loadHomepageData,
    setBean,
    setServiceabilityCalled
  };
};

export default useJMHomeScreenController;

const dummyAlgoliaConfig = {
  pincode: '400020',
  regionCode: {
    BOOKS: ['PANINDIABOOKS'],
    COUPONS: ['PANINDIACOUPONS'],
    CRAFTSOFINDIA: ['PANINDIACRAFT'],
    ELECTRONICS: ['PANINDIADIGITAL'],
    FASHION: ['PANINDIAFASHION'],
    FINANCE: ['PANINDIAFINANCE'],
    FURNITURE: ['PANINDIAFURNITURE'],
    GROCERIES: ['6210', 'QCPANINDIAGROCERIES', '6217', 'PANINDIAGROCERIES'],
    HOMEANDKITCHEN: ['PANINDIAHOMEANDKITCHEN'],
    HOMEIMPROVEMENT: ['PANINDIAHOMEIMPROVEMENT'],
    JEWELLERY: ['PANINDIAJEWEL'],
    PREMIUMFRUITS: ['S575'],
    SPORTSTOYSLUGGAGE: ['PANINDIASTL'],
    WELLNESS: ['PANINDIAWELLNESS'],
  },
  storeCodeClassified: {
    GROCERIES: {
      '3P': ['6210', 'S575', 'general_zone', 'DMYSTR01', 'TXOW', 'TMW6'],
    },
    FASHION: {
      '3P': ['mini', 'general_zone'],
      '1P': ['SL9F', '7278', 'SFJJ', 'SURR'],
    },
    JEWELLERY: {
      '1P': ['TMX5'],
    },
    PREMIUMFRUITS: {
      '1P': ['S1IQ', 'S575'],
    },
    BOOKS: {
      '3P': ['7351', 'general_zone'],
      '1P': ['7278', 'SURR'],
    },
    FURNITURE: {
      '3P': ['general_zone'],
      '1P': ['600'],
    },
    SPORTSTOYSLUGGAGE: {
      '3P': ['general_zone'],
      '1P': ['SURR'],
    },
    COUPONS: {
      '1P': ['WC05', '5518'],
    },
    FINANCE: {
      '1P': ['FN01'],
    },
    HOMEIMPROVEMENT: {
      '3P': ['general_zone'],
      '1P': ['SURR'],
    },
    HOMEANDKITCHEN: {
      '3P': ['general_zone'],
      '1P': ['SURR'],
    },
    CRAFTSOFINDIA: {
      '3P': ['7351', 'general_zone'],
      '1P': ['SURR'],
    },
    WELLNESS: {
      '1P': ['769'],
    },
    ELECTRONICS: {
      '3P': ['electronics_zone', 'mini'],
      '1P': [
        '6460',
        '6155',
        '6782',
        '7474',
        '9134',
        '6850',
        '7278',
        '7501',
        '7885',
        '8797',
        'I002',
        '3561',
        'I004',
        '7570',
        'IA50',
        '7635',
        '7579',
        '6746',
        '7934',
        '3754',
        '8636',
        '6417',
        'IA48',
        '6925',
      ],
    },
  },
  polygonStore: {},
  productVerticals: [
    'books',
    'coupons',
    'craftsofindia',
    'electronics',
    'fashion',
    'finance',
    'furniture',
    'groceries',
    'homeandkitchen',
    'homeimprovement',
    'jewellery',
    'premiumfruits',
    'sportstoysluggage',
    'wellness',
    'master_vertical',
  ],
  hyperlocalDeliveryMessage: 'Scheduled Delivery',
  endpointDetails: {
    hitsPerPage: 10,
    clickAnalytics: true,
    filters:
      '(NOT category_level.level1:Alcohol AND NOT category_level.level1:Beauty AND NOT category_level.level1:Localshops AND  (NOT category_level.level1:Alcohol) AND (NOT category_level.level1:"Local Shops"))',
    analyticsTags: [
      'JioMartApp',
      '400020',
      'PANINDIABOOKS',
      'PANINDIACOUPONS',
      'PANINDIACRAFT',
      'PANINDIADIGITAL',
      'PANINDIAFASHION',
      'PANINDIAFINANCE',
      'PANINDIAFURNITURE',
      '6210',
      'QCPANINDIAGROCERIES',
      '6217',
      'PANINDIAGROCERIES',
      'PANINDIAHOMEANDKITCHEN',
      'PANINDIAHOMEIMPROVEMENT',
      'PANINDIAJEWEL',
      'S575',
      'PANINDIASTL',
      'PANINDIAWELLNESS',
    ],
    plpFilters:
      '(available_stores:PANINDIABOOKS OR available_stores:PANINDIACOUPONS OR available_stores:PANINDIACRAFT OR available_stores:PANINDIADIGITAL OR available_stores:PANINDIAFASHION OR available_stores:PANINDIAFINANCE OR available_stores:PANINDIAFURNITURE OR available_stores:6210 OR available_stores:QCPANINDIAGROCERIES OR available_stores:6217 OR available_stores:PANINDIAGROCERIES OR available_stores:PANINDIAHOMEANDKITCHEN OR available_stores:PANINDIAHOMEIMPROVEMENT OR available_stores:PANINDIAJEWEL OR available_stores:S575 OR available_stores:PANINDIASTL OR available_stores:PANINDIAWELLNESS) AND (inventory_stores:ALL OR inventory_stores:SL9F OR inventory_stores:7278 OR inventory_stores:SFJJ OR inventory_stores:SURR OR inventory_stores:TMX5 OR inventory_stores:S1IQ OR inventory_stores:S575 OR inventory_stores:600 OR inventory_stores:WC05 OR inventory_stores:5518 OR inventory_stores:FN01 OR inventory_stores:769 OR inventory_stores:6460 OR inventory_stores:6155 OR inventory_stores:6782 OR inventory_stores:7474 OR inventory_stores:9134 OR inventory_stores:6850 OR inventory_stores:7501 OR inventory_stores:7885 OR inventory_stores:8797 OR inventory_stores:I002 OR inventory_stores:3561 OR inventory_stores:I004 OR inventory_stores:7570 OR inventory_stores:IA50 OR inventory_stores:7635 OR inventory_stores:7579 OR inventory_stores:6746 OR inventory_stores:7934 OR inventory_stores:3754 OR inventory_stores:8636 OR inventory_stores:6417 OR inventory_stores:IA48 OR inventory_stores:6925 OR inventory_stores_3p:ALL OR inventory_stores_3p:6210 OR inventory_stores_3p:S575 OR inventory_stores_3p:general_zone OR inventory_stores_3p:DMYSTR01 OR inventory_stores_3p:TXOW OR inventory_stores_3p:TMW6 OR inventory_stores_3p:mini OR inventory_stores_3p:7351 OR inventory_stores_3p:electronics_zone) AND (mart_availability:JIO OR mart_availability:JIO_WA)',
  },
  masterVerticalIndexSearch: {
    analytics: false,
    clickAnalytics: false,
  },
  DeliveryPromises: {
    status: 'success',
    code: 200,
    result: {
      stormbreaker_uuid: '3f902edd-8105-4b2b-8b0c-0dd4d1374b81',
      vertical: {
        GROCERIES: {
          qc: 1,
          suspect: 1,
          stores: [
            {
              store: '5518',
              delivery_promise: {
                timestamp: {
                  min: 1753955767,
                  max: 1753956967,
                },
                formatted: {
                  min: '31 Jul, Thursday - 15:26',
                  max: '31 Jul, Thursday - 15:46',
                },
              },
              display_message: 'Free Delivery in 10 to 30 mins',
              is_qc: true,
              distance: 1.2074958355341856,
            },
          ],
          global_promise: {
            timestamp: {
              min: 1753955767,
              max: 1753956967,
            },
            formatted: {
              min: '31 Jul, Thursday - 15:26',
              max: '31 Jul, Thursday - 15:46',
            },
          },
          global_display_message: 'Free Delivery in 10 to 30 mins',
        },
        FASHION: {
          error: {
            type: 'StoreNotFoundError',
            message: 'no qc store mapped',
          },
          qc: 0,
          suspect: 0,
          distance: 0,
        },
        ELECTRONICS: {
          qc: 0,
          store_selection_filter: {
            store_type: 'DC',
          },
          suspect: 0,
          stores: [],
          error: {
            type: 'StoreNotQCEnabled',
            message: 'No store is QC enabled',
          },
        },
      },
      to_pincode: '400020',
      channel_id: '',
      identifier: '6e2dc155-5c6f-4bf7-a427-b6221cf8e752',
      customer_details: {
        phone_number: '',
        coordinates: {
          lat: 18.937910895,
          long: 72.826642286,
        },
        pincode: '400020',
      },
      success: true,
    },
  },
};
