import React from 'react';
import { JioIcon, JioText, useColor } from '@jio/rn_components';
import {
  IconColor,
  JioTypography,
  type JioIconProps,
  type JioTextProps,
} from '@jio/rn_components/src/index.types';
import { useState } from 'react';
import {
  Pressable,
  StyleSheet,
  View,
  type StyleProp,
  type ViewStyle,
} from 'react-native';
import FastImage, { type FastImageProps } from 'react-native-fast-image';
import { FlatList } from 'react-native-gesture-handler';
import { StaticImages } from '../../../jiomart-common/src/JMConstants';
import { rh, rw } from '../../../jiomart-common/src/JMResponsive';
import SelectStarRating, {
  type SelectStarRatingProps,
} from '../../../jiomart-general/src/ui/SelectStarRating';
import StarIcon from '../../../jiomart-general/src/ui/StarIcon';
import { type StarRatingProps } from '../../../jiomart-general/src/ui/StarRating';

interface JMOrderCardHeaderProps {
  title?: JioTextProps;
  subTitle?: JioTextProps;
  icon?: JioIconProps;
}

const JMOrderCardHeader = (props: JMOrderCardHeaderProps) => {
  const { title, subTitle, icon } = props;
  return (
    <View
      style={{
        flexDirection: 'row',
        alignItems: 'center',
        columnGap: 4,
      }}>
      <JioText
        color={'sparkle_60'}
        appearance={JioTypography.BODY_XS_BOLD}
        {...title}
      />
      <JioText
        color={'primary_grey_60'}
        appearance={JioTypography.BODY_XXS_BOLD}
        {...subTitle}
      />
      {icon && (
        <JioIcon
          color={IconColor.PRIMARY60}
          style={{
            marginLeft: 'auto',
            padding: 4,
          }}
          {...icon}
        />
      )}
    </View>
  );
};

interface JMOrderCardItemProps {
  productTitle?: JioTextProps;
  productSubTitle?: JioTextProps;
  image?: FastImageProps;
  child?: any;
  qty?: number;
}

const JMOrderCardItem = (props: JMOrderCardItemProps) => {
  const { productTitle, productSubTitle, image, child, qty } = props;
  const [imageError, setImageError] = useState(false);

  const difference = child?.length > 3 ? child?.length - 3 : 0;
  const borderColor = useColor('primary_grey_40');
  const size = child?.length === 1 ? 64 : 24;
  return (
    <View
      style={{
        flexDirection: 'row',
        marginVertical: 4,
        columnGap: 12,
      }}>
      <View>
        <FlatList
          data={child?.slice(0, 4)}
          showsHorizontalScrollIndicator={false}
          keyExtractor={(_, index) => `image-${index}`}
          numColumns={2}
          renderItem={({ item, index }) => {

            if (index === 3) {
              return (
                <View
                  style={{
                    width: rw(24),
                    height: rw(24),
                    borderWidth: 1,
                    borderColor,
                    borderRadius: 100,
                    justifyContent: 'center',
                    alignItems: 'center',
                    margin: 4,
                  }}>
                  <JioText
                    text={`+${difference}`}
                    color={'primary_grey_80'}
                    appearance={JioTypography.BODY_XS_BOLD}
                  />
                </View>
              );
            }
            return (
              <FastImage
                source={{ 
                  uri: item?.product_image || StaticImages.PLACEHOLDER_IMAGE,
                  priority: FastImage.priority.normal,
                }}
                defaultSource={require('../../../jiomart-common/src/assets/placeholder_image.png')}
                style={{
                  width: rw(size),
                  height: rw(size),
                  borderColor,
                  borderRadius: 8,
                  margin: 4,
                }}
                resizeMode={FastImage.resizeMode.cover}
                onError={() => setImageError(true)}
              />
            );
          }}
          contentContainerStyle={{
            justifyContent: 'center',
            alignItems: 'center',
            flex: 1,
          }}
          style={{
            width: rw(64),
            height: rw(64),
            borderWidth: 1,
            borderColor,
            borderRadius: 8,
            backgroundColor: '#ffffff',
          }}
        />
        {child?.length === 1 && qty ? (
          <View
            style={{
              borderWidth: 1,
              width: rw(16),
              height: rw(16),
              justifyContent: 'center',
              alignItems: 'center',
              borderColor,
              borderRadius: 100,
              position: 'absolute',
              bottom: 0,
              right: 0,
              backgroundColor: borderColor,
            }}>
            <JioText text={`${qty}`} appearance={JioTypography.BODY_XXS_BOLD} />
          </View>
        ) : null}
      </View>
      <View
        style={{
          rowGap: 8,
          alignSelf: 'center',
          flex: 1,
        }}>
        {image?.source ? (
          <FastImage
            style={{
              width: rw(78),
              height: rh(15),
              paddingVertical: 4,
            }}
            defaultSource={require('../../../jiomart-common/src/assets/placeholder_image.png')}
            resizeMode={FastImage.resizeMode.contain}
            {...image}
          />
        ) : null}
        <JioText
          color={'primary_grey_80'}
          appearance={JioTypography.BODY_XS}
          maxLines={1}
          {...productTitle}
        />
        <JioText
          color={'primary_grey_60'}
          appearance={JioTypography.BODY_XS}
          maxLines={1}
          {...productSubTitle}
        />
      </View>
    </View>
  );
};

interface JMDeliveryOtpProps {
  deliveryOtp?: JioTextProps;
}

const JMDeliveryOtp = (props: JMDeliveryOtpProps) => {
  const { deliveryOtp } = props;
  const backgroundColor = useColor('primary_20');
  return (
    <View
      style={{
        paddingHorizontal: 12,
        paddingVertical: 6,
        backgroundColor,
        borderRadius: 8,
        marginTop: 12,
      }}>
      <JioText
        color={'primary_grey_100'}
        appearance={JioTypography.BODY_XS_BOLD}
        {...deliveryOtp}
      />
    </View>
  );
};

interface JMTrackOrderProps {
  trackOrder?: JioTextProps;
  onTrackOrderPress?: () => void;
}

const JMTrackOrder = (props: JMTrackOrderProps) => {
  const {trackOrder, onTrackOrderPress} = props;

  const borderColor = useColor('primary_grey_40');
  const backgroundColor = useColor('primary_inverse');

  return (
    <Pressable
      style={{
        width: '100%',
        paddingVertical: 8,
        alignItems: 'center',
        justifyContent: 'center',
        borderWidth: 1,
        borderRadius: 100,
        marginTop: 12,
        borderColor: borderColor,
        backgroundColor: backgroundColor,
      }}
      onPress={onTrackOrderPress}>
      <JioText
        color={'primary_60'}
        appearance={JioTypography.BODY_XS_BOLD}
        {...trackOrder}
        text={'Track this order'}
      />
    </Pressable>
  );
};

interface JMProductRatingProps {
  rate?: SelectStarRatingProps;
  rateTitle?: JioTextProps;
}

const JMProductRating = (props: JMProductRatingProps) => {
  const { rate, rateTitle } = props;
  return (
    <View
      style={{
        flexDirection: 'row',
        marginTop: 8,
        justifyContent: 'space-between',
      }}>
      <JioText
        color={'primary_grey_100'}
        appearance={JioTypography.BODY_XS}
        style={{ alignSelf: 'center' }}
        {...rateTitle}
      />
      <SelectStarRating size={20} style={{ columnGap: 4 }} {...rate} />
    </View>
  );
};

interface JMProductReviewProps {
  reviewTitle?: JioTextProps;
  reviewSubTitle?: JioTextProps;
  onPressReviewSubTitle?: () => void;
}

const JMProductReview = (props: JMProductReviewProps) => {
  const { reviewTitle, reviewSubTitle, onPressReviewSubTitle } = props;
  const backgroundColor = useColor('primary_grey_20');
  return (
    <View
      style={{
        paddingHorizontal: 12,
        paddingVertical: 6,
        backgroundColor,
        borderRadius: 8,
        marginTop: 8,
        flexDirection: 'row',
        justifyContent: 'space-between',
      }}>
      <JioText
        color={'primary_grey_100'}
        appearance={JioTypography.BODY_XS_BOLD}
        {...reviewTitle}
      />
      <Pressable onPress={onPressReviewSubTitle}>
        <JioText
          color={'primary_60'}
          appearance={JioTypography.BODY_XS_BOLD}
          {...reviewSubTitle}
        />
      </Pressable>
    </View>
  );
};

interface JMProductRateInOrderProps {
  rateMessage?: StarRatingProps;
  onPress?: () => void;
}

const JMProductRateInOrder = (props: JMProductRateInOrderProps) => {
  const { rateMessage, onPress } = props;
  const backgroundColor = useColor('primary_grey_20');
  return (
    <Pressable
      style={{
        paddingHorizontal: 12,
        paddingVertical: 6,
        backgroundColor,
        borderRadius: 8,
        marginTop: 12,
        flexDirection: 'row',
        alignItems: 'center',
        columnGap: 8,
      }}
      onPress={onPress}>
      <StarIcon offset="100%" />
      <JioText
        color={'primary_60'}
        appearance={JioTypography.BODY_XS_BOLD}
        {...rateMessage}
      />
    </Pressable>
  );
};

interface JMProductRefundProps {
  refund?: JioTextProps;
}

const JMProductRefund = (props: JMProductRefundProps) => {
  const { refund } = props;
  const backgroundColor = useColor('primary_grey_20');
  return (
    <View
      style={{
        paddingHorizontal: 12,
        paddingVertical: 6,
        backgroundColor,
        borderRadius: 8,
        marginTop: 12,
        flexDirection: 'row',
        alignItems: 'center',
        columnGap: 8,
      }}>
      <JioText
        color={'primary_grey_100'}
        appearance={JioTypography.BODY_XS_BOLD}
        {...refund}
      />
    </View>
  );
};

interface JMOrderCardProps
  extends JMOrderCardHeaderProps,
  JMTrackOrderProps,
  JMDeliveryOtpProps,
  JMProductRefundProps,
  JMProductRatingProps,
  JMProductReviewProps {
  style?: StyleProp<ViewStyle>;
  item?: JMOrderCardItemProps[];
  shouldShowRateBlock?: boolean;
  shouldShowReviewBlock?: boolean;
  shouldShowReviewMessageBlock?: boolean;
  reviewMessage: JMProductRateInOrderProps;
  onPress?: () => void;
}

const JMOrderCard = (props: JMOrderCardProps) => {
  const {
    style,
    title,
    subTitle,
    icon,
    item,
    deliveryOtp,
    refund,
    onPress,
    rate,
    rateTitle,
    shouldShowRateBlock,
    shouldShowReviewBlock,
    shouldShowReviewMessageBlock,
    reviewMessage,
    reviewTitle,
    reviewSubTitle,
    trackOrder,
    onTrackOrderPress,
    onPressReviewSubTitle,
  } = props;
  return (
    <Pressable onPress={onPress}>
      <View style={[style]}>
        <JMOrderCardHeader title={title} subTitle={subTitle} icon={icon} />
        {item?.map((item, index) => {
          return <JMOrderCardItem {...item} key={index} />;
        })}
        {deliveryOtp ? <JMDeliveryOtp deliveryOtp={deliveryOtp} /> : null}
        {shouldShowRateBlock ? (
          <JMProductRating rate={rate} rateTitle={rateTitle} />
        ) : null}
        {shouldShowReviewBlock ? (
          <JMProductReview
            reviewTitle={reviewTitle}
            reviewSubTitle={reviewSubTitle}
            onPressReviewSubTitle={onPressReviewSubTitle}
          />
        ) : null}
        {shouldShowReviewMessageBlock ? (
          <JMProductRateInOrder {...reviewMessage} />
        ) : null}
        {refund ? <JMProductRefund refund={refund} /> : null}
        {trackOrder ? <JMTrackOrder trackOrder={trackOrder} onTrackOrderPress={onTrackOrderPress} /> : null}
      </View>
    </Pressable>
  );
};

export default JMOrderCard;

const styles = StyleSheet.create({});
