import {useQuery} from '@tanstack/react-query';
import type {UseJMServiceScreenProps} from '../types/JMServiceScreenType';
import {AsyncStorageKeys, RQKey} from '../../../jiomart-common/src/JMConstants';
import JMOrderNetworkController from '../../../jiomart-networkmanager/src/JMNetworkController/JMOrderNetworkController';
import {
  addStringPref,
  getPrefString,
} from '../../../jiomart-common/src/JMAsyncStorageHelper';

const orderController = new JMOrderNetworkController();

const useJMServiceScreenController = (props: UseJMServiceScreenProps) => {
  const {route} = props;
  const navigationBean = route.params;

  const serviceRequestListRes = useQuery({
    queryKey: [RQKey.SERVICE_REQUEST_LIST],
    queryFn: async () => {
      try {
        const serviceRequestResponse =
          await orderController.getServiceRequestList();
        if (serviceRequestResponse?.data?.length > 0) {
          await addStringPref(
            AsyncStorageKeys.SERVICE_REQUEST_RESPONSE,
            JSON.stringify(serviceRequestResponse),
          );
        }
        return serviceRequestResponse;
      } catch (error) {
        throw error;
      }
    },
    retry: 0,
    placeholderData: (previousData, _) => previousData,
    initialData: () => getPrefString(AsyncStorageKeys.SERVICE_REQUEST_RESPONSE),
  });

  return {
    ...props,
    data: serviceRequestListRes?.data?.result ?? [],
    serviceRequestListRes,
    navigationBean,
  };
};

export default useJMServiceScreenController;
