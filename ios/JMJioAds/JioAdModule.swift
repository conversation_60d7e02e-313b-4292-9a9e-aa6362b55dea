import Foundation
import JioAdsFramework
import CommonCrypto

@objc(JioAdModule)
class JioAdModule: NSObject, JIOAdViewProtocol {
  
  static var adViewMap: [String: JioAdView] = [:]
  var adView: JioAdView?
  var params: NSDictionary = [:]
  var clouser: ((Bool) -> Void)?
  
  @objc
  func checkAdStatus(_ params: NSDictionary, resolver resolve: @escaping RCTPromiseResolveBlock, rejecter reject: @escaping RCTPromiseRejectBlock) {
    DispatchQueue.main.async {
      do {
        self.params = params
        guard let adSpotId = params["adspotKey"] as? String, !adSpotId.isEmpty else {
          resolve(false)
          return
        }
        let adType = params["adType"] as? Int ?? 5
        let adWidth = params["adWidth"] as? Int ?? 0
        let adHeight = params["adHeight"] as? Int ?? 0
        var adMetaVal = params["adMetaData"] as? [String: Any] ?? [:]
        
        guard let rootVC = RCTPresentedViewController() else {
          resolve(false)
          return
        }
        
        JioAdSdk.setPackageName(packageName: JMInfoPlist.appIdentifier)
        self.setupAdView(adType: adType, adspotKey: adSpotId, adHeight: adHeight, adWidth: adWidth, adMetaData: adMetaVal)
        
        self.clouser = {(isLoaded) in
          resolve(isLoaded)
          self.clouser = nil
        }
//        resolve(true)
      } catch {
        resolve(false)
      }
    }
  }
  
  private func setupAdView(adType: Int, adspotKey: String, adHeight: Int, adWidth: Int, adMetaData: [String: Any] = [:]) {
      
      switch adType {
        case 1:
          let container = UIView(frame: CGRect(x: 0, y: 0, width: adWidth, height: adHeight))
        createAdView(view: container, adspotKey: adspotKey, adType: .dynamicDisplay, adHeight: adHeight, adWidth: adWidth, adMetaData: adMetaData)
          self.adView?.setDisplayAdSize(displaySizes: [.size300x250])
        case 2:
          let container = UIView(frame: CGRect(x: 0, y: 0, width: adWidth, height: adHeight))
        createAdView(view: container, adspotKey: adspotKey, adType: .instreamVideo, adHeight: adHeight, adWidth: adWidth, adMetaData: adMetaData)
        case 3:
          let container = UIView(frame: CGRect(x: 0, y: 0, width: adWidth, height: adHeight))
        createAdView(view: container, adspotKey: adspotKey, adType: .interstitial, adHeight: adHeight, adWidth: adWidth, adMetaData: adMetaData)
        case 4:
          let container = CustomNativeContentStremView(frame: CGRect(x: 0, y: 0, width: adWidth, height: adHeight))
        createAdView(view: container, adspotKey: adspotKey, adType: .nativeContentStream, adHeight: adHeight, adWidth: adWidth, adMetaData: adMetaData)
        case 5:
          let container = CustomNativeContentStremView(frame: CGRect(x: 0, y: 0, width: adWidth, height: adHeight))
        createAdView(view: container, adspotKey: adspotKey, adType: .customNative, adHeight: adHeight, adWidth: adWidth, adMetaData: adMetaData)
        case 9:
            DispatchQueue.main.async {
              self.adView?.invalidateAd()            }
        default:
            let container = CustomNativeContentStremView(frame: CGRect(x: 0, y: 0, width: adWidth, height: adHeight))
        createAdView(view: container, adspotKey: adspotKey, adType: .instreamVideo, adHeight: adHeight, adWidth: adWidth, adMetaData: adMetaData)
      }
  }
  
  private func createAdView(view: UIView, adspotKey: String, adType: JioAdsFramework.AdType, adHeight: Int, adWidth: Int, adMetaData: [String: Any] = [:]) {
    JioAdSdk.setLogLevel(logLevel: .debug);
    adView = JioAdView(adSpotId: adspotKey, adType: adType, delegate: self,forPresentionClass: UIViewController(), publisherContainer: view)
    adView?.translatesAutoresizingMaskIntoConstraints = false
    adView?.setCustomView(container: view)
    adView?.setMetaData(metaDataDict: adMetaData)
    adView?.cacheAd()
  }
  
  public static func setAdView(key: String, adView: JioAdView) {
    JioAdModule.adViewMap[key] = adView
  }
  
  public static func getAdView(key: String) -> JioAdView? {
    return JioAdModule.adViewMap[key]
  }
  
  public static func removeAdView(key: String) {
    JioAdModule.adViewMap[key] = nil
  }
  
  public static func createUniqueAdViewKey(params: NSDictionary) -> String {
    let adType = params["adType"] as? Int ?? 5
    let adSpotId = params["adspotKey"] as? String ?? ""
    let adWidth = params["adWidth"] as? Int ?? 0
    let adHeight = params["adHeight"] as? Int ?? 0
    let adMetaData = params["adMetaData"] as? [String: Any] ?? [:]
    
    let sortedMeta = adMetaData.sorted { $0.key < $1.key }
    let metaKey = sortedMeta.map { "\($0.key)=\($0.value)" }.joined(separator: "&")
    let rawKey = "\(adSpotId)|\(adType)|\(adWidth)|\(adHeight)|\(metaKey)"
    return rawKey.md5()
  }
  
  private func callOnce(_ isLoaded: Bool) {
      if let callback = clouser {
          callback(isLoaded)
          clouser = nil // ❗️Ensure it's only called once
      }
  }
    
  func onAdReceived(adView: JioAdView) {
      print("onAdReceived",adView)
      JioAdModule.setAdView(key: JioAdModule.createUniqueAdViewKey(params: self.params), adView: self.adView!)
      callOnce(true)
  }
  
  func onAdPrepared(adView: JioAdView) {
      print( "onAdPrepared", adView)
      callOnce(true)
  }
  
  func onAdRender(adView: JioAdView) {}
  
  func onAdClicked(adView: JioAdView) {}
  
  func onAdRefresh(adView: JioAdView) {}
  
  func onAdFailedToLoad(adView: JioAdView, error: JioAdError) {
      callOnce(false)
  }
  
  func onAdMediaEnd(adView: JioAdView) {}
  
  func onAdClosed(adView: JioAdView, isVideoCompleted: Bool, isEligibleForReward: Bool) {}
  
  func onAdMediaStart(adView: JioAdView) {}
  
  func onAdSkippable(adView: JioAdView) {}
  
  func onAdMediaExpand(adView: JioAdView) {}
  
  func onAdMediaCollapse(adView: JioAdView) {}
  
  func onMediaPlaybackChange(adView: JioAdView, mediaPlayBack: MediaPlayBack) {}
  
  func onAdDataPrepared(videoAd: VideoAd?, isLastAd: Bool) {}
  
  func onAdDataPrepared(nativeAd: NativeAd?, isLastAd: Bool) {}
  
  func onAdChange(adView: JioAdView, trackNo: Int) {}
  
  func mediationRequesting() {}
  
  func mediationLoadAd() {}
  
  
}
