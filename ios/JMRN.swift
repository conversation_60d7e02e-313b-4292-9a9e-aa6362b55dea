//
//  JMRN.swift
//  JioMart
//
//  Created by <PERSON>ish<PERSON> Sa<PERSON> on 28/04/25.
//

import Foundation

typealias InitialProps = InfoPlist

@objc(JMRN)
class JMRN: NSObject {
  @objc public static let shared = JMRN()
  @objc public var initialProps: [String: Any] = [:]
  
  private override init(){
    super.init();
    self.setInitialPropsValue()
  }
  
  private func setInitialPropsValue() {
    self.initialProps[InitialProps.googleMapKey] = JMInfoPlist.googleMapKey
    self.initialProps["env"] = JMInfoPlist.appEnvironment
    self.initialProps["appLevelParams"] = getAppLevelConfigurations()
   
    if let pendingDeepLink = UserDefaults.standard.string(forKey: JMConstant.UserDefaultsKeys.pendingDeepLink) {
      self.initialProps["mUri"] = pendingDeepLink
      UserDefaults.standard.removeObject(forKey: JMConstant.UserDefaultsKeys.pendingDeepLink)
      UserDefaults.standard.synchronize()
    }
  }
  
  private func getAppLevelConfigurations() -> [String: Any] {
    return [
      "oneRetailConfiguration": [
        "apiDomain": JMInfoPlist.oneRetailDomain,
        "clientId": JMInfoPlist.oneRetailClientID,
        "returenUiUrl": JMInfoPlist.oneRetailReturnUIURL
      ],
      "appsFlyerConfiguration": [
        "appsFlyerDevKey": JMInfoPlist.appsflyerDevId(),
        "appsFlyerAppId": JMInfoPlist.appsFlyerAppId(),
        "appsFlyerDomain": JMInfoPlist.appsflyerDomain
      ],
      "cleverTapConfiguration": [
        "cleverTapAccountId": JMInfoPlist.cleverTapAccountID,
        "cleverTapAccountToken": JMInfoPlist.cleverTapToken
      ],
      "googleMapKeyConfiguration": [
        "GOOGLE_MAPS_API_KEY": JMInfoPlist.googleMapKey
      ],
      "polygonStoreAccounConfiguration": [
        "polygonStoreAccounToken": JMInfoPlist.polygonAccountToken
      ],
      "omsConfiguration": [
        "omsApplicationId": JMInfoPlist.xomsApplicationID,
        "omsApplicationToken": JMInfoPlist.xAppToken
      ]
    ]
  }
}
