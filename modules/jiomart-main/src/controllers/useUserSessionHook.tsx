import React from 'react';
import {JMDatabaseManager} from '../../../jiomart-networkmanager/src/db/JMDatabaseManager';
import {addStringPref} from '../../../jiomart-common/src/JMAsyncStorageHelper';
import {AsyncStorageKeys} from '../../../jiomart-common/src/JMConstants';
import JMUserApiNetworkController from '../../../jiomart-networkmanager/src/JMNetworkController/JMUserAPINetworkController';
import {useGlobalState} from '../../../jiomart-general/src/context/JMGlobalStateProvider';
import {isNullOrUndefinedOrEmpty} from '../../../jiomart-common/src/JMObjectUtility';
import {JMAddressModel} from '../../../jiomart-common/src/uiModals/JMAddressModel';
import useAddressOperation from '../../../jiomart-address/src/hooks/useAddressOperation';
import {logAnalyticsEvent} from '../../../jiomart-common/src/JMAnalyticsUtility';
import {AnalyticsParams} from '../../../jiomart-common/src/AnalyticsParams';
import { JHExceptionLogger, JMLogger } from '../../../jiomart-common/src/utils/JMLogger';

const userApiNetworkController = new JMUserApiNetworkController();

const useUserSessionHook = () => {
  const {setUserInitials} = useGlobalState();
  const {checkAndSetPincode} = useAddressOperation();

  const saveUserSessionDetails = async (sessionDetails: any) => {
    try{
      const mstarJsonString = JSON.stringify(sessionDetails?.data?.mstar_session);
      const craJsonString = JSON.stringify(sessionDetails?.data?.cra_session);
      const jcpJsonString = JSON.stringify(sessionDetails?.data?.jcp_session);
      await addStringPref(AsyncStorageKeys.CRA_DETAILS, JSON.stringify(sessionDetails));
      await JMDatabaseManager.user.saveUserSession(mstarJsonString);
      await addStringPref(AsyncStorageKeys.CRA_USER_SESSION_DATA, craJsonString);
      await addStringPref(AsyncStorageKeys.JCP_USER_SESSION_DATA, jcpJsonString);

    } catch(error){
      JHExceptionLogger.log('saveUserSessionDetails error:', error);
    }

    const gaModelLogin = {
      channel: 'firebase',
      eventName: 'login',
      payload: {
        [AnalyticsParams.METHOD]: 'login',
      },
    };

    logAnalyticsEvent(gaModelLogin);

    const gaModelLoginSuccess = {
      channel: 'firebase',
      eventName: 'login',
      payload: {
        category: 'login',
        action: 'login success',
        label: 'login_success',
        pageType: 'login otp page',
      },
    };
    logAnalyticsEvent(gaModelLoginSuccess);

    
  };

  const saveProfileDetails = async () => {
    try{
      JMLogger.log("saveProfileDetails profileResponse called")
      const profileResponse = await userApiNetworkController.fetchUserDetails();
      JMDatabaseManager.user.saveUserDetails(profileResponse)
      setUserInitials(profileResponse?.full_name);
    } catch(error){
      JHExceptionLogger.log('saveProfileDetails error:', error);
    }

  };

  const handleAddressAfterLogin = async () => {
    const defaultAddress = await JMDatabaseManager.address.getDefaultAddress();
    if (!isNullOrUndefinedOrEmpty(defaultAddress)) {
      try {
        const addressData = JSON.parse(defaultAddress ?? '') as JMAddressModel;
        if (addressData) {
          await checkAndSetPincode(
            {
              pincode: addressData?.pin,
              state: addressData?.state,
              city: addressData?.city,
            },
            true,
          );
        }
      } catch (error) {
        JHExceptionLogger.log('handleAddressAfterLogin error:', error);
      }
    }
  };

  return {
    saveUserSessionDetails,
    saveProfileDetails,
    handleAddressAfterLogin,
  };
};

export default useUserSessionHook;
