import {useFocusEffect} from '@react-navigation/native';
import React, {useCallback, useState} from 'react';
import {
  AppScreens,
  type ScreenProps,
} from '../../../jiomart-common/src/JMAppScreenEntry';
import {JMSharedViewModel} from '../../../jiomart-common/src/JMSharedViewModel';
import JMBridgeEmitterState from '../../../jiomart-general/src/bridge/JMBridgeEmitter';
import ScreenSlot, {
  DeeplinkHandler,
  createEventFinalBean,
} from '../../../jiomart-general/src/ui/JMScreenSlot';
import useGlobalBottomSheetController from '../../../jiomart-main/src/features/StartUp/controllers/useGlobalBottomSheetController';
import GlobalBottomSheet from '../../../jiomart-main/src/features/StartUp/GlobalBottomSheet';
import {
  getBaseURL,
  isHomeUrl,
} from '../../../jiomart-networkmanager/src/JMEnvironmentConfig';
import useWebViewController from '../../../jiomart-webmanager/src/useWebViewController';
import WebViewUtility from '../../../jiomart-webmanager/src/WebViewUtility';
import BottomSheet from '../../../jiomart-general/src/ui/BottomSheet/BottomSheet';
import JMQuickBtmSheet from '../BottomSheet/JMQuickBtmSheet';
import {Image, StyleSheet} from 'react-native';
import {rh, rw} from '../../../jiomart-common/src/JMResponsive';
import useQuickBtmSheet from '../hooks/useQuickBtmSheet';
import type {GlobalBottomSheetProps} from '../../../jiomart-common/src/JMScreenSlot.types';
import {logAnalyticsEvent} from '../../../jiomart-common/src/JMAnalyticsUtility';
import useAppStatePermissionSync from '../../../jiomart-main/src/controllers/useAppStatePermissionSync';

export type JProps = ScreenProps<typeof AppScreens.HOME_SCREEN>;

const JMHomeScreen = (props: JProps) => {
  const {
    navigation,
    navigationBean,
    webState,
    webViewRef,
    handleJSEvents,
    onUrlOverride,
    onError,
    handleBackPress,
    handleNavigationStateChange,
    eventTriggerBean,
    screenSpecificQcMessage,
    handleWebViewLoadEnd,
    quickBottomSheetIdentifierBean,
    newTagImage,
    quickCommerceConfig,
  } = useWebViewController(props);
  useAppStatePermissionSync();
  const {showQuickBtmSheet, openQuickBtmSheet, closeQuickBtmSheet} =
    useQuickBtmSheet();

  const {checkAndOpenNextSheet} = useGlobalBottomSheetController();

  const [isBSNonCancellable, setBSNonCancellable] = useState(false);

  const {} = JMBridgeEmitterState();

  const webViewMainUi = () => {
    console.log('webState.content', webState.content);
    return (
      <WebViewUtility
        source={{uri: webState.content || getBaseURL()}}
        onMessage={handleJSEvents}
        onRef={ref => {
          webViewRef.current = ref;
        }}
        onError={onError}
        javaScriptEnabled={true}
        onNavigationStateChange={handleNavigationStateChange}
        onShouldStartLoadWithRequest={onUrlOverride}
        onLoadEnd={handleWebViewLoadEnd}
      />
    );
  };

  useFocusEffect(
    useCallback(() => {
            if (
        isHomeUrl(webState.content ?? '') &&
        !JMSharedViewModel.Instance.getNavigationData()
      ) {
        checkAndOpenNextSheet();
      }
    }, []),
  );

  const bottomSheetContent = ({
    openDeliverToBarPincodeBtmSheet,
  }: GlobalBottomSheetProps) => (
    <>
      <GlobalBottomSheet
        navigation={navigation}
        openPincodeBottomSheet={isNonCancellable => {
          setBSNonCancellable(isNonCancellable);
          openDeliverToBarPincodeBtmSheet();
        }}
        openQuickBtmSheet={openQuickBtmSheet}
        quickBottomSheetIdentifierBean={quickBottomSheetIdentifierBean}
      />
      <BottomSheet
        visible={showQuickBtmSheet}
        enableKeyboarAvoidingView
        onBackDropClick={() => {
          closeQuickBtmSheet();
          logAnalyticsEvent(
            quickCommerceConfig?.analyticEvent
              ?.quickBottomSheet_outsideTheBottomsheet_click,
          );
        }}
        onDrag={() => {
          closeQuickBtmSheet();
          logAnalyticsEvent(
            quickCommerceConfig?.analyticEvent
              ?.quickBottomSheet_outsideTheBottomsheet_click,
          );
        }}
        disabledBackDropClick={true}
        headerComponent={
          <Image
            source={{
              uri: newTagImage,
            }}
            style={styles.newTagImageStyle}
            resizeMode="cover"
          />
        }>
        <JMQuickBtmSheet
          quickBottomSheetIdentifierBean={quickBottomSheetIdentifierBean?.[0]}
          onClose={() => {
            closeQuickBtmSheet();
            logAnalyticsEvent(
              quickCommerceConfig?.analyticEvent
                ?.quickBottomSheet_close_buttonClick,
            );
          }}
          onGotIt={() => {
            closeQuickBtmSheet();
            logAnalyticsEvent(
              quickCommerceConfig?.analyticEvent
                ?.quickBottomSheet_gotIt_buttonClick,
            );
          }}
        />
      </BottomSheet>
    </>
  );

  return (
    <DeeplinkHandler
      navigationBean={navigationBean}
      navigation={navigation}
      children={bean => (
        <ScreenSlot
          navigationBean={createEventFinalBean(bean, eventTriggerBean)}
          navigation={navigation}
          onCustomBackPress={handleBackPress}
          bottomsheetDismissable={{disabledBackDropClick: isBSNonCancellable}}
          bottomSheetContent={bottomSheetContent}
          setBSNonCancellableValue={(value: boolean) => {
            if (isBSNonCancellable !== value) {
              setBSNonCancellable(value);
            }
          }}
          children={_ => {
            return webViewMainUi();
          }}
          deliverToBarData={{qcMessage: screenSpecificQcMessage}}
          enableOpenPincodeBtmSheetListener={true}
        />
      )}
    />
  );
};

const styles = StyleSheet.create({
  newTagImageStyle: {
    width: rw(80),
    height: rh(36),
    position: 'absolute',
    top: rh(-18),
    alignSelf: 'center',
  },
});

export default JMHomeScreen;
