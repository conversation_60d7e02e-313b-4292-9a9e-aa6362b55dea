package com.jcpjiomart.jioAds

import android.app.Activity
import android.content.Context
import android.graphics.Outline
import android.text.TextUtils
import android.util.DisplayMetrics
import android.util.Log
import android.util.TypedValue
import android.view.View
import android.view.ViewGroup
import android.view.ViewOutlineProvider
import android.widget.FrameLayout
import android.widget.RelativeLayout
import androidx.core.os.bundleOf
import com.clevertap.react.CleverTapEventEmitter.reactContext
import com.facebook.react.bridge.Arguments
import com.facebook.react.bridge.LifecycleEventListener
import com.facebook.react.bridge.ReadableMap
import com.facebook.react.modules.core.DeviceEventManagerModule
import com.facebook.react.uimanager.ThemedReactContext
import com.facebook.react.uimanager.events.RCTEventEmitter
import com.facebook.react.views.view.ReactViewGroup
import com.facebook.react.bridge.Callback
import com.facebook.react.bridge.WritableMap
import com.google.gson.Gson
import com.jcpjiomart.MainActivity
import com.jcpjiomart.R
import com.jio.jioads.adinterfaces.JioAdError
import com.jio.jioads.adinterfaces.JioAdListener
import com.jio.jioads.adinterfaces.JioAdView
import com.jio.jioads.adinterfaces.JioAdView.AD_TYPE
import org.json.JSONObject


enum class AdEventName(val event: String) {
    AdRecived("adRecived"),
    AdPrepared("adPrepared"),
    AdRender("adRender"),
    AdClicked("adClicked"),
    AdRefresh("adRefresh"),
    AdFailed("adFailed"),
    AdMediaEnd("adMediaEnd"),
    AdClosed("adClosed"),
    AdMediaStart("adMediaStart"),
    AdSkippable("adSkippable"),
    AdMediaExpand("adMediaExpand"),
    AdMediaCollapse("adMediaCollapse"),
    AdMediaPlaybackChange("adMediaPlaybackChange"),
    AdDataPrepared("adDataPrepared"),
    AdChange("adChange"),
    MediationRequesting("mediationRequesting"),
    MediationAd("mediationAd")
}

class JioAdViewGroup(context: ThemedReactContext) : ReactViewGroup(context),
    LifecycleEventListener {
    protected var mJioAdView: JioAdView? = null
    private var mAdType: AD_TYPE? = null
    private var mAdspotKey: String? = null
    private var adViewUniquekey: String? = null

    var mWidth = 0
    var pxH = 0
    var mHeight = 0
    var mCustomHeight = 0
    var mCustomWidth = 0
    var adMetaData: HashMap<String, String>? = null
    var context: ThemedReactContext? = null
    fun setAdType(adType: Int) {

        mAdType = when (adType) {
            1 -> AD_TYPE.DYNAMIC_DISPLAY
            2 -> AD_TYPE.INSTREAM_VIDEO
            3 -> AD_TYPE.INTERSTITIAL
            4 -> AD_TYPE.CONTENT_STREAM
            5 -> AD_TYPE.CUSTOM_NATIVE
            6 -> AD_TYPE.INSTREAM_AUDIO
            else -> AD_TYPE.INSTREAM_VIDEO
        }
    }

    fun setAdData(adData: ReadableMap) {
        Log.e("Test", "inside setAdData: $adData")
        if (adData != null && adData.hasKey("adspotKey") && !TextUtils.isEmpty(adData.getString("adspotKey"))) {
            mAdspotKey = adData.getString("adspotKey")
            if (adData.hasKey("adType")) {
                setAdType(adData.getInt("adType"))
            }
            if (adData.hasKey("adCustomWidth")) {
                mCustomWidth = adData.getInt("adCustomWidth")
            }
            if (adData.hasKey("adCustomHeight")) {
                mCustomHeight = adData.getInt("adCustomHeight")
            }
            if (adData.hasKey("adHeight")) {
                mHeight = adData.getInt("adHeight")
            }
            if (adData.hasKey("adWidth")) {
                mWidth = adData.getInt("adWidth")
            }
            if (adData.hasKey("adMetaData")) {
                adMetaData = setAdMetaData(adData.getMap("adMetaData")!!)
            }

            val adParams: WritableMap = Arguments.createMap()

            // Required ad params
            adParams.putString("adspotKey", mAdspotKey ?: "")
            adParams.putInt("adType", mAdType?.ordinal ?: 5)
            adParams.putInt("adWidth", mWidth)
            adParams.putInt("adHeight", mHeight)

            adViewUniquekey = JioAdModule.createUniqueAdViewKey(adParams)
            createAdViewIfCan()
        }
    }

    fun setAdMetaData(params: ReadableMap): HashMap<String, String>? {
        try {
            if (params.toHashMap().size > 0) {
                val bundle = bundleOf()
                params.toHashMap().forEach { entry ->
                    bundle.putString(entry.key, entry.value.toString())
                }
                return modifyMetaData(params.toHashMap())
            }
            return null
        } catch (ex: Exception) {
            return null
        }
    }
    // Method to set the onChange callback

    private fun triggerOnChange(eventData: WritableMap) {
        sendEvent("onChange", eventData)
    }

    private fun sendEvent(eventName: String, params: WritableMap) {
        reactContext?.getJSModule(RCTEventEmitter::class.java)
            ?.receiveEvent(id, eventName, params)
    }

    fun onAdStateChanged(state: String, value: Map<String, String?>? = null) {
        val eventData = Arguments.createMap().apply {
            putString("event", state)
            putMap("value", convertToReadableMap(value))
        }
        triggerOnChange(eventData)
    }

    fun convertToReadableMap(input: Map<String, String?>?): ReadableMap? {
        if (input == null) return null

        val map: WritableMap = Arguments.createMap()
        input.forEach { (key, value) ->
            if (value != null) {
                map.putString(key, value)
            }
        }
        return map
    }


    private fun modifyMetaData(metaDataHashMapParams: HashMap<String, Any>?): HashMap<String, String>? {
        metaDataHashMapParams?.let { params ->
           return convertMap(params)
        }
        return null
    }


    private fun dp2px(dp: Int, dm: DisplayMetrics): Int {
        return Math.round(TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dp.toFloat(), dm))
    }

    private fun createAdViewIfCan() {
        if (mJioAdView == null && mAdspotKey != null) {
            context = getContext() as ThemedReactContext
            if (context!!.hasCurrentActivity()) {
                val cacheAdView = JioAdModule.getAdView(adViewUniquekey!!)
                if (cacheAdView != null) {
                    mJioAdView = cacheAdView
                } else {
                    mJioAdView = JioAdView(
                        context!!.currentActivity!!, mAdspotKey!!,
                        mAdType!!
                    )
                }
                setListenerToAdview(context!!.currentActivity)
                val dm = context!!.resources.displayMetrics

                //                -----------For custom native uncomment below commneted section---------------------
                if (mAdType == AD_TYPE.CUSTOM_NATIVE) {

                    mJioAdView!!.setCustomNativeAdContainer(R.layout.jio_custom_native_layout)

                    mJioAdView!!.setCustomImageSize(mWidth, mHeight)

                }
                mJioAdView!!.setMetaData(adMetaData)
                if (cacheAdView != null) {
                    drawView();
                    mJioAdView!!.loadAd()
                    onAdStateChanged(AdEventName.AdPrepared.event)
                } else {
                    mJioAdView?.enableClickCallbacktoApp(true)
                    mJioAdView!!.cacheAd()
                }
            }
        }
    }

    private fun convertMap(originalMap: HashMap<String, Any>?): HashMap<String, String>? {
        return originalMap?.mapValues { it.value.toString() } as HashMap<String, String>?
    }

    init {
        context.addLifecycleEventListener(this)
    }

    override fun onHostResume() {}
    override fun onHostPause() {}
    override fun onHostDestroy() {
        if (mJioAdView != null) {
            mJioAdView!!.onDestroy()
            JioAdModule.clearAdView(adViewUniquekey!!)
        }
    }

    fun convertPixelsToDp(px: Float, dm: DisplayMetrics): Int {
        return Math.round(px / (dm.densityDpi.toFloat() / DisplayMetrics.DENSITY_DEFAULT))
    }

    private fun drawView() {

        val dm = context!!.resources.displayMetrics
        val width = (dm.widthPixels - convertDpToPx(context!!, 44f)).toInt()
        val customWidth = convertDpToPx(context!!, mCustomWidth.toFloat()).toInt()

        val viewGroup = mJioAdView!!.getCustomNativeContainer()
        val imageView = viewGroup!!.findViewWithTag<View>("NativeCustomImageLayout")
        val height = convertDpToPx(context!!, mCustomHeight.toFloat())// width / 1.91
        val imageLayout = RelativeLayout.LayoutParams(LayoutParams(customWidth, height.toInt()))
        imageView.layoutParams = imageLayout

        pxH = convertDpToPx(context!!, mCustomHeight.toFloat()).toInt()//width/(328/224)
        //Put height of required adView
        mJioAdView!!.getAdView()!!.measure(customWidth, pxH) //jio ad layout
        mJioAdView!!.getAdView()!!.layout(0, 0, customWidth, pxH)
        if (mJioAdView!!.getAdView()!!.parent != null) {
            (mJioAdView!!.getAdView()!!.parent as ViewGroup).removeView(mJioAdView!!.getAdView())
        }

        mJioAdView!!.getAdView()?.outlineProvider = object : ViewOutlineProvider() {
            override fun getOutline(view: View, outline: Outline) {
                outline.setRoundRect(0, 0, view.width, view.height, 24f)
            }
        }

        mJioAdView!!.getAdView()?.clipToOutline = true
        val viewGroupLayoutParams = FrameLayout.LayoutParams(customWidth, pxH)

        Log.e(
            "TAG",
            "mJioAdView.getAdView() W: " + mJioAdView!!.getAdView()!!.width + "||H" + mJioAdView!!.getAdView()!!.height
        )
        //mJioAdView.getAdView().setBackgroundColor(Color.parseColor("#912b0f"));
        addView(mJioAdView!!.getAdView(), viewGroupLayoutParams)

    }


    fun convertDpToPx(context: Context, dp: Float): Float {
        return dp * context.resources.displayMetrics.density
    }

    fun setListenerToAdview(activity: Activity?) {
        mJioAdView!!.setAdListener(object : JioAdListener() {
            override fun onAdMediaEnd(jioAdView: JioAdView?) {
                Log.d("Test", "onAdMediaEnd callback")
                // Toast.makeText(context, "onAdMediaEnd", Toast.LENGTH_SHORT).show()

                //Notify in App.js
                /*WritableMap payload = Arguments.createMap();
                // Put data to map
                payload.putString("JioAdViewParam", jioAdView.getAdSpotId() + "_" + jioAdView.getAdType());
                // Emitting event from java code
                context.getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
                        .emit("onAdMediaEnd", payload);*/
                onAdStateChanged(AdEventName.AdMediaEnd.event)
            }

            override fun onAdClosed(
                jioAdView: JioAdView?,
                isVideoCompleted: Boolean,
                isEligibleForReward: Boolean
            ) {
                Log.d(
                    "Test",
                    "onAdClosed callback=> isVideoCompleted: $isVideoCompleted isEligibleForReward: $isEligibleForReward"
                )
                onAdStateChanged(AdEventName.AdClosed.event)
            }

            override fun onAdFailedToLoad(adview: JioAdView?, jioAdError: JioAdError?) {
                Log.d("Test", "onAdFailedToLoad callback." + jioAdError!!.getErrorDescription())
                try {
                    val jsonObj = JSONObject()
                    jsonObj.put("loadStatus", "adFailed")
                    jsonObj.put("adSpotId", mAdspotKey)

//                    (JioMart.mActivity as MainActivity).getReactInstanceManagerLocal()?.currentReactContext?.getJSModule(
//                        DeviceEventManagerModule.RCTDeviceEventEmitter::class.java
//                    )?.emit("jioAddLoadStatus", jsonObj.toString())

                    // Console.debug("onAdFailedToLoad","onAdFailedToLoad event emitted--"+mAdspotKey+jsonObj.toString())
                } catch (ex: Exception) {
                    // JioExceptionHandler.handle(ex)
                }
                onAdStateChanged(AdEventName.AdFailed.event)
            }

            override fun onAdPrepared(adView: JioAdView?) {
                //setEventName("adPrepared")
                Log.d("Test", "onAdPrepared callback")
                drawView();
                mJioAdView!!.loadAd()
                onAdStateChanged(AdEventName.AdPrepared.event)
            }

            override fun onAdChange(jioAdView: JioAdView?, adNumber: Int) {
                Log.d("Test", "")
                onAdStateChanged(AdEventName.AdChange.event)
            }

            override fun onAdReceived(jioAdView: JioAdView?) {
                Log.d("Test", "onAdReceived callback")
                onAdStateChanged(AdEventName.AdRecived.event)
            }

            override fun onAdClicked(adview: JioAdView?) {
                Log.d("Test", "onAdClicked callback")
                val clickUrls = mJioAdView?.getAdAllClickUrl()
                onAdStateChanged(AdEventName.AdClicked.event, clickUrls)
            }

            override fun onAdRender(adview: JioAdView?) {
                Log.d("Test", "onAdRender callback")
                try {
                    mJioAdView!!.muteVideoAd()
                    // Console.debug("jioAddLoadStatus","onAdRender mJioAdView muted")
                } catch (ex: Exception) {
                    // JioExceptionHandler.handle(ex)
                }
                try {
                    val jsonObj = JSONObject()
                    jsonObj.put("loadStatus", "onAdRender")
//                    (JioMart.mActivity as MainActivity).getReactInstanceManagerLocal()?.currentReactContext?.getJSModule(
//                        DeviceEventManagerModule.RCTDeviceEventEmitter::class.java
//                    )?.emit("jioAddLoadStatus", jsonObj.toString())
                    // Console.debug("jioAddLoadStatus","onAdPrepared event emitted")
                } catch (ex: Exception) {
                    // JioExceptionHandler.handle(ex)
                }
                drawView()
                onAdStateChanged(AdEventName.AdRender.event)
            }

            override fun onAdMediaStart(adview: JioAdView?) {
                Log.d("Test", "onAdMediaStart callback")
                onAdStateChanged(AdEventName.AdMediaStart.event)
            }

            override fun onAdRefresh(adview: JioAdView?) {
                Log.d("Test", "onAdRefresh callback")
                drawView()
                onAdStateChanged(AdEventName.AdRefresh.event)
            }

            override fun onAdMediaExpand(adview: JioAdView?) {
                Log.d("Test", "onAdMediaExpand callback")
                onAdStateChanged(AdEventName.AdMediaExpand.event)
            }

            override fun onAdMediaCollapse(adview: JioAdView?) {
                Log.d("Test", "onAdMediaCollapse callback")
                onAdStateChanged(AdEventName.AdMediaCollapse.event)
            }

            override fun onAdSkippable(jioAdView: JioAdView?) {
                Log.d("Test", "onAdSkippable callback")
                onAdStateChanged(AdEventName.AdSkippable.event)
            }
        })
    }

    private fun measureView(viewGroup: ViewGroup?, width: Int /*in DP*/, height: Int /*in DP*/) {
        val dm = context!!.resources.displayMetrics
        var child: View? = null
        if (viewGroup != null) {
            child = viewGroup.getChildAt(0)
        }
        if (child != null) {
            child.measure(dp2px(width, dm), dp2px(height, dm))
            child.layout(0, 0, dp2px(width, dm), dp2px(height, dm))
        }
    }
}