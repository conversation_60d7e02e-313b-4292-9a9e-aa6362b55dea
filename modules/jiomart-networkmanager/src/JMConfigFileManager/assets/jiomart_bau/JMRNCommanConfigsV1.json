{"location": {"pincode": "400001", "city": "Mumbai", "name": "", "state": "Maharashtra", "country_iso_code": "IN", "country": "India"}, "privacyPolicyConfig": {"isVisible": true, "title": "Privacy Policy", "regularString": "Please note that our Privacy Policy has been updated and we have also added Retail Account Privacy Policy. By continuing to use our platform and its services, you consent to the updated policies.", "clickableString": "Privacy Policy,Retail Account Privacy Policy", "redirectionUrl": "privacy-policy,https://account.relianceretail.com/privacy-policy/", "Privacy Policy": {"navTitle": "Privacy Policy", "source": "", "destination": "CommonWebViewScreen", "actionUrl": "privacy-policy", "actionType": "T003", "bundle": "", "navigationType": "push", "headerType": 1}, "Retail Account Privacy Policy": {"navTitle": "Retail Account Privacy Policy", "source": "", "destination": "CommonWebViewScreen", "actionUrl": "https://account.cctz0.de/privacy-policy/", "actionType": "T003", "bundle": "", "navigationType": "push", "headerType": 1}, "buttonText": "Agree"}, "permissionConfig": {"isVisible": true, "title": "Access Required", "subTitle": "Please provide us with the required permissions for a more personalised shopping experience.", "buttonText": "Proceed", "permissions": {"ios": [{"permissionTitle": {"text": "Allow location access"}, "permissionSubTitle": {"text": "Stay updated with location-based offers and product availability."}, "permissionIcon": {"ic": "IcLocation"}}, {"permissionTitle": {"text": "Allow Notifications"}, "permissionSubTitle": {"text": "Get notified about the latest offers, deals & new arrivals."}, "permissionIcon": {"ic": "IcNotification"}}], "android": [{"permissionTitle": {"text": "Allow location access"}, "permissionSubTitle": {"text": "Stay updated with location-based offers and product availability."}, "permissionIcon": {"ic": "IcLocation"}}, {"permissionTitle": {"text": "Allow Access to SMS"}, "permissionSubTitle": {"text": "Track your orders better by enabling real time order status notifications."}, "permissionIcon": {"ic": "IcSms"}}, {"permissionTitle": {"text": "Allow Notifications"}, "permissionSubTitle": {"text": "Get notified about the latest offers, deals & new arrivals."}, "permissionIcon": {"ic": "IcNotification"}}]}, "requestPermissions": {"ios": ["ios.permission.LOCATION_WHEN_IN_USE", "ios.permission.APP_TRACKING_TRANSPARENCY", "IOS_PUSH_NOTIFICATION"], "android": ["android.permission.ACCESS_FINE_LOCATION", "android.permission.READ_SMS", "android.permission.POST_NOTIFICATIONS"]}, "alert": {"blockedLocation": {"title": "", "message": "Please provide your location or search using your closest landmark/apartment name", "button": {"text": "Enable Now"}}}}, "enableLocationConfig": {"isVisible": true, "title": "Enable Location Services", "subTitle": "Please enable your location from your device settings for a improved delivery experience.", "closeIcon": {"isIconVisible": true, "iconAsset": "IcClose"}, "enableLocationBtn": {"isButtonVisible": true, "buttonText": "Enable Location"}, "selectLocationBtn": {"isButtonVisible": true, "buttonText": "Select Location Manually"}}, "quickCommerceConfig": {"featureEnabled": true, "prefixTitle": "Delivery to:", "quickDeliveryKey": "jiomart_quick", "scheduledDeliveryKey": "scheduled_delivery", "quickDeliveryMessage": "Free Delivery in 10 to 30 minutes", "scheduledDeliveryMessage": "Scheduled Delivery", "verticalCode": ["GROCERIES"], "quickImageUrl": "https://myjiostatic.cdn.jio.com/JioMart/Common/Group_1171279821.svg", "quickActiveImageUrl": "https://myjiostatic.cdn.jio.com/JioMart/Common/<EMAIL>", "quickInactiveImageUrl": "https://myjiostatic.cdn.jio.com/JioMart/Common/<EMAIL>", "scheduledActiveImageUrl": "https://myjiostatic.cdn.jio.com/JioMart/Common/<EMAIL>", "scheduledInactiveImageUrl": "https://myjiostatic.cdn.jio.com/JioMart/Common/<EMAIL>", "initialQcTab": "jiomart_quick", "QuickBottomSheetViewDetails": [{"appVersion": 2060, "versionType": 2, "isQuickBottomSheetEnabled": true, "CloseButtonIconAsset": "IcClose", "IsCloseIconVisible": true, "ButtonText": "Got it", "IsButtonVisible": true, "ShowQuickIcon": true, "TopNewIconAsset": "https://myjiostatic.cdn.jio.com/JioMart/Common/Group_New.png", "QuickBottomsheetVerticleCodes": ["electronics", "fashion", "localshops"], "backgroundImage": "https://myjiostatic.cdn.jio.com/JioMart/Common/quickBtmSheet_Background.png", "QuickBottomsheetTypeIdentifiers": [{"VerticleCode": ["electronics", "fashion", "localshops"], "TitleText": " Fashion, Electronics & Local Shop items", "SubTitleText": "are now available on", "SubTitleQuickIconAsset": "https://myjiostatic.cdn.jio.com/JioMart/Common/Group_Quick.png", "BannerImage": "https://myjiostatic.cdn.jio.com/JioMart/Common/Group_Fashion_Electronics_Localshop_1.png"}, {"VerticleCode": ["electronics", "fashion"], "TitleText": "Fashion & Electronics", "SubTitleText": "are now available on", "SubTitleQuickIconAsset": "https://myjiostatic.cdn.jio.com/JioMart/Common/Group_Quick.png", "BannerImage": "https://myjiostatic.cdn.jio.com/JioMart/Common/Fashion_Electronics_1.png"}, {"VerticleCode": ["electronics", "localshops"], "TitleText": "Electronics & Items from Local Shop", "SubTitleText": "are now available on", "SubTitleQuickIconAsset": "https://myjiostatic.cdn.jio.com/JioMart/Common/Group_Quick.png", "BannerImage": "https://myjiostatic.cdn.jio.com/JioMart/Common/Fashion_Electronics_1.png"}, {"VerticleCode": ["localshops", "fashion"], "TitleText": "Fashion & Items from Local Shop", "SubTitleText": "are now available on", "SubTitleQuickIconAsset": "https://myjiostatic.cdn.jio.com/JioMart/Common/Group_Quick.png", "BannerImage": "https://myjiostatic.cdn.jio.com/JioMart/Common/Fashion_Localshop_1.png"}, {"VerticleCode": ["localshops"], "TitleText": "Items from Local Shop", "SubTitleText": "are now available on", "SubTitleQuickIconAsset": "https://myjiostatic.cdn.jio.com/JioMart/Common/Group_Quick.png", "BannerImage": "https://myjiostatic.cdn.jio.com/JioMart/Common/Localshop_1.png"}, {"VerticleCode": ["electronics"], "TitleText": "Electronics", "SubTitleText": "is now available on", "SubTitleQuickIconAsset": "https://myjiostatic.cdn.jio.com/JioMart/Common/Group_Quick.png", "BannerImage": "https://myjiostatic.cdn.jio.com/JioMart/Common/electronics_1.png"}, {"VerticleCode": ["fashion"], "TitleText": "Fashion", "SubTitleText": "is now available on", "SubTitleQuickIconAsset": "https://myjiostatic.cdn.jio.com/JioMart/Common/Group_Quick.png", "BannerImage": "https://myjiostatic.cdn.jio.com/JioMart/Common/Fashion_1.png"}]}], "analyticEvent": {"quickBottomSheet_close_buttonClick": {"channel": "firebase", "eventName": "event_banner_interaction", "payload": {"category": "promotion banner", "action": "banner close", "label": "promotion banner", "banner_name": "Quick FELS", "pincode": "[PINCODE]", "page_type": "home page", "journey": "Quick", "platform": "[PLATFORM]", "user ID": "[USER_ID]", "login_status": "[LOGIN_STATUS]"}}, "quickBottomSheet_gotIt_buttonClick": {"channel": "firebase", "eventName": "event_banner_interaction", "payload": {"category": "promotion banner", "action": "Got it", "label": "promotion banner", "banner_id": "123456", "banner_name": "Quick FELS", "pincode": "[PINCODE]", "page_type": "home page", "journey": "Quick", "platform": "[PLATFORM]", "user ID": "[USER_ID]", "login_status": "[LOGIN_STATUS]"}}, "quickBottomSheet_outsideTheBottomsheet_click": {"channel": "firebase", "eventName": "event_banner_interaction", "payload": {"category": "promotion banner", "action": "Else where", "label": "promotion banner", "banner_name": "Quick FELS", "pincode": "[PINCODE]", "page_type": "home page", "journey": "Quick", "platform": "[PLATFORM]", "user ID": "[USER_ID]", "login_status": "[LOGIN_STATUS]"}}}}, "jmFlags": {"homeRN": [{"rnVersion": "", "iosVersion": "2051", "androidVersion": "2063", "data": true}], "showPhoneHint": [{"rnVersion": "", "iosVersion": "", "androidVersion": "", "data": true}], "autoFetchOtp": [{"rnVersion": "", "iosVersion": "", "androidVersion": "", "data": 1}]}}