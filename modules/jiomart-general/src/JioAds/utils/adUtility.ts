import {getPrefString} from '../../../../jiomart-common/src/JMAsyncStorageHelper';
import {AsyncStorageKeys} from '../../../../jiomart-common/src/JMConstants';
import {getValidString} from '../../../../jiomart-common/src/utils/JMStringUtility';
import {JMDatabaseManager} from '../../../../jiomart-networkmanager/src/db/JMDatabaseManager';

export const generateDefaultAdMetaData = async () => {
  try {
    let [xlocation, userDetails]: [any, any] = await Promise.all([
      JMDatabaseManager.address.getDefaultAddress(),
      getPrefString(AsyncStorageKeys.PROFILE_DETAILS),
    ]);

    xlocation = xlocation ? JSON.parse(xlocation) : {};
    userDetails = userDetails ? JSON.parse(userDetails) : {};

    const email = getValidString(userDetails?.emails?.[0]?.email);

    return {
      languageOfArticle: 'english',
      email,
      pincode: xlocation?.pin,
      city: xlocation?.city,
      state: xlocation?.state,
    };
  } catch (error) {
    return {};
  }
};
