import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, JioText } from '@jio/rn_components';
import {
  ButtonState,
  JioTypography,
  type JioColor,
} from '@jio/rn_components/src/index.types';
import {useEffect, useRef, useState} from 'react';
import {Platform, StyleSheet, View} from 'react-native';
import {TextInput} from 'react-native-gesture-handler';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {rh, rw} from '../../../jiomart-common/src/JMResponsive';
import {capitalizeFirstLetter} from '../../../jiomart-common/src/utils/JMStringUtility';
import {useConfigFile} from '../../../jiomart-general/src/hooks/useJMConfig';
import type {BottomSheetChildren} from '../../../jiomart-general/src/ui/BottomSheet/types/BottomSheetType';
import JMBtmSheetHeader from '../../../jiomart-general/src/ui/JMBtmSheetHeader';
import {JMConfigFileName} from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import useAddressOperation from '../hooks/useAddressOperation';
import {addStringPref} from '../../../jiomart-common/src/JMAsyncStorageHelper';
import {AsyncStorageKeys} from '../../../jiomart-common/src/JMConstants';
import {JMSharedViewModel} from '../../../jiomart-common/src/JMSharedViewModel';
import {JMDatabaseManager} from '../../../jiomart-networkmanager/src/db/JMDatabaseManager';
import {PlatformType} from '../../../jiomart-common/src/JMObjectUtility';
import {logAnalyticsEvent} from '../../../jiomart-common/src/JMAnalyticsUtility';
import {
  AnalyticsParams,
  EventTriggerChannel,
} from '../../../jiomart-common/src/AnalyticsParams';
import { getVersionSpecificFilterListData } from '../../../jiomart-common/src/utils/JMCommonFunctions';

interface JMDeliverToPincodeBtmSheetProps extends BottomSheetChildren {
  showCloseButton: boolean;
  onClose?: () => void;
}

const JMDeliverToPincodeBtmSheet = (props: JMDeliverToPincodeBtmSheetProps) => {
  const { close, onClose } = props;
  const insets = useSafeAreaInsets();
  // const config = CONFIG?.bottomSheet?.deliverToBarPincode;
  const config: any = getVersionSpecificFilterListData(useConfigFile(
    JMConfigFileName.JMAddressConfigurationFileNAme,
  )?.bottomSheet?.deliverToBarPincode)?.[0];
  const { getPincodeStatus, checkAndSetPincode } = useAddressOperation();

  const [pincode, setPincode] = useState('');
  const [isValidPincodeMessage, setIsValidPincodeMessage] = useState('');
  const [isValidPincode, setIsValidPincode] = useState<boolean | null>(null);
  let isButtonClicked = false;

  const pincodeInputRef = useRef<TextInput>(null);

  const iconValidation = isValidPincode
    ? config?.successIcon
    : config?.errorIcon;

  const enterPincode = async (text: string) => {
    setPincode(text);
    if (text.length < 6) {
      setIsValidPincodeMessage('');
      setIsValidPincode(null);
    }
    if (text.length === 6) {
      getPincodeStatus.mutate(text, {
        onSuccess: response => {
          if (response?.success || response.status === 'success') {
            console.log('🚀 ~ return ~ response:', response?.result);
            const city = capitalizeFirstLetter(
              response?.data
                ? response?.data?.[0]?.parents
                  ?.find((it: any) => it?.sub_type === 'city')
                  ?.name?.replace(/_/g, ' ')
                : response?.result?.city?.replace(/_/g, ' '),
            );
            const state = capitalizeFirstLetter(
              response?.data
                ? response?.data?.[0]?.parents
                  ?.find((it: any) => it?.sub_type === 'state')
                  ?.name?.replace(/_/g, ' ')
                : response?.result?.state_name?.replace(/_/g, ' '),
            );

            setIsValidPincodeMessage(`${city}, ${state}`);
            setIsValidPincode(true);
          } else {
            setIsValidPincodeMessage(config?.message?.invalidPincode);
            setIsValidPincode(false);
          }
        },
        onError: error => {
          setIsValidPincodeMessage(
            error?.response?.reason?.reason_eng ??
            config?.message?.pincodeChangeFailed,
          );
          setIsValidPincode(false);
        },
      });
    }
  };

  const resetPincodeInputField = () => {
    close?.(onClose);
  };

  const applyPincode = async () => {
    try {
      isButtonClicked = true;
      const address = await JMDatabaseManager.address.getDefaultAddressModel();
      console.log(
        '🚀 ~ applyPincode ~ pincodeInputRef.current?.isFocused:',
        pincodeInputRef.current?.isFocused() +
          'address pincode ' +
          address?.pin +
          ' pincode ' +
          pincode,
      );
      await addStringPref(AsyncStorageKeys.PINCODE_PROVIDED, 'true');
      if (address?.pin !== pincode) {
        await logAnalyticsEvent({
          eventName: 'header',
          payload: {
            category: 'header',
            action: 'pincode applied',
            label: 'pincode applied',
            page_type: 'Home',
          },
          channel: `${EventTriggerChannel.FIREBASE}`,
        });

        await logAnalyticsEvent({
          eventName: 'Native_Deliver_To',
          payload: {
            option: 'Enter Pincode',
            [AnalyticsParams.PAGE_NAME]: 'Home',
          },
          channel: `${EventTriggerChannel.FIREBASE},${EventTriggerChannel.CLEVER_TAP}`,
        });
        await checkAndSetPincode({
          pincode,
          city: isValidPincodeMessage?.split(',')?.[0],
          state: isValidPincodeMessage?.split(',')?.[1],
        });
      }
      close?.(() => {
        onClose?.();
      }); // Close bottom sheet
    } catch (error) { }
  };

  useEffect(() => {
    if (props.showCloseButton) {
      const timer = setTimeout(() => {
        pincodeInputRef.current?.focus();
      }, 300);
      return () => clearTimeout(timer);
    }
  }, []);

  return (
    <View style={{ paddingBottom: insets.bottom }}>
      <JMBtmSheetHeader
        title={config?.title ?? ''}
        onPress={resetPincodeInputField}
        hideClose={!props.showCloseButton}
      />
      <JioText
        text={config?.description?.title ?? ''}
        style={styles.margin}
        color={config?.description?.color as JioColor}
        appearance={JioTypography.BODY_XS}
      />

      <View style={styles.container}>
        <View style={{ justifyContent: 'center' }}>
          <JioText
            {...config?.textInput?.label}
            appearance={JioTypography.BODY_XS}
          />
          <View style={styles.textInputContainer}>
            <JioIcon {...config?.textInput?.icon} />
            <TextInput
              ref={pincodeInputRef}
              textContentType="postalCode"
              placeholder={config?.textInput?.placeholder}
              style={styles.textInput}
              keyboardType={'number-pad'}
              keyboardAppearance={'default'}
              editable
              enablesReturnKeyAutomatically
              value={pincode}
              onChangeText={enterPincode}
              autoComplete={'postal-code'}
              inputMode={'numeric'}
              maxLength={6}
              contextMenuHidden={true}
              selectionColor="#000000A6"
              returnKeyType="done"
              onSubmitEditing={({ nativeEvent }) => {
                console.log('Return pressed:', nativeEvent.text);
                if (isValidPincode) {
                  applyPincode();
                }
              }}
            />
            <JioButton
              title={config?.button?.apply?.text ?? ''}
              state={isValidPincode ? ButtonState.NORMAL : ButtonState.DISABLED}
              onClick={applyPincode}
            />
          </View>
          <View
            style={[
              styles.border,
              isValidPincode !== null
                ? isValidPincode
                  ? styles.valid
                  : styles.invalid
                : null,
            ]}
          />

          {pincode && isValidPincode !== null ? (
            <View style={{ minHeight: rh(24), flexDirection: 'row' }}>
              <JioIcon
                {...iconValidation}
                style={[styles.marginTop, styles.marginRight]}
              />
              <JioText
                text={isValidPincodeMessage}
                maxLines={2}
                appearance={JioTypography.BODY_XXS}
                style={styles.marginTop}
                color={
                  isValidPincode !== null
                    ? isValidPincode
                      ? 'feedback_success_80'
                      : 'feedback_error_80'
                    : 'primary_inverse'
                }
              />
            </View>
          ) : null}
        </View>
      </View>
    </View>
  );
};

export default JMDeliverToPincodeBtmSheet;

const styles = StyleSheet.create({
  keyboardAvoidingContainer: {
    flexDirection: 'column',
    backgroundColor: 'white',
    justifyContent: 'space-between',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
  },
  flex: { flex: 1 },
  margin: { marginHorizontal: 24 },
  container: {
    flexDirection: 'row',
    margin: 24,
    marginBottom: Platform.OS === PlatformType.IOS ? 0 : 42,
  },
  textInputContainer: {
    flexDirection: 'row',
    columnGap: 8,
    marginBottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    // height: rh(40),
    fontSize: 16,
    // lineHeight: 24,
    letterSpacing: -0.05,
    fontFamily: 'JioType',
    fontStyle: 'normal',
    fontWeight: '500',
  },
  textInput: {
    width: rw(225),
    textDecorationColor: '#141414',
    color: 'black',
    fontSize: 16,
    lineHeight: 24,
    letterSpacing: -0.05,
    fontFamily: 'JioType',
    fontStyle: 'normal',
    fontWeight: '500',
  },
  border: { height: 2, backgroundColor: '#000000A6', width: rw(242) },
  valid: { backgroundColor: '#25AB21' },
  invalid: { backgroundColor: '#F50031' },
  marginTop: { marginTop: 4 },
  marginRight: { marginRight: 4 },
});
