package com.jiomart.module

import android.util.Log
import androidx.activity.result.ActivityResult
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.IntentSenderRequest
import androidx.activity.result.contract.ActivityResultContracts
import com.facebook.react.ReactActivity
import com.facebook.react.bridge.Promise
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod
import com.facebook.react.module.annotations.ReactModule
import com.google.android.gms.auth.api.identity.GetPhoneNumberHintIntentRequest
import com.google.android.gms.auth.api.identity.Identity

@ReactModule(name = "JMPhoneNumberHintModule")
class JMPhoneNumberHintModule(reactContext: ReactApplicationContext) :
    ReactContextBaseJavaModule(reactContext) {

    override fun canOverrideExistingModule(): Boolean {
        return true
    }

    override fun getName(): String = "JMPhoneNumberHintModule"

    companion object {
        private lateinit var phoneNumberLauncher: ActivityResultLauncher<IntentSenderRequest>
        private var phoneNumberPromise: Promise? = null
        fun registerPhoneNumberLauncher(activity: ReactActivity) {
            try{

                phoneNumberLauncher = activity.activityResultRegistry.register(
                    "phone_number_hint",
                    ActivityResultContracts.StartIntentSenderForResult()
                ) { result ->
                    handlePhoneHintResult(result, activity)
                }
            } catch (e:Exception){
                Log.d("registerPhoneNumberLauncher ", "registerPhoneNumberLauncher error ")
            }
        }
        fun handlePhoneHintResult(result: ActivityResult, activity: ReactActivity) {
            try {
                Log.d("registerPhoneNumberLauncher ", "registerPhoneNumberLauncher result "+result.data)
                val phoneNumber = Identity.getSignInClient(activity)
                    .getPhoneNumberFromIntent(result.data)
                    .replaceFirst("+", "")

                if (phoneNumber.length >= 10) {
                    phoneNumberPromise?.resolve(phoneNumber.takeLast(10))
                } else {
                    phoneNumberPromise?.reject("INVALID_PHONE", "Invalid phone number")
                }
            } catch (e: Exception) {
                phoneNumberPromise?.reject("ERROR", "Phone Number Hint failed")
            }
        }
    }

    @ReactMethod
    fun showPhoneNumberHint(p: Promise) {
        val activity = currentActivity ?: run {
            p.reject("NO_ACTIVITY", "Activity doesn't exist")
            return
        }

        phoneNumberPromise = p

        val request = GetPhoneNumberHintIntentRequest.builder().build()
        Identity.getSignInClient(activity)
            .getPhoneNumberHintIntent(request)
            .addOnSuccessListener { result ->
                val senderRequest = IntentSenderRequest.Builder(result).build()
                phoneNumberLauncher.launch(senderRequest)
            }
            .addOnFailureListener {
                p.reject("PHONE_HINT_FAILED", "Failed to get phone number hint: ${it.message}")
            }
    }
}
