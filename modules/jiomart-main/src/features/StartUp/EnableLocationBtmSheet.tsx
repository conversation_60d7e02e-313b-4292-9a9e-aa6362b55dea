import React, {useCallback} from 'react';
import {EnableLocationBottomSheetProps} from './types/EnableLocationBottomSheetType';
import {PermissionsAndroid, Platform, Pressable, SafeAreaView, View} from 'react-native';
import {JioIcon, JioText} from '@jio/rn_components';
import {useNavigation} from '@react-navigation/native';
import {AsyncStorageKeys} from '../../../../jiomart-common/src/JMConstants';
import {addStringPref} from '../../../../jiomart-common/src/JMAsyncStorageHelper';
import {styles} from './styles/EnableLocationBottomSheetStyle';
import {
  IconColor,
  IconSize,
  JioTypography,
} from '@jio/rn_components/src/index.types';
import {IconKey} from '@jio/rn_components/src/utils/IconUtility';
import {rh} from '../../../../jiomart-common/src/JMResponsive';
import {checkAndRequestSinglePermission} from '../../../../jiomart-common/src/JMPermission';
import {PERMISSIONS, RESULTS} from 'react-native-permissions';
import {navigateTo} from '../../../../jiomart-general/src/navigation/JMNavGraph';
import {
  NavigationType,
  navBeanObj,
} from '../../../../jiomart-common/src/JMNavGraphUtil';
import {checkIsLocationEnabled} from '../../../../jiomart-common/src/utils/JMLocationUtility';
import useCurrentLocation from '../../../../jiomart-address/src/hooks/useCurrentLocation';
import useAddressOperation from '../../../../jiomart-address/src/hooks/useAddressOperation';
import { JMSharedViewModel } from '../../../../jiomart-common/src/JMSharedViewModel';
import { PlatformType } from '../../../../jiomart-common/src/JMObjectUtility';
import { checkPermissionForLocation } from '../../../../jiomart-general/src/bridge/JMRNBridge';

const EnableLocationBtmSheet = (props: EnableLocationBottomSheetProps) => {
  const {onClose, configData, close, onNavigate} = props;

  const navigation = useNavigation();

  const {fetchLocationFromReverseGeoCodeFromLatLong} = useCurrentLocation({
    alertBlocked: {title: ''},
  });
  const {checkAndSetPincode} = useAddressOperation();

  const handleOnNavigate = useCallback(async () => {
    await addStringPref(AsyncStorageKeys.ENABLE_LOCATION, 'true');
    close?.(onNavigate);
  }, [close, onNavigate]);

  const handleClose = useCallback(async () => {
    await addStringPref(AsyncStorageKeys.ENABLE_LOCATION, 'true');
    close?.(onClose);
  }, [close, onClose]);

  const handleEnableLocation = async () => {
    const locationPermission =
      Platform.OS === 'android'
        ? PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION
        : PERMISSIONS.IOS.LOCATION_WHEN_IN_USE;

    try {
      const locationPermissionPrevStatus = Platform.OS === PlatformType.ANDROID ? await checkPermissionForLocation() : null;

      const result = await checkAndRequestSinglePermission(locationPermission);
      const isLocationEnabled = await checkIsLocationEnabled();
      Platform.OS === 'android' ? handleClose() : handleOnNavigate();
      if (
        ((result === RESULTS.LIMITED || result === RESULTS.GRANTED) && isLocationEnabled) ||
          Platform.OS === 'ios' || locationPermissionPrevStatus === RESULTS.BLOCKED
      ) {
        const stats = await fetchLocationFromReverseGeoCodeFromLatLong(true, true);
        if (stats?.address?.pin) {
          addStringPref(AsyncStorageKeys.LOCATION_PERMISSION_GRANTED, 'true');
          await checkAndSetPincode({
            pincode: stats?.address?.pin,
            state: stats?.address?.state,
            city: stats?.address?.city,
          });
        }
      }
    } catch (error) {}
  };

  const handleSelectLocation = async () => {
    await handleOnNavigate();
    navigateTo(
      navBeanObj({
        navTitle: 'Set Delivery Location',
        headerType: 1,
        source: '',
        destination: 'JMAddressSearchScreen',
        actionType: 'T001',
        actionUrl: '',
        bundle: '',
        navigationType: NavigationType.PUSH,
      }),
      navigation,
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <View
        style={styles.titleContainer}>
        <JioText
          text={configData?.title}
          appearance={JioTypography.HEADING_XXS}
          color="black"
        />
        <Pressable onPress={handleClose} style={styles.closeBtn}>
          <JioIcon
            ic={(configData?.closeIcon?.iconAsset as IconKey) ?? 'IcClose'}
            color={IconColor.PRIMARY60}
            size={IconSize.MEDIUM}
          />
        </Pressable>
      </View>
      <JioText
        text={configData?.subTitle}
        appearance={JioTypography.BODY_XS}
        color="primary_grey_80"
        style={styles.subTitleText}
      />
      {configData?.enableLocationBtn?.isButtonVisible && (
        <Pressable
          style={styles.enableLocationBtn}
          onPress={handleEnableLocation}>
          <JioText
            text={configData?.enableLocationBtn?.buttonText}
            appearance={JioTypography.BODY_S_BOLD}
            color="primary_inverse"
          />
        </Pressable>
      )}
      {configData?.selectLocationBtn?.isButtonVisible && (
        <Pressable
          style={styles.selectLocationBtn}
          onPress={handleSelectLocation}>
          <JioText
            text={configData?.selectLocationBtn?.buttonText}
            appearance={JioTypography.BODY_S_BOLD}
            color="primary_60"
          />
        </Pressable>
      )}
    </SafeAreaView>
  );
};

export default EnableLocationBtmSheet;
