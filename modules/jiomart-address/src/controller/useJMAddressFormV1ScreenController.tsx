import {QueryClient} from '@tanstack/react-query';
import {useEffect, useState} from 'react';
import type {GeoCoordinates} from 'react-native-geolocation-service';
import {RQKey} from '../../../jiomart-common/src/JMConstants';
import {navBeanObj} from '../../../jiomart-common/src/JMNavGraphUtil';
import {
  GenericToast,
  mergeGenericToastTypeData,
} from '../../../jiomart-common/src/JMScreenSlot.types';
import {JMSharedViewModel} from '../../../jiomart-common/src/JMSharedViewModel';
import type {JMAddressModel} from '../../../jiomart-common/src/uiModals/JMAddressModel';
import {JMLogger} from '../../../jiomart-common/src/utils/JMLogger';
import {
  capitalizeFirstLetter,
  getValidString,
} from '../../../jiomart-common/src/utils/JMStringUtility';
import {useGlobalState} from '../../../jiomart-general/src/context/JMGlobalStateProvider';
import { getValidName, getVersionSpecificFilterListData, hideKeyboard } from '../../../jiomart-common/src/utils/JMCommonFunctions';
import useFormHandler from '../../../jiomart-general/src/hooks/useFormHandler';
import {useConfigFile} from '../../../jiomart-general/src/hooks/useJMConfig';
import useUserProfile from '../../../jiomart-general/src/hooks/useUserProfile';
import {navigateTo} from '../../../jiomart-general/src/navigation/JMNavGraph';
import {JMConfigFileName} from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import useAddressOperation from '../hooks/useAddressOperation';
import useCurrentLocation from '../hooks/useCurrentLocation';
import type {UseJMAddressFormV1ScreenProps} from '../types/JMAddressFormV1ScreenType';
import {logAnalyticsEvent} from '../../../jiomart-common/src/JMAnalyticsUtility';
import {
  AnalyticsParams,
  EventTriggerChannel,
} from '../../../jiomart-common/src/AnalyticsParams';

export const enum AddressFormField {
  ID = 'id',
  HOUSE_NO = 'flat_or_house_no',
  FLOOR_NO = 'floor_no',
  TOWER_NO = 'tower_no',
  BUILDING_APARTMENT_NAME = 'area',
  ADDRESS = 'address',
  LANDMARK_AREA = 'landmark',
  PINCODE = 'pincode',
  CITY = 'city',
  STATE = 'state',
  RECEIVER_NAME = 'name',
  RECEIVER_NUMBER = 'phone',
  ADDRESS_TYPE = 'address_type',
  ADDRESS_TYPE_OTHER = 'address_type_other',
}

export const enum AddressType {
  HOME = 'Home',
  WORK = 'Work',
  OTHER = 'Other',
}

const queryClient = new QueryClient();

const useJMAddressFormV1ScreenController = (
  props: UseJMAddressFormV1ScreenProps,
) => {
  const { route, navigation } = props;
  const config: any = getVersionSpecificFilterListData(useConfigFile(
    JMConfigFileName.JMAddressConfigurationFileNAme
  )?.screen?.addressFormV1)?.[0];
  const [address, setAddress] = useState<any>(route.params?.params?.address)

  useEffect(() => {
    setAddress(route.params?.params?.address);
  }, [route.params?.params?.address]);

  // console.log('🚀 ~ address:', address);

  let geoCordinate: GeoCoordinates = {
    latitude: address?.lat,
    longitude: address?.lon,
    accuracy: 0,
    altitude: null,
    heading: null,
    speed: null
  };

  let regionCamera = {
    ...config?.map?.initialCamera,
    center: geoCordinate,
  };

  const {setToastTypeData} = useGlobalState();

  const {userData} = useUserProfile();
  const {
    saveAddress,
    updateAddress,
    getPincodeStatus,
    setDefaultAddressApi,
    generateDefaultAddressRequestBody,
    generateSaveAddressRequestBody,
    generateUpdateAddressRequestBody,
  } = useAddressOperation();

  const [isFormValid, setFormValid] = useState(false);
  const [isApiLoading, setApiLoading] = useState(false);

  const receiverName = address?.id
    ? getValidName(address?.name)
    : getValidString(userData?.first_name) +
      ' ' +
      getValidString(userData?.last_name);
  const receiverNumber = address?.id
    ? address?.phone
    : userData?.phone_numbers?.length > 0
    ? userData.phone_numbers[0].phone
    : '';

  const {fetchLocationFromReverseGeoCodeFromLatLong} = useCurrentLocation({
    alertBlocked: config?.alert?.blockedLocation,
  });

  const {
    formData,
    formError,
    handleInputRef,
    handleFormError,
    handleFormData,
    onChangeText,
    validateValue,
    validateForm,
  } = useFormHandler(
    {
      [AddressFormField.ID]: address?.id,
      [AddressFormField.RECEIVER_NAME]: receiverName,
      [AddressFormField.RECEIVER_NUMBER]: receiverNumber,
      [AddressFormField.ADDRESS_TYPE]: address?.address_type,
      [AddressFormField.ADDRESS_TYPE_OTHER]: address?.address_type_other,
      [AddressFormField.ADDRESS]: address?.address,
      [AddressFormField.HOUSE_NO]: address?.flat_or_house_no,
      [AddressFormField.FLOOR_NO]: address?.floor_no,
      [AddressFormField.TOWER_NO]: address?.tower_no,
      [AddressFormField.BUILDING_APARTMENT_NAME]: address?.area,
      [AddressFormField.LANDMARK_AREA]: address?.landmark,
      [AddressFormField.CITY]: address?.city,
      [AddressFormField.STATE]: address?.state,
      [AddressFormField.PINCODE]: address?.pin,
    },
    config?.form,
  );

  const resetCoords = () => {
    const updateAddress = {...address};
    delete updateAddress?.lat;
    delete updateAddress?.lon;
    navigation.setParams({params: {address: updateAddress}});
  };

  const handlePincodeChange = (field: any) => {
    isReqAddressFieldsValid();
    return (value: string) => {
      resetCoords();
      let text = value.trim().replace(/[.,-]/g, '');
      const fieldConfig = config?.[field];
      if (fieldConfig?.isRegexClean && fieldConfig?.regex) {
        const regex = new RegExp(fieldConfig?.regex, 'g');
        text = text?.replace?.(regex, '') ?? '';
      }
      handleFormData(field, fieldConfig)(text);
      if (text?.length === 6) {
        getPincodeStatus.mutate(text, {
          onSuccess: response => {
            if (response?.success || response?.status === 'success') {
              console.log('🚀 ~ return ~ response:', response?.result);
              const city = capitalizeFirstLetter(
                response?.data
                  ? response?.data?.[0]?.parents
                      ?.find((it: any) => it?.sub_type === 'city')
                      ?.name?.replace(/_/g, ' ')
                  : response?.result?.city?.replace(/_/g, ' '),
              );
              const state = capitalizeFirstLetter(
                response?.data
                  ? response?.data?.[0]?.parents
                      ?.find((it: any) => it?.sub_type === 'state')
                      ?.name?.replace(/_/g, ' ')
                  : response?.result?.state_name?.replace(/_/g, ' '),
              );
              console.log('🚀 ~ return ~ state:', state);
              handleFormData(AddressFormField.CITY, fieldConfig)(city);
              handleFormData(AddressFormField.STATE, fieldConfig)(state);
              hideKeyboard();
            } else {
              handleFormError(
                AddressFormField.PINCODE,
                config?.message?.invalidPincode,
              )();
            }
          },
          onError(error, variables, context) {
            handleFormError(
              AddressFormField.PINCODE,
              error?.response?.reason?.reason_eng ??
                config?.message?.pincodeChangeFailed,
            )();
          },
        });
      } else {
        handleFormData(AddressFormField.CITY, fieldConfig)('');
        handleFormData(AddressFormField.STATE, fieldConfig)('');
      }
    };
  };

  const onTextChange = (field: AddressFormField) => (text: string) => {
    onChangeText(field)(text);
    isReqAddressFieldsValid();
  };

  const shouldShowLocationBlock = (val: string[]) => {
    let isValid = true;
    for (let i = 0; i < val?.length; i++) {
      if (!formData[val[i]]) {
        isValid = false;
        break;
      }
    }
    return isValid;
  };

  const shouldShowMapBlock = (val: string[]) => {
    let isValid = true;
    for (let i = 0; i < val?.length; i++) {
      if (!formData[val[i]]) {
        isValid = false;
        break;
      }
    }
    return isValid;
  };

  const handleCurrentLocation = async () => {
    const {address, coords} =
      await fetchLocationFromReverseGeoCodeFromLatLong();
    handleMapRedirection(coords, address);
  };
  const handleMapRedirection = (
    geoCoords: GeoCoordinates,
    address?: JMAddressModel,
  ) => {
    navigateTo(
      navBeanObj({
        ...config?.locationBlock?.currentLocation?.cta,
        params: {coords: geoCoords, address},
      }),
      navigation,
    );
  };
  const handleEditDeliveryLocation = () => {
    handleMapRedirection(geoCordinate, address);
  };
  const handleSearchRedirection = () => {
    navigateTo(
      navBeanObj({
        ...config?.locationBlock?.search?.cta,
      }),
      navigation,
    );
  };
  const handleSaveAs = (val: string) => {
    handleFormData(AddressFormField.ADDRESS_TYPE)(val);
  };
  const handleRedirection = () => {
    try {
      queryClient.invalidateQueries({
        queryKey: [RQKey.GET_ADDRESS],
      });
    } catch (error) {
      JMLogger.log('handleRedirection ' + error);
    }
    JMSharedViewModel.Instance.setRefreshAddressList(true);
    setApiLoading(false);
    if (navigation?.canGoBack()) {
      navigation?.goBack();
    }
  };

  const isReqAddressFieldsValid = () => {
    const getLimit = (field: string, fallback: number) =>
      parseInt(
        config?.form?.[field]?.validation?.minLength ||
          config?.form?.[field]?.validation?.exactLength ||
          '',
      ) || fallback;

    const validations = [
      formData[AddressFormField.PINCODE]?.trim().length ===
        getLimit(AddressFormField.PINCODE, 6),

      formData[AddressFormField.CITY]?.trim() !== '-' &&
        formData[AddressFormField.CITY]?.trim().length > 0,

      formData[AddressFormField.STATE]?.trim() !== '-' &&
        formData[AddressFormField.STATE]?.trim().length > 0,

      formData[AddressFormField.ADDRESS]?.trim().length >=
        getLimit(AddressFormField.ADDRESS, 3),

      formData[AddressFormField.LANDMARK_AREA]?.trim().length >=
        getLimit(AddressFormField.LANDMARK_AREA, 3),

      formData[AddressFormField.RECEIVER_NAME]?.trim().length >=
        getLimit(AddressFormField.RECEIVER_NAME, 3),

      formData[AddressFormField.RECEIVER_NUMBER]?.trim().length ===
        getLimit(AddressFormField.RECEIVER_NUMBER, 10),

      !!formData[AddressFormField.ADDRESS_TYPE]?.trim(),
    ];

    if (
      formData[AddressFormField.ADDRESS_TYPE] === 'Other' &&
      getValidString(formData[AddressFormField.ADDRESS_TYPE_OTHER]?.trim())
        .length < getLimit(AddressFormField.ADDRESS_TYPE_OTHER, 3)
    ) {
      validations.push(false);
    }

    setFormValid(validations.every(Boolean));
  };

  const handleAddressSubmit = () => {
    try {
      if (!validateForm()) {
        return;
      }

      console.log('🚀 ~ handleAddressSubmit ~ formData:', formData);
      setApiLoading(true);
      const payload: JMAddressModel = {
        id: formData[AddressFormField.ID],
        city: formData[AddressFormField.CITY],
        state: formData[AddressFormField.STATE],
        pin: formData[AddressFormField.PINCODE],
        name: formData[AddressFormField.RECEIVER_NAME],
        phone: formData[AddressFormField.RECEIVER_NUMBER],
        address: formData[AddressFormField.ADDRESS],
        flat_or_house_no: formData[AddressFormField.HOUSE_NO],
        floor_no: formData[AddressFormField.FLOOR_NO],
        tower_no: formData[AddressFormField.TOWER_NO],
        area: formData[AddressFormField.BUILDING_APARTMENT_NAME],
        landmark: formData[AddressFormField.LANDMARK_AREA],
        lat: regionCamera?.center?.latitude ?? 0.0,
        lon: regionCamera?.center?.longitude ?? 0.0,
        input_mode: regionCamera?.center?.latitude ? 'MAP_POLY' : 'manual',
        is_default_address: true,
        ...(formData[AddressFormField.ADDRESS_TYPE] != 'Home' &&
        formData[AddressFormField.ADDRESS_TYPE] != 'Work'
          ? {
              address_type: formData[AddressFormField.ADDRESS_TYPE],
              address_type_other: formData[AddressFormField.ADDRESS_TYPE_OTHER],
            }
          : {
              address_type: formData[AddressFormField.ADDRESS_TYPE],
            }),
      };

      console.log('🚀 ~ handleAddressSubmit ~ payload:', payload);
      if (formData[AddressFormField.ID]) {
        const request = generateUpdateAddressRequestBody(payload);
        updateAddress.mutate(request, {
          onSuccess: res => {
            if (res?.success || res?.status === 'success') {
              logAnalyticsEvent({
                eventName: 'Address',
                payload: {
                  option: 'Save As',
                  [AnalyticsParams.PAGE_NAME]: 'Address Page',
                },
                channel: `${EventTriggerChannel.FIREBASE},${EventTriggerChannel.CLEVER_TAP}`,
              });
              const request = generateDefaultAddressRequestBody(payload);
              setDefaultAddressApi.mutate(request, {
                onSuccess: res => {
                  let modifiedToastData = {
                    isVisible: true,
                    message: 'Address updated',
                  };

                  setToastTypeData(
                    mergeGenericToastTypeData(
                      GenericToast.SUCCESS,
                      modifiedToastData,
                    ),
                  );
                  handleRedirection();
                },
                onError(error, variables, context) {
                  setApiLoading(false);
                  let modifiedToastData = {
                    isVisible: true,
                    message: 'Something went wrong. Please try again',
                  };

                  setToastTypeData(
                    mergeGenericToastTypeData(
                      GenericToast.ERROR,
                      modifiedToastData,
                    ),
                  );
                },
              });
            }
          },
        });
      } else {
        const request = generateSaveAddressRequestBody(payload);
        saveAddress.mutate(request, {
          onSuccess: res => {
            if (res?.success === true || res?.status === 'success') {
              payload.id = res.result.address_id;
              logAnalyticsEvent({
                eventName: 'Address',
                payload: {
                  option: 'Save & Proceed',
                  [AnalyticsParams.PAGE_NAME]: 'Address Page',
                },
                channel: `${EventTriggerChannel.FIREBASE},${EventTriggerChannel.CLEVER_TAP}`,
              });

              const request = generateDefaultAddressRequestBody(payload);
              setDefaultAddressApi.mutate(request, {
                onSuccess: res => {
                  let modifiedToastData = {
                    isVisible: true,
                    message: 'Address saved',
                  };

                  setToastTypeData(
                    mergeGenericToastTypeData(
                      GenericToast.SUCCESS,
                      modifiedToastData,
                    ),
                  );
                  handleRedirection();
                },
              });
            } else {
              setApiLoading(false);
              let modifiedToastData = {
                isVisible: true,
                message: 'Something went wrong. Please try again',
              };

              setToastTypeData(
                mergeGenericToastTypeData(
                  GenericToast.ERROR,
                  modifiedToastData,
                ),
              );
            }
          },
          onError(error, variables, context) {
            setApiLoading(false);
            let modifiedToastData = {
              isVisible: true,
              message: 'Something went wrong. Please try again',
            };

            setToastTypeData(
              mergeGenericToastTypeData(GenericToast.ERROR, modifiedToastData),
            );
          },
        });
      }
    } catch (error) {}
  };

  useEffect(() => {
    navigation.setParams({
      ...route.params,
      navTitle: formData[AddressFormField.ID]
        ? config?.header?.editNavTitle
        : route.params.navTitle,
    });
  }, []);

  return {
    ...props,
    address,
    regionCamera,
    config,
    formData,
    formError,
    navigationBean: route.params,
    isFormValid,
    isApiLoading,
    handleInputRef,
    handleFormError,
    onTextChange,
    validateValue,
    handleAddressSubmit,
    handleSaveAs,
    shouldShowLocationBlock,
    shouldShowMapBlock,
    handleCurrentLocation,
    handleSearchRedirection,
    handlePincodeChange,
    handleEditDeliveryLocation,
  };
};

export default useJMAddressFormV1ScreenController;
