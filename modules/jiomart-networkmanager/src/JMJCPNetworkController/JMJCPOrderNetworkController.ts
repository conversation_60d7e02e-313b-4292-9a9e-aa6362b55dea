import {JMJCPOrderEndpointKeys} from '../api/endpoints/JMApiEndpoints';
import {
  JMRequestConfigObject,
  JMHttpMethods,
} from '../api/helpers/JMRequestConfig';
import JMRequestHelper from '../api/helpers/JMRequestHelper';
import J<PERSON><PERSON>Client from '../api/service/JMApiClient';
import JMBaseOrderNetworkController from '../base/JMBaseOrderNetworkController';
import {JMBaseUrlKeys} from '../JMEnvironmentConfig';

class JMJCPOrderNetworkController extends JMBaseOrderNetworkController {
  private apiClient: JMApiClient = JMApiClient.getInstance;
  private baseUrl: JMBaseUrlKeys = JMBaseUrlKeys.JCP;
  constructor() {
    super();
  }

  protected fetchOrderList = async (): Promise<any> => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMJCPOrderEndpointKeys.ORDER_LIST,
          JMHttpMethods.POST,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };

  protected fetchServiceRequestList = async (): Promise<any> => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMJCPOrderEndpointKeys.SERVICE_REQUEST_LIST,
          JMHttpMethods.POST,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };

  protected fetchRefundList = async (): Promise<any> => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMJCPOrderEndpointKeys.ORDER_LIST,
          JMHttpMethods.POST,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };

  protected fetchRefundDetails = async (request: any): Promise<any> => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMJCPOrderEndpointKeys.ORDER_LIST,
          JMHttpMethods.POST,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };
}

export default JMJCPOrderNetworkController;
