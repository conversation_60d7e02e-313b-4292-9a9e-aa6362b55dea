// swift-interface-format-version: 1.0
// swift-compiler-version: Apple Swift version 6.0.3 effective-5.10 (swiftlang-6.0.3.1.10 clang-1600.0.30.1)
// swift-module-flags: -target arm64-apple-ios12.0-simulator -enable-objc-interop -enable-library-evolution -swift-version 5 -enforce-exclusivity=checked -O -enable-bare-slash-regex -module-name JioAdsFramework
// swift-module-flags-ignorable: -no-verify-emitted-module-interface
import AVFoundation
import AVKit
import AdSupport
import AppTrackingTransparency
import SystemConfiguration.CaptiveNetwork
import Combine
import CommonCrypto
import CoreLocation
import CoreMedia
import CoreTelephony
import CoreVideo
import DeveloperToolsSupport
import Foundation
import Foundation/*.Data*/
import ImageIO
@_exported import JioAdsFramework
import Network
import SafariServices
import Swift
import SwiftUI
import SystemConfiguration
import UIKit
import WebKit
import _Concurrency
import _StringProcessing
import _SwiftConcurrencyShims
import zlib
public enum JioVastModelFetcherError : Swift.Int, Swift.Error {
  case invalidInput
  case vastTooManyRedirects
  case vastError
  case vastRenditionError
  case emptyVastTags
  case noVastTagUris
  case unknownAdResponse
  case unknownError
  case vastAssetNotFound
  case vastEmptyResponse
  case vastLoadTimeout
  case vastMalformedResponse
  case vastMediaLoadTimeout
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
public struct RILProductListViewEvent : JioAdsFramework.RILEventProtocol, Swift.Codable {
  public var eventType: Swift.String?
  public var eventID: Swift.Int
  public var time: Swift.String?
  public var parameters: [Swift.String : Any]?
  public var startTime: Swift.String?, endTime: Swift.String?
  public var productDetails: JioAdsFramework.RILProductDetails?
  public var filters: [JioAdsFramework.RILFilter]?
  public init(startTime: Swift.String?, endTime: Swift.String?, productDetails: JioAdsFramework.RILProductDetails, parameters: [Swift.String : Any]? = nil, filters: [JioAdsFramework.RILFilter]? = nil)
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
public var checkVisibilityQueue: Dispatch.DispatchQueue?
public struct JioSDKNotificationName {
  public static let openingLandingPage: Swift.String
  public static let closingLandingPage: Swift.String
}
public func isDeviceTypeiPAD() -> Swift.Bool
@objc @_inheritsConvenienceInitializers @_hasMissingDesignatedInitializers @_Concurrency.MainActor @preconcurrency public class JioAdView : UIKit.UIView {
  @_Concurrency.MainActor @preconcurrency public var isShowAdAPICalled: Swift.Bool
  @_Concurrency.MainActor @preconcurrency public var multiAd: Swift.Bool {
    get
  }
  @_Concurrency.MainActor @preconcurrency public var customDataSHA1: [Swift.String : Any]?
  @_Concurrency.MainActor @preconcurrency weak public var delegate: (any JioAdsFramework.JIOAdViewProtocol)?
  @_Concurrency.MainActor @preconcurrency public var isVideoAdExpnaded: Swift.Bool
  @_Concurrency.MainActor @preconcurrency public var isPlayPause: Swift.Bool
  @_Concurrency.MainActor @preconcurrency public var mediaMuteState: Swift.Bool
  @_Concurrency.MainActor @preconcurrency public var isHybrid: Swift.Bool
  @_Concurrency.MainActor @preconcurrency public var adState: JioAdsFramework.JioAdState
  @_Concurrency.MainActor @preconcurrency weak public var customNativeAdContaier: UIKit.UIView? {
    get
  }
  @_Concurrency.MainActor @preconcurrency public var isAutoRefreshEnabled: Swift.Bool
  @_Concurrency.MainActor @preconcurrency public var adType: JioAdsFramework.AdType
  @_Concurrency.MainActor @preconcurrency public var autoPlayMediaEnalble: Swift.Bool
  @_Concurrency.MainActor @preconcurrency public var isRewardedAd: Swift.Bool {
    get
  }
  @_Concurrency.MainActor @preconcurrency public var isOnPreparedCalled: Swift.Bool
  @_Concurrency.MainActor @preconcurrency public var isMediationPrepared: Swift.Bool
  @_Concurrency.MainActor @preconcurrency public var imaAdFailedToLoad: Swift.Bool
  @_Concurrency.MainActor @preconcurrency public var mediationModel: [JioAdsFramework.MediationModelElement]
  @_Concurrency.MainActor @preconcurrency public var isMediationTypeSelection: Swift.Bool
  @_Concurrency.MainActor @preconcurrency public var isIMAFirstIndex: Swift.Bool {
    get
  }
  @_Concurrency.MainActor @preconcurrency public var isIMAFirstIndexStartPlaying: Swift.Bool
  @_Concurrency.MainActor @preconcurrency public var isIMAPlayed: Swift.Bool
  @_Concurrency.MainActor @preconcurrency public var isImaNeedToClose: Swift.Bool
  @_Concurrency.MainActor @preconcurrency public var hybridAdview: JioAdsFramework.JioAdView?
  @_Concurrency.MainActor @preconcurrency public var autoRotateEnabled: Swift.Bool
  @_Concurrency.MainActor @preconcurrency public var isAdpodCountShown: Swift.Bool
  @_Concurrency.MainActor @preconcurrency public var dynamicDisplaySize: JioAdsFramework.DynamicDisplaySize
  @_Concurrency.MainActor @preconcurrency public var isNativeVideoAd: Swift.Bool
  @_Concurrency.MainActor @preconcurrency public var refreshRate: Swift.Int {
    get
  }
  @_Concurrency.MainActor @preconcurrency public var testFileNames: [Swift.String]?
  @_Concurrency.MainActor @preconcurrency convenience public init(adSpotId: Swift.String, adType: JioAdsFramework.AdType, delegate: any JioAdsFramework.JIOAdViewProtocol, forPresentionClass: UIKit.UIViewController? = nil, publisherContainer: UIKit.UIView)
  @_Concurrency.MainActor @preconcurrency public func cacheAd()
  @_Concurrency.MainActor @preconcurrency public func getVMAPURL(vmapID: Swift.String, contentVideoDuration: Swift.String, cue: Swift.String) -> Swift.String
  @_Concurrency.MainActor @preconcurrency public func loadCustomAd()
  @_Concurrency.MainActor @preconcurrency public func loadAd()
  @_Concurrency.MainActor @preconcurrency public func closeAd()
  @_Concurrency.MainActor @preconcurrency public func invalidateAd()
  @objc deinit
  @_Concurrency.MainActor @preconcurrency public func setRefreshRate(refreshRate: Swift.Int)
  @_Concurrency.MainActor @preconcurrency public func getRefreshRate() -> Swift.Int?
  @_Concurrency.MainActor @preconcurrency public func setCacheAdTimer(time: Foundation.TimeInterval)
  @_Concurrency.MainActor @preconcurrency public func invalidateCacheAdTimer()
  @_Concurrency.MainActor @preconcurrency public func setCompanions(companions: [JioAdsFramework.JioAdCompanion], delegate: any JioAdsFramework.JioCompanionAdviewProtocol)
  @_Concurrency.MainActor @preconcurrency public func setCarouselItemLayout(container: UIKit.UIView?)
  @_Concurrency.MainActor @preconcurrency public func setInstreamCarouselCompanionLayout(container: UIKit.UIView?, delegate: (any JioAdsFramework.JioCarousalCompanionProtocol)?)
  @_Concurrency.MainActor @preconcurrency public func setCustomView(container: UIKit.UIView?)
  @_Concurrency.MainActor @preconcurrency public func setCustomDisplayAdContainer(nativeContainer: UIKit.UIView?, videoContainer: UIKit.UIView?)
  @_Concurrency.MainActor @preconcurrency public func setAdparamView(container: UIKit.UIView?)
  @_Concurrency.MainActor @preconcurrency public func hideSkip()
  @_Concurrency.MainActor @preconcurrency public func pauseAd()
  @_Concurrency.MainActor @preconcurrency public func resumeAd()
  @_Concurrency.MainActor @preconcurrency public func isActivityIndicatorEnabled(enabled: Swift.Bool)
}
public enum JioHttpMethod : Swift.String {
  case get
  case post
  public init?(rawValue: Swift.String)
  public typealias RawValue = Swift.String
  public var rawValue: Swift.String {
    get
  }
}
public struct JioHttpClientModel {
  public init(endpoint: Swift.String, method: JioAdsFramework.JioHttpMethod, headers: [Swift.String : Swift.String]? = nil, timeout: Swift.Int = 20)
  public init(endpoint: Swift.String, method: JioAdsFramework.JioHttpMethod, body: Foundation.Data, headers: [Swift.String : Swift.String]? = nil, timeout: Swift.Int = 20)
}
public func SDKLog(_ logMessage: Any, functionName: Swift.String = #function, isTimeProfile: Swift.Bool = false)
public func SDKNSLog(_ logMessage: Any)
@objc public class JioHttpClient : ObjectiveC.NSObject, JioAdsFramework.JioLogPreText {
  public var preTextTAG: Swift.String
  public init(adSpotId: Swift.String? = nil)
  @objc deinit
  public func fetch(model: JioAdsFramework.JioHttpClientModel, completionHandler: @escaping (Swift.Result<Foundation.Data, JioAdsFramework.JioHttpClientError>) -> Swift.Void)
}
public struct RILAppUserDetailsEvent : JioAdsFramework.RILEventProtocol, Swift.Codable {
  public var parameters: [Swift.String : Any]?
  public var eventType: Swift.String?
  public var time: Swift.String?
  public var eventID: Swift.Int
  public var customerDetails: [JioAdsFramework.RILCustomerDetail]?
  public init(customerDetails: [JioAdsFramework.RILCustomerDetail])
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
public struct RILCustomerDetail : Swift.Codable {
  public var type: Swift.Int?
  public var idValue: Swift.String?
  public var hashMethod: Swift.String?
  public init(type: Swift.Int, idValue: Swift.String, hashMethod: Swift.String = "SHA1")
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
public struct JioAdMarkup {
  public let body: Foundation.Data
}
@objc @_inheritsConvenienceInitializers @_hasMissingDesignatedInitializers @_Concurrency.MainActor @preconcurrency final public class InterstitialNativeLayout : UIKit.UIView {
  @objc deinit
}
@objc @_inheritsConvenienceInitializers @_hasMissingDesignatedInitializers @_Concurrency.MainActor @preconcurrency final public class DefaultInstreamLayout : UIKit.UIView {
  @objc deinit
}
public enum SdkDeviceType : Swift.Int {
  case phone
  case tablet
  case appleTV
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
extension JioAdsFramework.SdkDeviceType : Swift.CaseIterable {
  public typealias AllCases = [JioAdsFramework.SdkDeviceType]
  nonisolated public static var allCases: [JioAdsFramework.SdkDeviceType] {
    get
  }
}
@_hasMissingDesignatedInitializers public class JioViewPortObserverManager {
  public func startObserver()
  public func getAllObserver() -> [any JioAdsFramework.JioViewPortObserverDelegate]
  public func add(delegate: any JioAdsFramework.JioViewPortObserverDelegate)
  @objc deinit
}
extension JioAdsFramework.JioViewPortObserverManager : JioAdsFramework.JioViewPortObserverDelegate {
  public func viewPortStatusUpdate(status: JioAdsFramework.ViewPortStatus?, percentage: CoreFoundation.CGFloat?, adSpotId: Swift.String? = nil)
}
public struct CTAUrls {
}
public struct RILRemoveFromWishListEvent : JioAdsFramework.RILEventProtocol, Swift.Codable {
  public var eventType: Swift.String?
  public var eventID: Swift.Int
  public var time: Swift.String?
  public var parameters: [Swift.String : Any]?
  public var productDetails: JioAdsFramework.RILProductDetails?
  public init(parameters: [Swift.String : Any]? = nil, productDetails: JioAdsFramework.RILProductDetails)
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
public protocol JioMediaRenderer : AnyObject {
  func mediaItemRenditionFailed(jioAdError: JioAdsFramework.JioAdError, duration: Swift.Int)
  func mediaRenditionComplete(duration: Swift.Int, count: Swift.Int)
  func remainingDuration() -> Swift.Int?
  func remainingAdCount() -> Swift.Int?
}
public struct AppLaunch : JioAdsFramework.RILEventProtocol, Swift.Codable {
  public var eventType: Swift.String?
  public var eventID: Swift.Int
  public var time: Swift.String?
  public var parameters: [Swift.String : Any]?
  public var firstLaunch: Swift.Int?
  public var appName: Swift.String?
  public var appSdkVer: Swift.String?
  public var language: Swift.String?
  public var country: Swift.String?
  public var customerDetails: [JioAdsFramework.RILCustomerDetail]?
  public init(parameters: [Swift.String : Any]? = nil, customerDetails: [JioAdsFramework.RILCustomerDetail]? = nil)
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
public protocol JIoOmSdkCoordinator {
  init()
  func downloadOmIdJsService()
  func omsdk(vendorKey: Swift.String?, javaScriptResource: Swift.String?, verificationParam: Swift.String?, videoContainer: UIKit.UIView?, player: AVFoundation.AVPlayer?, isAudioAd: Swift.Bool)
}
extension JioAdsFramework.JIoOmSdkCoordinator {
  public func downloadOmIdJsService()
  public func omsdk(vendorKey: Swift.String?, javaScriptResource: Swift.String?, verificationParam: Swift.String?, videoContainer: UIKit.UIView?, player: AVFoundation.AVPlayer?, isAudioAd: Swift.Bool)
}
public protocol JIOAdViewProtocol : AnyObject {
  func onAdReceived(adView: JioAdsFramework.JioAdView)
  func onAdAnalyticsFired(adView: JioAdsFramework.JioAdView, eventName: Swift.String, parameters: [Swift.String : Any])
  func onAdPrepared(adView: JioAdsFramework.JioAdView)
  func onAdRender(adView: JioAdsFramework.JioAdView)
  func onAdClicked(adView: JioAdsFramework.JioAdView)
  func onAdRefresh(adView: JioAdsFramework.JioAdView)
  func onAdFailedToLoad(adView: JioAdsFramework.JioAdView, error: JioAdsFramework.JioAdError)
  func onAdMediaEnd(adView: JioAdsFramework.JioAdView)
  func onAdClosed(adView: JioAdsFramework.JioAdView, isVideoCompleted: Swift.Bool, isEligibleForReward: Swift.Bool)
  func onAdMessage(adView: JioAdsFramework.JioAdView, message: Swift.String)
  func onAdLoaded(adView: JioAdsFramework.JioAdView)
  func onAdMediaStart(adView: JioAdsFramework.JioAdView)
  func onAdSkippable(adView: JioAdsFramework.JioAdView)
  func onAdMediaExpand(adView: JioAdsFramework.JioAdView)
  func onAdMediaCollapse(adView: JioAdsFramework.JioAdView)
  func onMediaPlaybackChange(adView: JioAdsFramework.JioAdView, mediaPlayBack: JioAdsFramework.MediaPlayBack)
  func onAdDataPrepared(jioAd: JioAdsFramework.JioAd?, isLastAd: Swift.Bool, jioAdView: JioAdsFramework.JioAdView?)
  func onAdChange(adView: JioAdsFramework.JioAdView, trackNo: Swift.Int)
  func onAdSkipButtonClick()
  func onAdMediaProgress(totalDuration: Swift.Float, progress: Swift.Float)
  func getTestResourceData(fileName: Swift.String) -> (data: Foundation.Data?, error: (any Swift.Error)?)
  func onAdQuartileEventFired(eventType: JioAdsFramework.EQuartileEventType)
  func onAdExpandCollapseContainer(adView: JioAdsFramework.JioAdView, expandCollapse: JioAdsFramework.ExpandCollapse)
}
extension JioAdsFramework.JIOAdViewProtocol {
  public func onAdAnalyticsFired(adView: JioAdsFramework.JioAdView, eventName: Swift.String, parameters: [Swift.String : Any])
  public func mediationRequesting()
  public func mediationLoadAd()
  public func onAdSkipButtonClick()
  public func mediationReleaseResource()
  public func onAdDataPrepared(jioAd: JioAdsFramework.JioAd?, isLastAd: Swift.Bool, jioAdView: JioAdsFramework.JioAdView?)
  public func onAdMessage(adView: JioAdsFramework.JioAdView, message: Swift.String)
  public func onAdLoaded(adView: JioAdsFramework.JioAdView)
  public func onAdMediaProgress(totalDuration: Swift.Float, progress: Swift.Float)
  public func getTestResourceData(fileName: Swift.String) -> (data: Foundation.Data?, error: (any Swift.Error)?)
  public func onAdQuartileEventFired(eventType: JioAdsFramework.EQuartileEventType)
  public func onAdExpandCollapseContainer(adView: JioAdsFramework.JioAdView, expandCollapse: JioAdsFramework.ExpandCollapse)
}
public class JioVmapAdsLoader {
  public init(delegate: any JioAdsFramework.JioVmapAdsLoaderDelegate, jioVmapInfo: JioAdsFramework.JioVmapInfo)
  @objc deinit
  public func request()
  public func playAd()
  public func pauseAd()
  public func resumeAd()
  public func skipCuePoint()
  public func notifyCuePointMissed(playerCurrentTime: Swift.Int)
  public func getCurrentAdBreakCount() -> Swift.Int?
  public func shouldShowAdCounter(showAdCounter: Swift.Bool)
  public func setCurrentPlayerTime(currentPlayerTime: Swift.Double, totalContentDuration: Swift.Double)
  public func setCustomLayout(view: UIKit.UIView)
  public func contentComplete()
  public func destroy()
  public func setCompanions(companions: [JioAdsFramework.JioAdCompanion], delegate: any JioAdsFramework.JioCompanionAdviewProtocol)
  public func setAutoPlayAdBreaks(isEnabled: Swift.Bool)
}
public struct RILRemoveFromCartEvent : JioAdsFramework.RILEventProtocol, Swift.Codable {
  public var eventType: Swift.String?
  public var eventID: Swift.Int
  public var time: Swift.String?
  public var parameters: [Swift.String : Any]?
  public var productDetails: JioAdsFramework.RILProductDetails?
  public init(parameters: [Swift.String : Any]? = nil, productDetails: JioAdsFramework.RILProductDetails)
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
public class JioAdTargettingBuilder {
  public init()
  @discardableResult
  public func set(channelId: Swift.String) -> JioAdsFramework.JioAdTargettingBuilder
  @discardableResult
  public func set(channelName: Swift.String) -> JioAdsFramework.JioAdTargettingBuilder
  @discardableResult
  public func set(showName: Swift.String) -> JioAdsFramework.JioAdTargettingBuilder
  @discardableResult
  public func set(pageCategory: Swift.String) -> JioAdsFramework.JioAdTargettingBuilder
  @discardableResult
  public func set(sectionCategory: Swift.String) -> JioAdsFramework.JioAdTargettingBuilder
  @discardableResult
  public func set(languageOfArticle: Swift.String) -> JioAdsFramework.JioAdTargettingBuilder
  @discardableResult
  public func set(language: Swift.String) -> JioAdsFramework.JioAdTargettingBuilder
  @discardableResult
  public func set(contentId: Swift.String) -> JioAdsFramework.JioAdTargettingBuilder
  @discardableResult
  public func set(contentTitle: Swift.String) -> JioAdsFramework.JioAdTargettingBuilder
  @discardableResult
  public func set(contentType: Swift.String) -> JioAdsFramework.JioAdTargettingBuilder
  @discardableResult
  public func set(vendor: Swift.String) -> JioAdsFramework.JioAdTargettingBuilder
  @discardableResult
  public func set(actor: Swift.String) -> JioAdsFramework.JioAdTargettingBuilder
  @discardableResult
  public func set(objects: Swift.String) -> JioAdsFramework.JioAdTargettingBuilder
  @discardableResult
  public func set(kidsProtected: JioAdsFramework.JioAdTargetting.KidsProtected) -> JioAdsFramework.JioAdTargettingBuilder
  @discardableResult
  public func set(gender: JioAdsFramework.JioAdTargetting.Gender) -> JioAdsFramework.JioAdTargettingBuilder
  @discardableResult
  public func set(appVersion: Swift.String) -> JioAdsFramework.JioAdTargettingBuilder
  @discardableResult
  public func set(genre: Swift.String) -> JioAdsFramework.JioAdTargettingBuilder
  @discardableResult
  public func set(state: Swift.String) -> JioAdsFramework.JioAdTargettingBuilder
  @discardableResult
  public func set(city: Swift.String) -> JioAdsFramework.JioAdTargettingBuilder
  @discardableResult
  public func set(age: Swift.String) -> JioAdsFramework.JioAdTargettingBuilder
  @discardableResult
  public func set(country: Swift.String) -> JioAdsFramework.JioAdTargettingBuilder
  @discardableResult
  public func set(pincode: Swift.String) -> JioAdsFramework.JioAdTargettingBuilder
  @discardableResult
  public func set(keywords: Swift.String) -> JioAdsFramework.JioAdTargettingBuilder
  @discardableResult
  public func set(placementName: Swift.String) -> JioAdsFramework.JioAdTargettingBuilder
  @discardableResult
  public func set(customMetaData: [Swift.String : Swift.String]) -> JioAdsFramework.JioAdTargettingBuilder
  public func build() -> JioAdsFramework.JioAdTargetting
  @discardableResult
  public func set(xbz: Swift.String) -> JioAdsFramework.JioAdTargettingBuilder
  @objc deinit
}
public struct RILProductDetails : Swift.Codable {
  public var parameters: [Swift.String : Any]?
  public var listName: Swift.String?
  public var currency: Swift.String?
  public var productList: [JioAdsFramework.RILProductListEvent]?
  public init(currency: Swift.String?, productList: [JioAdsFramework.RILProductListEvent]?, listName: Swift.String? = nil, parameters: [Swift.String : Any]? = nil)
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
public enum ViewPortStatus : Swift.String {
  case inPort
  case outOfPort
  public init?(rawValue: Swift.String)
  public typealias RawValue = Swift.String
  public var rawValue: Swift.String {
    get
  }
}
public protocol JioViewPortObserverDelegate {
  func viewPortStatusUpdate(status: JioAdsFramework.ViewPortStatus?, percentage: CoreFoundation.CGFloat?, adSpotId: Swift.String?)
}
public struct RILLocationEvent : JioAdsFramework.RILEventProtocol, Swift.Codable {
  public var eventType: Swift.String?
  public var eventID: Swift.Int
  public var time: Swift.String?
  public var parameters: [Swift.String : Any]?
  public var pincode: Swift.String?
  public var latitude: Swift.String?
  public var longitude: Swift.String?
  public var productDetails: JioAdsFramework.RILProductDetails?
  #warning("Pincode is mandatory as per backend but as not mentioned in Doc")
  public init(parameters: [Swift.String : Any]? = nil, productDetails: JioAdsFramework.RILProductDetails?, latitude: Swift.String?, longitude: Swift.String?, pincode: Swift.String?)
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
public struct RILSearchViewEvent : JioAdsFramework.RILEventProtocol, Swift.Codable {
  public var eventType: Swift.String?
  public var eventID: Swift.Int
  public var time: Swift.String?
  public var parameters: [Swift.String : Any]?
  public var searchDetails: JioAdsFramework.RILSearchDetails?
  public init(parameters: [Swift.String : Any]? = nil, searchDetails: JioAdsFramework.RILSearchDetails)
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
public struct RILSearchDetails : Swift.Codable {
  public var searchString: Swift.String?
  public var filters: [JioAdsFramework.RILFilter]?
  public var currency: Swift.String?
  public var productList: [JioAdsFramework.RILProductListEvent]
  public init(productList: [JioAdsFramework.RILProductListEvent], filters: [JioAdsFramework.RILFilter]? = nil)
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
public struct RILFilter : Swift.Codable {
  public var name: Swift.String?
  public var value: [Swift.String]?
  public init()
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
public enum JioCTAaction {
  case native
  case embedded
  case deepLink
  public static func == (a: JioAdsFramework.JioCTAaction, b: JioAdsFramework.JioCTAaction) -> Swift.Bool
  public func hash(into hasher: inout Swift.Hasher)
  public var hashValue: Swift.Int {
    get
  }
}
public protocol JioCompanionAdviewProtocol : AnyObject {
  func onCompanionAdFailedToLoad(companionAdView: JioAdsFramework.JioAdCompanion?, error: JioAdsFramework.JioAdError)
  func onCompanionAdClosed(companionAdView: JioAdsFramework.JioAdCompanion?)
  func onCompanionAdRender(companionAdView: JioAdsFramework.JioAdCompanion?)
  func onCompanionAdChange(companionAdView: JioAdsFramework.JioAdCompanion?)
}
public enum JioCarousalCompanionEvent {
  case adRender
  case adClosed
  case adFailedToLoad
  public static func == (a: JioAdsFramework.JioCarousalCompanionEvent, b: JioAdsFramework.JioCarousalCompanionEvent) -> Swift.Bool
  public func hash(into hasher: inout Swift.Hasher)
  public var hashValue: Swift.Int {
    get
  }
}
public protocol JioCarousalCompanionProtocol : AnyObject {
  func carousalCompanionStatus(event: JioAdsFramework.JioCarousalCompanionEvent)
}
public struct RILAddToWishListEvent : JioAdsFramework.RILEventProtocol, Swift.Codable {
  public var eventType: Swift.String?
  public var eventID: Swift.Int
  public var time: Swift.String?
  public var parameters: [Swift.String : Any]?
  public var productDetails: JioAdsFramework.RILProductDetails?
  public init(parameters: [Swift.String : Any]? = nil, productDetails: JioAdsFramework.RILProductDetails)
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
public struct JioAdDetails {
  public var adSpotId: Swift.String?
  public var campaignId: Swift.String?
  public var adId: Swift.String?
  public var adCampaignTitle: Swift.String?
  public var adCtaText: Swift.String?
  public var adUnitSize: Swift.String?
  public var podCount: Swift.Int?
  public var clickUrl: Swift.String?
  public var clickTrackers: [Swift.String]?
  public var impressionCount: Swift.Int?
  public var viewableImpressionCount: Swift.Int?
}
extension JioAdsFramework.JioAdView {
  @_Concurrency.MainActor @preconcurrency public func setMetaData(metaDataDict: [Swift.String : Any])
  @_Concurrency.MainActor @preconcurrency public func setRequestTimeout(requestTimeout: Swift.Int)
  @_Concurrency.MainActor @preconcurrency public func setMediaTimeout(playerTimeOut: Swift.Int)
  @_Concurrency.MainActor @preconcurrency public func setPodTimeout(podPlayerTimeOut: Swift.Int)
  @_Concurrency.MainActor @preconcurrency public func setCustomImageSize(width: Swift.Int, height: Swift.Int)
  @_Concurrency.MainActor @preconcurrency public func enableMediaCaching(cacheMode: JioAdsFramework.CacheMode)
  @_Concurrency.MainActor @preconcurrency public func getAdSpotId() -> Swift.String
  @_Concurrency.MainActor @preconcurrency public func setKeyword(keyword: Swift.String)
  @_Concurrency.MainActor @preconcurrency public func setLanguageOfArticle(langOfArticleName: Swift.String)
  @_Concurrency.MainActor @preconcurrency public func setOrientation(orientationType: JioAdsFramework.OrientationType)
  @_Concurrency.MainActor @preconcurrency public func setCloseAfter(delay: Swift.Int)
  @_Concurrency.MainActor @preconcurrency public func setAdPodVariant(podVarient: JioAdsFramework.AdPodVariant)
  @_Concurrency.MainActor @preconcurrency public func enableTransitionLoader(enableTransitionLoader: Swift.Bool)
  @_Concurrency.MainActor @preconcurrency public func setRequestedAdDuration(adPodDuration: Swift.Int)
  @_Concurrency.MainActor @preconcurrency public func setMinimumAdDuration(adPodDuration: Swift.Int)
  @_Concurrency.MainActor @preconcurrency public func setRequestedAdCount(adPodCount: Swift.Int)
  @_Concurrency.MainActor @preconcurrency public func setDisplayAdSize(displaySizes: [JioAdsFramework.DynamicDisplaySize])
  @_Concurrency.MainActor @preconcurrency public func getDisplayAdSize() -> Swift.String
  @_Concurrency.MainActor @preconcurrency public func setInstreamAudioCompanion(companionAdSize: JioAdsFramework.DynamicDisplaySize?, companionPortraitImage: UIKit.UIImage?, companionLandscapeImage: UIKit.UIImage?)
  @_Concurrency.MainActor @preconcurrency public func getAdClickUrl() -> Swift.String?
  @_Concurrency.MainActor @preconcurrency public func getAdAllClickUrl() -> [Swift.String : Swift.String]?
  @_Concurrency.MainActor @preconcurrency public func enableClickCallbacktoApp(bool: Swift.Bool)
}
extension JioAdsFramework.JioAdView {
  @_Concurrency.MainActor @preconcurrency public func setPublisherDictionay(featureDict: [Swift.String : Swift.String])
  @_Concurrency.MainActor @preconcurrency public func setSkipThumbnailUrl(thumbnailUrl: Swift.String)
  @_Concurrency.MainActor @preconcurrency public func isInstreamVideoAdPlaying() -> Swift.Bool
  @_Concurrency.MainActor @preconcurrency public func isGCP(GCP: Swift.Bool)
  @_Concurrency.MainActor @preconcurrency public func setPackageName(packageName: Swift.String)
  @_Concurrency.MainActor @preconcurrency public func set(streamType: JioAdsFramework.StreamType)
}
extension JioAdsFramework.JioAdView {
  @_Concurrency.MainActor @preconcurrency public func isFullScreenEnabled(enabled: Swift.Bool)
}
extension JioAdsFramework.JioAdView {
  @_Concurrency.MainActor @preconcurrency public func setDisplayMaxSize(maxWidth: Swift.Int, maxHeight: Swift.Int)
  @_Concurrency.MainActor @preconcurrency public func setDynamicDisplaySupportedResponseType(isHtml: Swift.Bool, isNative: Swift.Bool, isVast: Swift.Bool)
}
public struct RILDeepLinkLaunchEvent : JioAdsFramework.RILEventProtocol, Swift.Codable {
  public var eventType: Swift.String?
  public var eventID: Swift.Int
  public var time: Swift.String?
  public var parameters: [Swift.String : Any]?
  public var deeplinkUri: Swift.String?
  public var referrerApp: Swift.String?
  public var firstLaunch: Swift.Int?
  public init(parameters: [Swift.String : Any]? = nil, deeplinkUri: Swift.String, referrerApp: Swift.String?)
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
public struct RILProductCancelledEvent : JioAdsFramework.RILEventProtocol, Swift.Codable {
  public var eventType: Swift.String?
  public var clickId: Swift.String?
  public var eventID: Swift.Int
  public var time: Swift.String?
  public var parameters: [Swift.String : Any]?
  public var transactionId: Swift.String?
  public var productDetails: JioAdsFramework.RILProductDetails?
  public init(transactionId: Swift.String, parameters: [Swift.String : Any]? = nil, productDetails: JioAdsFramework.RILProductDetails)
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
@objc public class JioCTAHandler : ObjectiveC.NSObject {
  public init(urls: JioAdsFramework.CTAUrls?, viewControllerToPresent: UIKit.UIViewController, delegate: any JioAdsFramework.JioCTAHelperDelegate, _ adClickOption: JioAdsFramework.AdClickOption? = nil, adSpotId: Swift.String? = nil)
  @objc deinit
  public func open()
  public func invalidate()
}
extension JioAdsFramework.JioCTAHandler : JioAdsFramework.JioCTAHelperDelegate {
  public func onSuccessRedirect(event: JioAdsFramework.JioCTAaction)
  public func onFailureRedirect(error: JioAdsFramework.JioAdError)
  public func onCompleteCTA()
}
extension JioAdsFramework.JioCTAHandler {
  public func getAdClickUrl() -> Swift.String?
  public func getAdAllClickUrl() -> [Swift.String : Swift.String]?
}
public struct RILProductSearchViewEvent : JioAdsFramework.RILEventProtocol, Swift.Codable {
  public var eventType: Swift.String?
  public var eventID: Swift.Int
  public var time: Swift.String?
  public var parameters: [Swift.String : Any]?
  public var searchDetails: JioAdsFramework.RILSearchDetails?
  public init(parameters: [Swift.String : Any]? = nil, searchDetails: JioAdsFramework.RILSearchDetails)
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
public struct RILAddToCartEvent : JioAdsFramework.RILEventProtocol, Swift.Codable {
  public var eventType: Swift.String?
  public var eventID: Swift.Int
  public var time: Swift.String?
  public var parameters: [Swift.String : Any]?
  public var clickId: Swift.String?
  public var productDetails: JioAdsFramework.RILProductDetails?
  public init(productDetails: JioAdsFramework.RILProductDetails, parameters: [Swift.String : Any]? = nil)
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
public protocol JioVmapAdsLoaderDelegate {
  func onJioVmapAdsLoaded(cuePoints: [Swift.Int])
  func onJioVmapEvent(event: JioAdsFramework.AdEventType, adMeta: JioAdsFramework.AdMeta?, dict: [Swift.String : Swift.String]?)
  func onJioVmapError(cuePoint: Swift.Int?, error: JioAdsFramework.JioAdError)
}
@objc @_inheritsConvenienceInitializers public class CampanionData : ObjectiveC.NSObject {
  public var clickTrackerUrl: [Swift.String]
  public var adParam: Swift.String
  public var clickThrouhUrl: Swift.String
  public var deeplinkUrl: Swift.String
  @objc override dynamic public init()
  @objc deinit
}
public class JioAdCompanion {
  public var container: UIKit.UIView? {
    get
  }
  public init(companionContainer: UIKit.UIView?, adslotId: Swift.String?, size: JioAdsFramework.DynamicDisplaySize?)
  @objc deinit
  public func destroy()
}
extension JioAdsFramework.JioAdCompanion : Swift.Equatable {
  public static func == (lhs: JioAdsFramework.JioAdCompanion, rhs: JioAdsFramework.JioAdCompanion) -> Swift.Bool
}
extension JioAdsFramework.JioAdCompanion : Swift.Hashable {
  public func hash(into hasher: inout Swift.Hasher)
  public var hashValue: Swift.Int {
    get
  }
}
public protocol JioCTAHelperDelegate : AnyObject {
  func onSuccessRedirect(event: JioAdsFramework.JioCTAaction)
  func onFailureRedirect(error: JioAdsFramework.JioAdError)
  func onCompleteCTA()
}
public struct ConvTrackingModel : Swift.Codable {
  public let ad: JioAdsFramework.convAdData?
  public let config: JioAdsFramework.AdConfig?
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
public struct convAdData : Swift.Codable {
  public let viewableimptrackers: [Swift.String]?, imptrackers: [Swift.String]?
  public let clktrackers: [Swift.String]?
  public let cid: Swift.String?
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
public struct AdConfig : Swift.Codable {
  public let viewableimptrackers: [Swift.String]?, imptrackers: [Swift.String]?
  public let c: Swift.String?
  public let cid: Swift.String?
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
public protocol JioSSAICompanionAdProtocol : AnyObject {
  func onCompanionError(error: JioAdsFramework.JioAdError, container: UIKit.UIView)
  func onCompanionClose(container: UIKit.UIView)
  func onCompanionRender(container: UIKit.UIView)
  func onCompanionClick()
}
public protocol JioSSAIAdEventsProtocol : AnyObject {
  func onAdMediaStart(adMetaData: JioAdsFramework.JioAdDataModel)
  func onStreamReady(streamUrl: Foundation.URL)
  func onAdMediaEnd()
  func onAdError(error: any Swift.Error)
  func onAdChange(adMetaData: JioAdsFramework.JioAdDataModel)
  func onAdQuartileEventFired(eventType: JioAdsFramework.EQuartileEventType)
  func onAdBreakDateRanges(adBreaks: [JioAdsFramework.AdBreakDateRange])
}
public protocol JioSpotAdCampanionProtocol : AnyObject {
  func onCompanionStart(cid: Swift.String, admetaData: JioAdsFramework.JioAdDataModel?)
  func onCompanionComplete(cid: Swift.String)
  func onTrackersFired(trackers: [Swift.String], type: Swift.String, isDefault: Swift.Bool)
}
extension JioAdsFramework.JioSpotAdCampanionProtocol {
  public func onTrackersFired(trackers: [Swift.String], type: Swift.String, isDefault: Swift.Bool)
}
extension JioAdsFramework.JioSSAIAdEventsProtocol {
  public func onAdQuartileEventFired(eventType: JioAdsFramework.EQuartileEventType)
}
extension JioAdsFramework.JioSSAIAdEventsProtocol {
  public func onAdBreakDateRanges(adBreaks: [JioAdsFramework.AdBreakDateRange])
}
@_hasMissingDesignatedInitializers public class JioAdTargetting {
  public enum KidsProtected {
    case yes
    case no
    public var strValue: Swift.String {
      get
    }
    public static func == (a: JioAdsFramework.JioAdTargetting.KidsProtected, b: JioAdsFramework.JioAdTargetting.KidsProtected) -> Swift.Bool
    public func hash(into hasher: inout Swift.Hasher)
    public var hashValue: Swift.Int {
      get
    }
  }
  public enum Gender {
    case male
    case female
    case other
    public var strValue: Swift.String {
      get
    }
    public static func == (a: JioAdsFramework.JioAdTargetting.Gender, b: JioAdsFramework.JioAdTargetting.Gender) -> Swift.Bool
    public func hash(into hasher: inout Swift.Hasher)
    public var hashValue: Swift.Int {
      get
    }
  }
  @objc deinit
}
public protocol JioAdRendererDelegate : AnyObject {
  func prepareSuccess()
  func prepareFailed(error: JioAdsFramework.JioAdError, errorUrls: [Swift.String])
  func loadSuccess()
  func loadFailed(error: JioAdsFramework.JioAdError, errorUrls: [Swift.String])
  func appendSuccess()
  func appendFailed(error: JioAdsFramework.JioAdError, errorUrls: [Swift.String])
  func renditionComplete()
}
public struct RILPurchaseReturnEvent : JioAdsFramework.RILEventProtocol, Swift.Codable {
  public var eventType: Swift.String?
  public var clickId: Swift.String?
  public var eventID: Swift.Int
  public var time: Swift.String?
  public var parameters: [Swift.String : Any]?
  public var transactionId: Swift.String?
  public var productDetails: JioAdsFramework.RILProductDetails?
  public init(transactionId: Swift.String, parameters: [Swift.String : Any]? = nil, productDetails: JioAdsFramework.RILProductDetails)
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
public class JioLbandSqueezeOverlayAdsLoader {
  public init(jioLbandSqueezeOverlayAdsInfo: JioAdsFramework.JioLbandSqueezeOverlayAdsInfo, delegate: any JioAdsFramework.JioLbandAdsLoaderDelegate)
  @objc deinit
  public func setCurrentPlayerTime(currentPlayerTime: Swift.Double, totalContentDuration: Swift.Double)
  public func closeNonLinearAd()
}
extension JioAdsFramework.JioLbandSqueezeOverlayAdsLoader {
  public func setCompanions(companions: [JioAdsFramework.JioAdCompanion], delegate: any JioAdsFramework.JioCompanionAdviewProtocol)
}
extension UIKit.UIApplication {
  @_Concurrency.MainActor @preconcurrency public class func getTopViewController() -> UIKit.UIViewController?
}
@_hasMissingDesignatedInitializers public class JioAdSdk {
  public static var isMECURYPointed: Swift.Bool
  public static var IMSI: Swift.String?
  public static var userLocation: CoreLocation.CLLocation?
  public static var deviceVendorId: Swift.String?
  public static var isXCTestingMode: Swift.Bool
  public static var errorLoggingForCSLMasterConfig: Swift.Int
  public static var isDemoMode: Swift.Bool
  public static var isCTATypeMode: Swift.Bool
  public static func initialize(environment: Swift.String = "Production", appType: JioAdsFramework.EAppType = .unknown)
  public class func fetchBpid()
  public class func getSDKLogFilePath() -> Swift.String?
  public class func setLogLevel(logLevel: JioAdsFramework.LogLevel)
  public class func getLogLevel() -> JioAdsFramework.LogLevel
  public class func getRequestParams(config: [Swift.String : Any]) -> [Swift.String : Any]
  public static func isAuthorized() -> Swift.Bool
  public class func getSDKVersion() -> Swift.String
  public class func clearCachedMedia(mediaType: JioAdsFramework.MediaType, adView: JioAdsFramework.JioAdView?)
  public class func getSHA1(rawString: Swift.String) -> Swift.String?
  public class func getSHA2(rawString: Swift.String) -> Swift.String?
  public class func setPackageName(packageName: Swift.String)
  public class func setAppGroupIndentity(groupIndentity: Swift.String)
  public class func getUid() -> Swift.String?
  public class func getIDFA() -> Swift.String
  public class func setMetaData(metaDataDict: [Swift.String : Any])
  public class func setUserAge(value: Swift.String?)
  public class func setUserCity(value: Swift.String?)
  public class func setUserGender(value: Swift.String?)
  public class func setLanguageOfArticle(value: Swift.String?)
  public class func setKeyword(value: Swift.String?)
  public class func setChannelID(value: Swift.String?)
  public class func setChannelName(value: Swift.String?)
  public class func setShowName(value: Swift.String?)
  public class func setPageCategory(value: Swift.String?)
  public class func setSectionCategory(value: Swift.String?)
  public class func setLanguage(value: Swift.String?)
  public class func setContentId(value: Swift.String?)
  public class func setContentType(value: Swift.String?)
  public class func setVendor(value: Swift.String?)
  public class func setActor(value: Swift.String?)
  public class func setObjects(value: Swift.String?)
  public class func setKidsProtected(value: Swift.Bool?)
  public class func setAppVersion(value: Swift.String?)
  public class func setGenre(value: Swift.String?)
  public class func setState(value: Swift.String?)
  public class func setCountry(value: Swift.String?)
  public class func setPincode(value: Swift.String?)
  public class func setPlacementName(value: Swift.String?)
  public class func release()
  public static func setDeviceVendorId(_ deviceVendorId: Swift.String)
  public static func setUserAgent(userAgent: Swift.String)
  public class func setSubscriberid(_ subId: Swift.String)
  @objc deinit
}
@objc public enum APIEnvironment : Swift.Int, Swift.RawRepresentable {
  case production
  case staging
  case sit
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
public class JioLbandSqueezeOverlayAdsInfo {
  public init(adspotId: Swift.String, cuePoints: Swift.String?, repeatedCuePoints: Swift.String?, publisherContainer: UIKit.UIView, adContainer: UIKit.UIView, adPlayerContainer: UIKit.UIView, adOverlayContainer: UIKit.UIView? = nil, jioAdTargetting: JioAdsFramework.JioAdTargetting? = nil, prepareThreshold: Swift.Int? = nil, mediaCaching: JioAdsFramework.CacheMode? = nil, mediaTimeout: Swift.Int? = nil)
  @objc deinit
}
extension JioAdsFramework.JioLbandSqueezeOverlayAdsInfo : Swift.CustomDebugStringConvertible {
  public var debugDescription: Swift.String {
    get
  }
}
extension UIKit.UIImageView {
  @_Concurrency.MainActor @preconcurrency public func loadGif(name: Swift.String)
  @available(iOS 9.0, *)
  @_Concurrency.MainActor @preconcurrency public func loadGif(asset: Swift.String)
}
extension UIKit.UIImage {
  public class func gif(data: Foundation.Data) -> UIKit.UIImage?
  public class func gif(url: Swift.String) -> UIKit.UIImage?
  public class func gif(name: Swift.String) -> UIKit.UIImage?
  @available(iOS 9.0, *)
  public class func gif(asset: Swift.String) -> UIKit.UIImage?
}
public enum AdClickOption : Swift.Int {
  case inApplication
  case inDeviceBrowser
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
public enum AdEventType : Swift.String {
  case contentPauseRequested
  case contentResumeRequested
  case adBreakReady
  case adBreakStarted
  case adBreakEnded
  case allAdsCompleted
  case adStarted
  case adFirstQuartile
  case adMidPoint
  case adThirdQuartile
  case adProgress
  case adPaused
  case adResumed
  case adSkipped
  case adClicked
  case adCompleted
  case adExpand
  case adCollapse
  case adFullScreen
  case adExitFullScreen
  public init?(rawValue: Swift.String)
  public typealias RawValue = Swift.String
  public var rawValue: Swift.String {
    get
  }
}
extension JioAdsFramework.JioAdView {
  @_Concurrency.MainActor @preconcurrency public func setVideoBitRate(videoBitRate: Swift.Int)
  @_Concurrency.MainActor @preconcurrency public func hideAdControls()
  @_Concurrency.MainActor @preconcurrency public func isMediaPlaying() -> Swift.Bool?
  @_Concurrency.MainActor @preconcurrency public func showAdControls()
  @_Concurrency.MainActor @preconcurrency public func expandAd()
  @_Concurrency.MainActor @preconcurrency public func collapseAd()
  @_Concurrency.MainActor @preconcurrency public func mute(_ mute: Swift.Bool)
}
public protocol JioMediaTracker : AnyObject {
  func onMediaPrepared()
  func onImpression()
  func onViewableImpression()
  func onClick()
  func onStart()
  func onFirstQuartile()
  func onMidPoint()
  func onThirdQuartile()
  func onEnd()
  func onPause()
  func onResume()
  func onMute()
  func onUnMute()
  func onClose()
  func onExpand()
  func onCollapse()
  func onSkipable()
  func onSkip()
  func onReplay(loopCount: Swift.Int)
  func onError(jioAdError: JioAdsFramework.JioAdError)
  func onProgress(currentDuration: Swift.Double, mediaDuration: Swift.Double?)
  func onCompletedPlaying()
  func onAllMediaCompleted()
  func onMediaIndexUpdate(currentAdIndex: Swift.Int, totalAdsCount: Swift.Int)
  func onFullScreen()
  func onExitFullScreen()
}
extension JioAdsFramework.JioMediaTracker {
  public func onMediaPrepared()
  public func onImpression()
  public func onViewableImpression()
  public func onClick()
  public func onStart()
  public func onFirstQuartile()
  public func onMidPoint()
  public func onThirdQuartile()
  public func onEnd()
  public func onPause()
  public func onResume()
  public func onMute()
  public func onUnMute()
  public func onClose()
  public func onExpand()
  public func onCollapse()
  public func onSkipable()
  public func onSkip()
  public func onReplay(loopCount: Swift.Int)
  public func onError(jioAdError: JioAdsFramework.JioAdError)
  public func onProgress(currentDuration: Swift.Double, mediaDuration: Swift.Double?)
  public func onCompletedPlaying()
  public func onAllMediaCompleted()
  public func onMediaIndexUpdate(currentAdIndex: Swift.Int, totalAdsCount: Swift.Int)
  public func onFullScreen()
  public func onExitFullScreen()
}
public typealias Events = [Swift.String : Any]
public enum Environments {
  case production
  case staging
  case sit
  public func description() -> Swift.String
  public static func == (a: JioAdsFramework.Environments, b: JioAdsFramework.Environments) -> Swift.Bool
  public func hash(into hasher: inout Swift.Hasher)
  public var hashValue: Swift.Int {
    get
  }
}
public struct JioAdDataModel {
  public var adId: Swift.String?
  public var cid: Swift.String?
  public var isSpotAd: Swift.Bool
  public var adTitle: Swift.String?
  public var adIndex: Swift.Int?
  public var adDuration: Swift.String?
  public var server: Swift.String?
  public var position: Swift.String?
  public var adClickUrl: Swift.String
  public var adparamTitle: Swift.String
  public var adDescription: Swift.String
  public var titleTextColor: Swift.String
  public var descriptionTextColor: Swift.String
  public var iconUrl: Swift.String
  public var ctaText: Swift.String
  public var ctaBtnBackgroundColour: Swift.String
  public var ctaTextColour: Swift.String
  public var secondaryCtaText: Swift.String
  public var secondaryCtaTextColor: Swift.String
  public var secondaryCtaButtonColor: Swift.String
  public var secondaryCtaUrl: Swift.String?
  public var secondaryCtaUrlTracker: Swift.String?
  public var adServingId: Swift.String?
  public var universalAdId: [(Swift.String, Swift.String)]?
  public var openInApp: Swift.String?
  public var clickTrackerURL: [Swift.String]?
  public var clickThroughURL: Swift.String?
  public var deepLink: Swift.String
}
@objc @_inheritsConvenienceInitializers @_hasMissingDesignatedInitializers @_Concurrency.MainActor @preconcurrency final public class InterstitialVideoLayout : UIKit.UIView {
  @objc deinit
}
public struct RILDeviceInfoEvent : JioAdsFramework.RILEventProtocol, Swift.Codable {
  public var eventType: Swift.String?
  public var parameters: [Swift.String : Any]?
  public var time: Swift.String?
  public var eventID: Swift.Int
  public var manufacturer: Swift.String?
  public var model: Swift.String?
  public var platform: Swift.String?
  public var osName: Swift.String?
  public var osVersion: Swift.String?
  public var devicetype: Swift.Int?
  public init()
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
extension JioAdsFramework.JioSSAIManager {
  public func setSpotAdAppId(appId: Swift.String?)
  public func setSpotAdAsi(asi: Swift.String?)
  public func setSpotAdContentName(contentName: Swift.String?)
  public func setSpotAdChannelid(channelid: Swift.String?)
  public func setSpotAdContentId(contentId: Swift.String?)
  public func setSpotAdData(_ spotAdData: [JioAdsFramework.JioSpotAdModel])
  public func setPlayerItemErrorLog(_ errorLog: AVFoundation.AVPlayerItemErrorLog)
  public func getCreativeData() -> [Swift.String : Any]
  public func fireSpotAdImpression(cid: Swift.String, molImpressionId: Swift.String? = nil)
  public func fireSpotAdComplete(cid: Swift.String, molImpressionId: Swift.String? = nil)
  public func fireSpotStart(cid: Swift.String, molImpressionId: Swift.String? = nil)
  public func fireSpotFirstQuartile(cid: Swift.String, molImpressionId: Swift.String? = nil)
  public func fireSpotMidQuartile(cid: Swift.String, molImpressionId: Swift.String? = nil)
  public func fireSpotThirdQuartile(cid: Swift.String, molImpressionId: Swift.String? = nil)
}
@objc @_inheritsConvenienceInitializers @_hasMissingDesignatedInitializers @_Concurrency.MainActor @preconcurrency public class JioCollectionViewLayout : UIKit.UIView {
  @objc deinit
}
@objc @_inheritsConvenienceInitializers public class SpotAdTrackerAPIs : ObjectiveC.NSObject {
  public func initJioAdsTracker()
  public func fetchCampanionData(scteID: Swift.String, completion: @escaping (JioAdsFramework.CampanionData?) -> ())
  public func setAppId(appid: Swift.String)
  public func setAdSpotId(adspotId: Swift.String)
  public func setContentName(contentName: Swift.String)
  public func setChannelId(channelId: Swift.String)
  public func setContentId(contentId: Swift.String)
  public func setCustomParam(customParam: [Swift.String : Any])
  public func setCreativeID(cid: Swift.String)
  public func triggerImpression(scteID: Swift.String, molImpressionId: Swift.String? = nil, shouldTriggerImpression: Swift.Bool, completion: @escaping (Swift.Bool) -> ())
  public func triggerStartEvent(scteID: Swift.String, molImpressionId: Swift.String? = nil)
  public func triggerFirstQuartileEvent(scteID: Swift.String, molImpressionId: Swift.String? = nil)
  public func triggerMidPointEvent(scteID: Swift.String, molImpressionId: Swift.String? = nil)
  public func triggerThirdQuartileEvent(scteID: Swift.String, molImpressionId: Swift.String? = nil)
  public func triggerCompletedEvent(scteID: Swift.String, molImpressionId: Swift.String? = nil)
  @objc override dynamic public init()
  @objc deinit
}
public enum VastTrackingEvents : Swift.String, Swift.Codable {
  case impression
  case viewableImpression
  case creativeView
  case start
  case complete
  case midpoint
  case firstQuartile
  case thirdQuartile
  case mute
  case unmute
  case pause
  case resume
  case close
  case closeLinear
  case progress
  case offset
  case expand
  case collapse
  case skip
  case click
  case companionCreativeView
  case companionClose
  case companionClickTracking
  case clickTracking
  case fullscreen
  case exitFullscreen
  case error
  case replay
  public init?(rawValue: Swift.String)
  public typealias RawValue = Swift.String
  public var rawValue: Swift.String {
    get
  }
}
extension JioAdsFramework.VastTrackingEvents : Swift.CaseIterable {
  public typealias AllCases = [JioAdsFramework.VastTrackingEvents]
  nonisolated public static var allCases: [JioAdsFramework.VastTrackingEvents] {
    get
  }
}
public enum LogTrackingEvents : Swift.String, Swift.Codable, Swift.CustomStringConvertible {
  case onMediaPrepared
  case impression
  case viewableImpression
  case creativeView
  case start
  case complete
  case end
  case midpoint
  case firstQuartile
  case midQuartile
  case thirdQuartile
  case mute
  case unmute
  case pause
  case resume
  case close
  case closeLinear
  case progress
  case offset
  case expand
  case collapse
  case skip
  case skipable
  case click
  case clickTracking
  case companionCreativeView
  case companionClose
  case companionClickTracking
  case fullscreen
  case exitFullscreen
  case error
  case replay
  case noCTAURL
  case clickForCustomAd
  case isClickImpressionFiredTrue
  case isClickImpressionFiredFalse
  case deeplinkSuccess
  case deeplinkFailure
  case deeplinkUrl
  case brandLinkUrl
  case clickThroughUrl
  case clickFBUrl
  case brandLinkUrlEmpty
  case clickThroughUrlEmpty
  case clickFBUrlEmpty
  case deeplinkUrlEmpty
  case deeplinkUrlBeforeMacroReplace
  case deeplinkUrlAfterMacroReplace
  case clickThroughUrlBeforeMacroReplace
  case clickThroughUrlAfterMacroReplace
  case fallback
  case externalTracker
  case externalClick
  case fireUrlForFrate
  case macroReplacedUrls
  case macroReplacedUrlsEmpty
  case isPrimaryImpressionFiredTrue
  case isPrimaryImpressionFiredFalse
  case isViewableImpressionFiredTrue
  case isViewableImpressionFiredFalse
  case viewControllerNotFound
  case onSuccessRedirect
  case onFailureRedirect
  case onCompleteCTA
  case masterConfigParsingFailed
  case masterConfigURL
  case masterConfigGetFromServer
  case masterConfigNilFromServer
  case masterConfigStored
  case masterConfigAvailableInLocal
  case masterConfigNotAvailableInLocal
  case masterConfigExpired
  case masterConfigNotExpired
  case masterConfigEffectivePackageName
  case masterConfigFetchedFromLocal
  case masterConfigClear
  case masterConfigDownloaded
  case masterConfigExpTime
  case masterConfigExpValue
  case masterConfigAdSpotDataFetched
  public var description: Swift.String {
    get
  }
  public init?(rawValue: Swift.String)
  public typealias RawValue = Swift.String
  public var rawValue: Swift.String {
    get
  }
}
extension JioAdsFramework.LogTrackingEvents : Swift.CaseIterable {
  public typealias AllCases = [JioAdsFramework.LogTrackingEvents]
  nonisolated public static var allCases: [JioAdsFramework.LogTrackingEvents] {
    get
  }
}
extension JioAdsFramework.JioAdView {
  @_Concurrency.MainActor @preconcurrency public func setUserAge(value: Swift.String?)
  @_Concurrency.MainActor @preconcurrency public func setUserCity(value: Swift.String?)
  @_Concurrency.MainActor @preconcurrency public func setUserGender(value: Swift.String?)
  @_Concurrency.MainActor @preconcurrency public func setLanguageOfArticle(value: Swift.String?)
  @_Concurrency.MainActor @preconcurrency public func setKeyword(value: Swift.String?)
  @_Concurrency.MainActor @preconcurrency public func setChannelID(value: Swift.String?)
  @_Concurrency.MainActor @preconcurrency public func setChannelName(value: Swift.String?)
  @_Concurrency.MainActor @preconcurrency public func setShowName(value: Swift.String?)
  @_Concurrency.MainActor @preconcurrency public func setPageCategory(value: Swift.String?)
  @_Concurrency.MainActor @preconcurrency public func setSectionCategory(value: Swift.String?)
  @_Concurrency.MainActor @preconcurrency public func setLanguage(value: Swift.String?)
  @_Concurrency.MainActor @preconcurrency public func setContentId(value: Swift.String?)
  @_Concurrency.MainActor @preconcurrency public func setContentType(value: Swift.String?)
  @_Concurrency.MainActor @preconcurrency public func setVendor(value: Swift.String?)
  @_Concurrency.MainActor @preconcurrency public func setActor(value: Swift.String?)
  @_Concurrency.MainActor @preconcurrency public func setObjects(value: Swift.String?)
  @_Concurrency.MainActor @preconcurrency public func setKidsProtected(value: Swift.Bool?)
  @_Concurrency.MainActor @preconcurrency public func setAppVersion(value: Swift.String?)
  @_Concurrency.MainActor @preconcurrency public func setGenre(value: Swift.String?)
  @_Concurrency.MainActor @preconcurrency public func setState(value: Swift.String?)
  @_Concurrency.MainActor @preconcurrency public func setCountry(value: Swift.String?)
  @_Concurrency.MainActor @preconcurrency public func setPincode(value: Swift.String?)
  @_Concurrency.MainActor @preconcurrency public func setPlacementName(value: Swift.String?)
}
public protocol JioLogPreText {
  var preTextTAG: Swift.String { get set }
}
public protocol RILEventProtocol : Swift.Decodable, Swift.Encodable {
  var eventID: Swift.Int { get set }
  var eventType: Swift.String? { get set }
  var time: Swift.String? { get set }
  var parameters: [Swift.String : Any]? { get set }
}
public struct CompressionLevel : Swift.RawRepresentable {
  public let rawValue: Swift.Int32
  public static let noCompression: JioAdsFramework.CompressionLevel
  public static let bestSpeed: JioAdsFramework.CompressionLevel
  public static let bestCompression: JioAdsFramework.CompressionLevel
  public static let defaultCompression: JioAdsFramework.CompressionLevel
  public init(rawValue: Swift.Int32)
  public init(_ rawValue: Swift.Int32)
  public typealias RawValue = Swift.Int32
}
public struct GzipError : Swift.Error {
  public enum Kind : Swift.Equatable {
    case stream
    case data
    case memory
    case buffer
    case version
    case unknown(code: Swift.Int)
    public static func == (a: JioAdsFramework.GzipError.Kind, b: JioAdsFramework.GzipError.Kind) -> Swift.Bool
  }
  public let kind: JioAdsFramework.GzipError.Kind
  public let message: Swift.String
  public var localizedDescription: Swift.String {
    get
  }
}
extension Foundation.Data {
  public var isGzipped: Swift.Bool {
    get
  }
  public func gzipped(level: JioAdsFramework.CompressionLevel = .defaultCompression) throws -> Foundation.Data
  public func gunzipped() throws -> Foundation.Data
}
public protocol AdEventTracker {
  func trackImpression()
  func trackViewableImpression()
  func trackClick(isLoadCustomAd: Swift.Bool?)
  func trackError(error: Swift.String?)
  func trackStart()
  func trackFirstQuartile()
  func trackMidQuartile()
  func trackThirdQuartile()
  func trackCompleteQuartile()
  func trackMute()
  func trackUnmute()
  func trackPause()
  func trackResume()
  func trackSkip()
  func trackFullscreen()
}
public struct RILCartViewEvent : JioAdsFramework.RILEventProtocol, Swift.Codable {
  public var eventType: Swift.String?
  public var eventID: Swift.Int
  public var time: Swift.String?
  public var parameters: [Swift.String : Any]?
  public var startTime: Swift.String?, endTime: Swift.String?
  public var productDetails: JioAdsFramework.RILProductDetails?
  public init(startTime: Swift.String?, endTime: Swift.String?, productDetails: JioAdsFramework.RILProductDetails, parameters: [Swift.String : Any]? = nil)
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
public struct AdMeta {
}
public enum JioPlaybackStateEvent : Swift.String {
  case contentPauseRequested
  case contentResumeRequested
  case adClicked
  public init?(rawValue: Swift.String)
  public typealias RawValue = Swift.String
  public var rawValue: Swift.String {
    get
  }
}
public class JioVmapInfo {
  public init(WithID vmapId: Swift.String, videoLength: Swift.String?, cuePoints: Swift.String?, packageName: Swift.String?, adContainer: UIKit.UIView, prepareThreshold: Swift.Int? = nil, jioAdTargetting: JioAdsFramework.JioAdTargetting? = nil)
  public init(vmapUrl: Swift.String, adContainer: UIKit.UIView, prepareThreshold: Swift.Int? = nil, prerollId: Swift.String, midRollId: Swift.String, postRollId: Swift.String, jioAdTargetting: JioAdsFramework.JioAdTargetting? = nil)
  @objc deinit
}
extension JioAdsFramework.JioVmapInfo : Swift.CustomDebugStringConvertible {
  public var debugDescription: Swift.String {
    get
  }
}
public enum JioMediaType : Swift.String {
  case lBandAd
  case squeezeUpAd
  case overlayAd
  case none
  public init?(rawValue: Swift.String)
  public typealias RawValue = Swift.String
  public var rawValue: Swift.String {
    get
  }
}
public struct RILEvent : JioAdsFramework.RILEventProtocol, Swift.Codable {
  public var eventType: Swift.String?
  public var eventID: Swift.Int
  public var time: Swift.String?
  public var parameters: [Swift.String : Any]?
  public init(eventId: Swift.Int, eventType: Swift.String?, time: Swift.String?, parameters: [Swift.String : Any]? = nil)
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
public struct MediationModelElement : Swift.Codable {
  public let appid: Swift.String?
  public let networkName: Swift.String?
  public let type: Swift.String?
  public let code: JioAdsFramework.MediationTypeCode?
  public let adunitid: Swift.String?
  public let adtagcode: Swift.String?
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
public enum MediationTypeCode : Swift.Int, Swift.Codable {
  case display
  case video
  case native
  case interstitial
  case rewarded
  case rewardedInterstitial
  case iMAVideo
  case audio
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
public enum CustomAdCategory {
  case video
  case native
  public static func == (a: JioAdsFramework.CustomAdCategory, b: JioAdsFramework.CustomAdCategory) -> Swift.Bool
  public func hash(into hasher: inout Swift.Hasher)
  public var hashValue: Swift.Int {
    get
  }
}
@_hasMissingDesignatedInitializers public class CustomVideoTracker : Swift.Codable {
  public var start: [Swift.String]? {
    get
  }
  public var firstQuartile: [Swift.String]? {
    get
  }
  public var midpoint: [Swift.String]? {
    get
  }
  public var thirdQuartile: [Swift.String]? {
    get
  }
  public var complete: [Swift.String]? {
    get
  }
  public var close: [Swift.String]? {
    get
  }
  public var mute: [Swift.String]? {
    get
  }
  public var unmute: [Swift.String]? {
    get
  }
  public var pause: [Swift.String]? {
    get
  }
  public var resume: [Swift.String]? {
    get
  }
  public var creativeView: [Swift.String]? {
    get
  }
  public var fullscreen: [Swift.String]? {
    get
  }
  public var skip: [Swift.String]? {
    get
  }
  public var error: [Swift.String]? {
    get
  }
  @objc deinit
  public func encode(to encoder: any Swift.Encoder) throws
  required public init(from decoder: any Swift.Decoder) throws
}
public class VideoAd : Swift.Codable {
  public var adSystem: Swift.String? {
    get
  }
  public var title: Swift.String? {
    get
  }
  public var desc: Swift.String? {
    get
  }
  public var id: Swift.String? {
    get
  }
  public var skipOffset: Swift.UInt? {
    get
  }
  public var duration: Swift.UInt? {
    get
  }
  public var durationInSec: Swift.Int? {
    get
  }
  public var skipDelay: Swift.Int? {
    get
  }
  public var impressions: [Swift.String]? {
    get
  }
  public var viewableImpressions: [Swift.String]? {
    get
  }
  public var selectedMediaFile: Swift.String? {
    get
  }
  public var clickThroughUrls: [Swift.String]? {
    get
  }
  public var deeplinkUrl: Swift.String? {
    get
  }
  public var clickTrackingUrls: [Swift.String]? {
    get
  }
  public var errorUrls: [Swift.String]? {
    get
  }
  public var isLastVastAd: Swift.Bool {
    get
  }
  public var trackingEvents: JioAdsFramework.CustomVideoTracker? {
    get
  }
  public init()
  public var getAdSystem: Swift.String? {
    get
  }
  public var getTitle: Swift.String? {
    get
  }
  public var getDescription: Swift.String? {
    get
  }
  public var getId: Swift.String? {
    get
  }
  public var getSkipOffset: Swift.UInt? {
    get
  }
  public var getDuration: Swift.UInt? {
    get
  }
  public var getImpressionTrackers: [Swift.String]? {
    get
  }
  public var getViewableImpressionTrackers: [Swift.String]? {
    get
  }
  public var getMedia: Swift.String? {
    get
  }
  public var getClickThroughUrl: [Swift.String]? {
    get
  }
  public var getClickTrackers: [Swift.String]? {
    get
  }
  public var getErrorTrackers: [Swift.String]? {
    get
  }
  @objc deinit
  public func encode(to encoder: any Swift.Encoder) throws
  required public init(from decoder: any Swift.Decoder) throws
}
@_hasMissingDesignatedInitializers public class MediaFile {
  public var mediaURL: Swift.String?, type: Swift.String?, delivery: Swift.String?
  public var bitrate: Swift.Int?, height: Swift.Int?, width: Swift.Int?, minBitrate: Swift.Int?, maxBitrate: Swift.Int?
  @objc deinit
}
public struct RILPurchaseCompletedEvent : JioAdsFramework.RILEventProtocol, Swift.Codable {
  public var eventType: Swift.String?
  public var clickId: Swift.String?
  public var eventID: Swift.Int
  public var time: Swift.String?
  public var parameters: [Swift.String : Any]?
  public var transactionId: Swift.String?
  public var productDetails: JioAdsFramework.RILProductDetails?
  public init(transactionId: Swift.String, productDetails: JioAdsFramework.RILProductDetails, parameters: [Swift.String : Any]? = nil)
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
@objc public class JioAdError : ObjectiveC.NSObject {
  public enum RequestError : Swift.Int, Swift.Error {
    case badRequest
    case noConnection
    case timeOutError
    case parserError
    case nofill
    case adBlocked
    case renditionError
    case allowExtractionError
    case adPodPlayerTimOutError
    case sinlgeAdBreakErrorCode
    case unKnown
    case misMatchAdType
    case mandtoryParamMissing
    case mediationError
    case adConfigNotExist
    case notAbleToRenderBackupAd
    case vmapError
    public init?(rawValue: Swift.Int)
    public typealias RawValue = Swift.Int
    public var rawValue: Swift.Int {
      get
    }
  }
  public enum RequestErrorType : Swift.String {
    case errorWhileFetchingUID
    case timeoutWhileFetchingAd
    case wrongUxType
    case mandatoryParametersMissing
    case adRenditionError
    case parsingError
    case cacheAdFailed
    case errorWhileDownLoadingAd
    case adSpotdoesNotExist
    case adRequestNotAllowed
    case adspotIsNotLinkedToAppOrWebiste
    case requestFromInvalidORrestrictedOrigin
    case uaIsInvalidOrDoesNotExist
    case osIsInvalidOrDoesNotExist
    case adPodTimeoutError
    case playerPreparationFailed
    case userDailyImpressionLimitReached
    case userLifeTimeImpressionLimitReached
    case userClickDailyLimitReached
    case userClickLifeTimeLimitReached
    case userCompletedViewDailyLimitReached
    case userCompletedViewLifetimeLimitReached
    case userMinuteWiseImpressionLimitReached
    case userHourlyImpressionLimitReached
    case userClickMinutewiseLimitReached
    case userClickHourlyLimitReached
    case userCompletedViewMinutewiseLimitReached
    case userCompletedViewHourlyLimitReached
    case invalidAdRequestParameters
    case masterConfigDownloadFailed
    case errorDuringCTAClick
    case mediationError
    case notAbleToRenderBackupAd
    case vmapError
    case emptyVmap
    case videoPlayerError
    case vastError
    case vastRenditionError
    case emptyVastTags
    case noMediaUrl
    case noVastTagUris
    case vmapRequestProcessError
    case vmapResponseProcessError
    case vmapParsingError
    case serverError
    case failedToRequestAds
    case internalError
    case invalidArguments
    case unknownAdResponse
    case unknownError
    case vastAssetNotFound
    case vastEmptyResponse
    case vastLoadTimeout
    case vastMalformedResponse
    case vastMediaLoadTimeout
    case vastTooManyRedirects
    case streamViewUrlCannotBeEmpty
    case headersCannotbeNullForVODstreamType
    case malformedURLObserved
    case socketTimeoutWhileNetworkCall
    case unableToConnectWhileNetworkCall
    case sslPeerUnverifiedExceptionWhileNetworkCall
    case ioExceptionWhileNetworkCall
    case unknownExceptionWhileNetworkCall
    case wrapperAdFailed
    case spotAdsFailure
    case jSExceptionduringtargeting
    case loadAdFailed
    case networkError
    case internetisNotAvailable
    case cohortFail
    public init?(rawValue: Swift.String)
    public typealias RawValue = Swift.String
    public var rawValue: Swift.String {
      get
    }
  }
  public enum VASTError : Swift.Int, Swift.Error {
    case vastXmlParsingError
    case vastSchemaValidationError
    case vastResponseVersionNotSupportedError
    case wrapperError
    case vastRedirectTimeoutReachedError
    case wrapperLimitReached
    case emptyvastResponseError
    case mediaFileNotFoundError
    case mediaDownloadingTimeoutError
    case mediaFileDisplayError
    case undefinedError
    case linearNonLinear
    public init?(rawValue: Swift.Int)
    public typealias RawValue = Swift.Int
    public var rawValue: Swift.Int {
      get
    }
  }
  public enum JioVmapAdsError : Swift.Int, Swift.Error {
    case emptyVmap
    case videoPlayerError
    case vastError
    case vastRenditionError
    case emptyVastTags
    case noMediaUrl
    case noVastTagUris
    case vmapRequestProcessError
    case vmapResponseProcessError
    case vmapParsingError
    case serverError
    case failedToRequestAds
    case internalError
    case invalidArguments
    case unknownAdResponse
    case unknownError
    case vastAssetNotFound
    case vastEmptyResponse
    case vastLoadTimeout
    case vastMalformedResponse
    case vastMediaLoadTimeout
    case vastTooManyRedirects
    public init?(rawValue: Swift.Int)
    public typealias RawValue = Swift.Int
    public var rawValue: Swift.Int {
      get
    }
  }
  public var title: Swift.String
  public var code: Swift.Int
  public var errorDescription: Swift.String {
    get
  }
  public init(title: Swift.String?, description: Swift.String, code: Swift.Int)
  @objc override dynamic public var description: Swift.String {
    @objc get
  }
  @objc deinit
}
extension JioAdsFramework.JioAdError {
  convenience public init(requestError: JioAdsFramework.JioAdError.RequestError, title: Swift.String? = nil, desc: Swift.String? = nil)
  public func updateDescription(requestError: JioAdsFramework.JioAdError.RequestError)
}
extension JioAdsFramework.JioAdError {
  convenience public init(vastError: JioAdsFramework.JioAdError.VASTError)
  public func updateDescription(vastError: JioAdsFramework.JioAdError.VASTError)
}
extension JioAdsFramework.JioAdError {
  convenience public init(modelFetcherError: JioAdsFramework.JioVastModelFetcherError)
  public func updateDescription(modelFetcherError: JioAdsFramework.JioVastModelFetcherError)
}
extension JioAdsFramework.JioAdError {
  convenience public init(vmapError: JioAdsFramework.JioAdError.JioVmapAdsError)
  public func updateDescription(vmapError: JioAdsFramework.JioAdError.JioVmapAdsError)
}
@_inheritsConvenienceInitializers @objc public class RILEventManager : ObjectiveC.NSObject {
  @objc public static let shared: JioAdsFramework.RILEventManager
  public func initializeRetargettingSDK()
  public func trackEvent(_ event: any JioAdsFramework.RILEventProtocol, params: [Swift.String : Any] = [:], completion: (([[Swift.String : Any]]?) -> Swift.Void)? = nil)
  public func availableProductIds(productIdList: [Swift.String])
  public func setEnvironment(environment: JioAdsFramework.Environments)
  @objc override dynamic public init()
  @objc deinit
}
extension JioAdsFramework.RILEventManager {
  public func fireTrackerAPI(trackerObject: Swift.String, trackerType: JioAdsFramework.TrackerType, productId: Swift.String, adSize: Swift.String, productOptions: Swift.String)
  public func fireImpTracker(trackingURL: Swift.String, productId: Swift.String, clickId: Swift.String, adSize: Swift.String)
  public func fireConversionApi(clickId: Swift.String, level: Swift.Int, options: JioAdsFramework.ConversionOptions)
  public func fireDirectConversionApi(clickId: Swift.String, level: Swift.Int, options: JioAdsFramework.ConversionOptions)
  public func getQueryStringParam(url: Swift.String, param: Swift.String) -> Swift.String?
}
public struct ConversionOptions {
  public var revenue: Swift.String?
  public var currency: Swift.String?
  public var prdrevenue: Swift.String?
  public var prdcount: Swift.Int?
  public var prdprice: Swift.String?
  public var prdid: Swift.String?
  public init(revenue: Swift.String, currency: Swift.String, prdrevenue: Swift.String, prdcount: Swift.Int, prdprice: Swift.String, prdid: Swift.String)
}
@objc @_inheritsConvenienceInitializers @_hasMissingDesignatedInitializers @_Concurrency.MainActor @preconcurrency final public class DefaultVideoLayout : UIKit.UIView {
  @objc deinit
}
public enum AdType : Swift.Codable {
  case infeed, nativeContentStream, instreamVideo, customNative, interstitial, dynamicDisplay, instreamAudio
  public static func == (a: JioAdsFramework.AdType, b: JioAdsFramework.AdType) -> Swift.Bool
  public func hash(into hasher: inout Swift.Hasher)
  public func encode(to encoder: any Swift.Encoder) throws
  public var hashValue: Swift.Int {
    get
  }
  public init(from decoder: any Swift.Decoder) throws
}
extension JioAdsFramework.AdType : Swift.CaseIterable {
  public typealias AllCases = [JioAdsFramework.AdType]
  nonisolated public static var allCases: [JioAdsFramework.AdType] {
    get
  }
}
public enum JioAdState : Swift.Int {
  case notRequested
  case requested
  case received
  case prepared
  case failed
  case destroyed
  case started
  case ended
  case closed
  case expanded
  case collapsed
  case interacted
  case blocked
  public func description() -> Swift.String
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
public enum DynamicDisplaySize : Swift.Codable {
  case size300x50
  case size300x250
  case size320x50
  case size320x100
  case size728x90
  case size728x250
  public func description() -> Swift.String
  public func getWidthHeight() -> (CoreFoundation.CGFloat, CoreFoundation.CGFloat)
  public static func == (a: JioAdsFramework.DynamicDisplaySize, b: JioAdsFramework.DynamicDisplaySize) -> Swift.Bool
  public func hash(into hasher: inout Swift.Hasher)
  public func encode(to encoder: any Swift.Encoder) throws
  public var hashValue: Swift.Int {
    get
  }
  public init(from decoder: any Swift.Decoder) throws
}
extension JioAdsFramework.DynamicDisplaySize : Swift.CaseIterable {
  public typealias AllCases = [JioAdsFramework.DynamicDisplaySize]
  nonisolated public static var allCases: [JioAdsFramework.DynamicDisplaySize] {
    get
  }
}
public enum CompanionAdSize {
  case size300x250
  case size240x260
  case size320x184
  public static func == (a: JioAdsFramework.CompanionAdSize, b: JioAdsFramework.CompanionAdSize) -> Swift.Bool
  public func hash(into hasher: inout Swift.Hasher)
  public var hashValue: Swift.Int {
    get
  }
}
public enum TrackerType {
  case impression
  case viewability
  case click
  public static func == (a: JioAdsFramework.TrackerType, b: JioAdsFramework.TrackerType) -> Swift.Bool
  public func hash(into hasher: inout Swift.Hasher)
  public var hashValue: Swift.Int {
    get
  }
}
public enum AdCategory : Swift.Int {
  case nativeInterstitial
  case videoInterstitial
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
public enum ExpandCollapse {
  case expand
  case collapse
  public static func == (a: JioAdsFramework.ExpandCollapse, b: JioAdsFramework.ExpandCollapse) -> Swift.Bool
  public func hash(into hasher: inout Swift.Hasher)
  public var hashValue: Swift.Int {
    get
  }
}
public enum MediaPlayBack : Swift.Int {
  case NOTRUN
  case RESUME
  case PAUSE
  case MUTE
  case UNMUTE
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
public enum AdRequestType : Swift.String {
  case adRequest
  case handleClick
  case fetchBPID
  case masterConfig
  case instruction
  case firstQuartile
  case midQuartile
  case thirdQuartile
  case completeQuartile
  case resumeAd
  case pauseAd
  case startEvent
  case skipEvent
  case fullscreenEvent
  case cdnLoggingAdRequest
  case rewardRequest
  case muteEvent
  case unmuteEvent
  case closeEvent
  case companionCloseEvent
  case errorEvent
  case impression
  case viewablility
  case click
  case prefetchAds
  case newUserType
  case adOpportunity
  public init?(rawValue: Swift.String)
  public typealias RawValue = Swift.String
  public var rawValue: Swift.String {
    get
  }
}
public enum LogLevel : Swift.Int {
  case none
  case debug
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
public enum MediaType : Swift.Int {
  case none
  case image
  case video
  case all
  public func description() -> Swift.String
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
public enum CacheMode : Swift.Int {
  case none
  case image
  case video
  case all
  public func description() -> Swift.String
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
extension JioAdsFramework.CacheMode : Swift.CaseIterable {
  public typealias AllCases = [JioAdsFramework.CacheMode]
  nonisolated public static var allCases: [JioAdsFramework.CacheMode] {
    get
  }
}
public enum VideoAdControlTags : Swift.Int {
  case skipButton, progressBar, visitAdvertiser, playPauseButton, muteUnmuteButton, expandCollapseBtn, adDurationLbl, mediaView, adThumbNailImage, adcounterLbl, titleLabel, descriptionLabel, iconImage
  case closeButton, miniPlayer
  case skipLabel
  case adBadge
  case ctaButtonView
  case videoLoader
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
public enum NativeAdTags : Swift.Int {
  case title, desc, iconImageView, mainImageView, customImageView, mediaView, floatRatingView, ctaButton, sponsored, sdkMainImageView, desc2, rating, downloads, price, salePrice, phone, address, displayURL, likes, campaignId, ctaButtonView
  case playPauseButton, muteUnmuteButton, expandCollapseBtn, adDurationLbl
  case closeButton, secondaryCtaButton, progressBar, replayBtn
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
public enum CarouselAdTags : Swift.Int {
  case carouselContainer
  case carousalImage, carousalTitle, carousalDescription
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
public enum JioExpandCollapseAdTag : Swift.Int {
  case expandCollapseContainer
  case expandCollapseBtn
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
public enum LbandSqueeseOverLayTags : Swift.Int {
  case lbandSqueeseOverLayContainer
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
public enum Severity : Swift.String {
  case all
  case high
  case med
  case low
  public func description() -> Swift.String
  public init?(rawValue: Swift.String)
  public typealias RawValue = Swift.String
  public var rawValue: Swift.String {
    get
  }
}
public enum OrientationType {
  case orientationDefault
  case orientationPortrait
  case orientationLandscape
  public static func == (a: JioAdsFramework.OrientationType, b: JioAdsFramework.OrientationType) -> Swift.Bool
  public func hash(into hasher: inout Swift.Hasher)
  public var hashValue: Swift.Int {
    get
  }
}
public enum AdPodVariant {
  case none
  case defaultAdPod
  case infiniteAdDutationWithLoop
  public static func == (a: JioAdsFramework.AdPodVariant, b: JioAdsFramework.AdPodVariant) -> Swift.Bool
  public func hash(into hasher: inout Swift.Hasher)
  public var hashValue: Swift.Int {
    get
  }
}
extension JioAdsFramework.AdPodVariant : Swift.CaseIterable {
  public typealias AllCases = [JioAdsFramework.AdPodVariant]
  nonisolated public static var allCases: [JioAdsFramework.AdPodVariant] {
    get
  }
}
public enum EQuartileEventType : Swift.Int {
  case start
  case first
  case mid
  case third
  case complete
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
public enum EAppType : Swift.Int {
  case unknown
  case jioVoot
  case jioTV
  case ajio
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
@_hasMissingDesignatedInitializers public class JioAd {
  public func getAdCategory() -> JioAdsFramework.CustomAdCategory?
  public func getAdId() -> Swift.String?
  public func getMetadata()
  public func getVideoAd() -> JioAdsFramework.VideoAd?
  public func getNativeAd() -> JioAdsFramework.NativeAd?
  public func getAdEventTracker() -> (any JioAdsFramework.AdEventTracker)?
  @objc deinit
}
public struct RILProductListEvent : Swift.Codable {
  public var parameters: [Swift.String : Any]?
  public var segment: Swift.String?
  public var id: Swift.String?
  public var price: Swift.Int?, quantity: Swift.Int?
  public var brickname: Swift.String?
  public var vertical: Swift.String?
  public var sku: Swift.String?
  public init(segment: Swift.String, id: Swift.String, price: Swift.Int, quantity: Swift.Int, brickname: Swift.String, vertical: Swift.String, sku: Swift.String? = "")
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
public class JioSpotAdModel {
  public var id: Swift.Double
  public var cid: Swift.String
  public var startDate: Foundation.Date
  public var duration: Swift.Double?
  public var isStart: Swift.Bool
  public init()
  @objc deinit
}
public class AdBreakDateRange {
  public var uniqueId: Swift.String?
  public var startDate: Foundation.Date
  public var duration: Swift.Double?
  public var adpodSize: Swift.Int
  public init(startDate: Foundation.Date)
  @objc deinit
}
public enum StreamType {
  case vod
  case live
  public static func == (a: JioAdsFramework.StreamType, b: JioAdsFramework.StreamType) -> Swift.Bool
  public func hash(into hasher: inout Swift.Hasher)
  public var hashValue: Swift.Int {
    get
  }
}
public enum PlayBackType {
  case sessionUrl
  case directPlayBackUrl
  public static func == (a: JioAdsFramework.PlayBackType, b: JioAdsFramework.PlayBackType) -> Swift.Bool
  public func hash(into hasher: inout Swift.Hasher)
  public var hashValue: Swift.Int {
    get
  }
}
public class JioSSAIManager {
  weak public var spotAdCompanionDelegate: (any JioAdsFramework.JioSpotAdCampanionProtocol)?
  weak public var delegate: (any JioAdsFramework.JioSSAIAdEventsProtocol)?
  public var customMetaData: [Swift.String : Any]?
  public static let shared: JioAdsFramework.JioSSAIManager
  public init()
  public func stop()
  public func setStreamType(_ streamType: JioAdsFramework.StreamType)
  public func setPlayBackType(_ playBackType: JioAdsFramework.PlayBackType)
  public func initializeJioSSAIAdManager(viewUrl: Swift.String, metaData: [Swift.String : Any]?, requestTimeout: Swift.Double?, isJioAds: Swift.Bool = true)
  public func setPlayer(avPlayer: AVFoundation.AVPlayer)
  public func handleAdClick(isPrimaryCTABtn: Swift.Bool)
  public func handleAdClick(adModel: JioAdsFramework.JioAdDataModel)
  public func setMetadataGroup(metaData: [AVFoundation.AVDateRangeMetadataGroup], time: Foundation.Date? = nil) -> Swift.Bool
  public func playerTimeUpdated(time: Foundation.Date?)
  @objc deinit
}
extension JioAdsFramework.JioSSAIManager {
  public func handleSpotAdClick(cid: Swift.String)
}
extension JioAdsFramework.JioSSAIManager {
  public func setCompanions(companions: [JioAdsFramework.JioAdCompanion], delegate: any JioAdsFramework.JioCompanionAdviewProtocol)
}
public enum ReachabilityError : Swift.Error {
  case failedToCreateWithAddress(Darwin.sockaddr, Swift.Int32)
  case failedToCreateWithHostname(Swift.String, Swift.Int32)
  case unableToSetCallback(Swift.Int32)
  case unableToSetDispatchQueue(Swift.Int32)
  case unableToGetFlags(Swift.Int32)
}
@available(*, unavailable, renamed: "Notification.Name.reachabilityChanged")
public let reachabilityChangedNotification: Foundation.NSNotification.Name
extension Foundation.NSNotification.Name {
  public static let reachabilityChanged: Foundation.Notification.Name
}
public protocol JioAdsVMAPBuilderProtocol : AnyObject {
  func onVmapDataPrepared(vmapData: Swift.String?)
  func onVmapDataFailedToload(error: JioAdsFramework.JioAdError?)
}
public struct JioAdsVMAPInfo {
  public var preRollAdspot: Swift.String?
  public var midRollAdspot: Swift.String?
  public var postRollAdspot: Swift.String?
  public var metadata: [Swift.String : Any]?
  public var cuePoints: [Swift.Int]?
  public init()
  public func isPreRollAdspotEmpty() -> Swift.Bool
  public func isMidRollAdspotEmpty() -> Swift.Bool
  public func isPostRollAdspotEmpty() -> Swift.Bool
  public func isMetadataEmpty() -> Swift.Bool
  public func isCuePointsEmpty() -> Swift.Bool
}
@objc @_inheritsConvenienceInitializers public class JioAdsVMAPBuilder : ObjectiveC.NSObject, JioAdsFramework.JIOAdViewProtocol {
  public func getVmapDataFor(vmapInfo: JioAdsFramework.JioAdsVMAPInfo, delegate: any JioAdsFramework.JioAdsVMAPBuilderProtocol)
  public func destroyVMAP()
  public func notifyPrerollPlayed()
  @objc override dynamic public init()
  @objc deinit
}
extension JioAdsFramework.JioAdsVMAPBuilder {
  public func onAdPrepared(adView: JioAdsFramework.JioAdView)
  public func onAdFailedToLoad(adView: JioAdsFramework.JioAdView, error: JioAdsFramework.JioAdError)
}
extension JioAdsFramework.JioAdsVMAPBuilder {
  public func onAdReceived(adView: JioAdsFramework.JioAdView)
  public func onAdRender(adView: JioAdsFramework.JioAdView)
  public func onAdClicked(adView: JioAdsFramework.JioAdView)
  public func onAdRefresh(adView: JioAdsFramework.JioAdView)
  public func onAdMediaEnd(adView: JioAdsFramework.JioAdView)
  public func onAdClosed(adView: JioAdsFramework.JioAdView, isVideoCompleted: Swift.Bool, isEligibleForReward: Swift.Bool)
  public func onAdMediaStart(adView: JioAdsFramework.JioAdView)
  public func onAdSkippable(adView: JioAdsFramework.JioAdView)
  public func onAdMediaExpand(adView: JioAdsFramework.JioAdView)
  public func onAdMediaCollapse(adView: JioAdsFramework.JioAdView)
  public func onMediaPlaybackChange(adView: JioAdsFramework.JioAdView, mediaPlayBack: JioAdsFramework.MediaPlayBack)
  public func onAdChange(adView: JioAdsFramework.JioAdView, trackNo: Swift.Int)
}
public struct RILProductViewEvent : JioAdsFramework.RILEventProtocol, Swift.Codable {
  public var eventType: Swift.String?
  public var eventID: Swift.Int
  public var time: Swift.String?
  public var parameters: [Swift.String : Any]?
  public var startTime: Swift.String?
  public var endTime: Swift.String?
  public var productdetails: JioAdsFramework.RILProductDetails?
  public init(startTime: Swift.String?, endTime: Swift.String?, parameters: [Swift.String : Any]? = nil, productDetails: JioAdsFramework.RILProductDetails)
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
@objc @_inheritsConvenienceInitializers @_hasMissingDesignatedInitializers @_Concurrency.MainActor @preconcurrency final public class JioInterstitialVideoLayout : UIKit.UIView {
  @objc deinit
}
public protocol JioLbandAdsLoaderDelegate : AnyObject {
  func onAdMedia(event: JioAdsFramework.JioPlaybackStateEvent)
  func onAdMediaStart(offsetPercentage: JioAdsFramework.JioOffsetPercentage, mediaType: JioAdsFramework.JioMediaType)
  func onAdMediaEnd(mediaType: JioAdsFramework.JioMediaType)
  func onAdMediaError(error: JioAdsFramework.JioAdError)
}
public struct RILPageViewEvent : JioAdsFramework.RILEventProtocol, Swift.Codable {
  public var eventType: Swift.String?
  public var eventID: Swift.Int
  public var time: Swift.String?
  public var parameters: [Swift.String : Any]?
  public var pageName: Swift.String?
  public var startTime: Swift.String?, endTime: Swift.String?
  public init(pageName: Swift.String, startTime: Swift.String?, endTime: Swift.String?, parameters: [Swift.String : Any]? = nil)
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
public struct RILCustomDataEvent : JioAdsFramework.RILEventProtocol, Swift.Codable {
  public var eventType: Swift.String?
  public var time: Swift.String?
  public var eventID: Swift.Int
  public var parameters: [Swift.String : Any]?
  public var customData: [[Swift.String : Any]]?
  public init(parameters: [Swift.String : Any]? = nil, customData: [[Swift.String : Any]])
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
public enum JSON {
  case double(Swift.Double)
  case integer(Swift.Int)
  case string(Swift.String)
  case boolean(Swift.Bool)
  case null
  indirect case array([JioAdsFramework.JSON])
  indirect case object([Swift.String : JioAdsFramework.JSON])
}
extension JioAdsFramework.JSON : Swift.Encodable {
  public func encode(to encoder: any Swift.Encoder) throws
}
extension JioAdsFramework.JSON : Swift.Decodable {
  public init(from decoder: any Swift.Decoder) throws
}
public struct JioOffsetPercentage {
  public var top: CoreFoundation.CGFloat?
  public var right: CoreFoundation.CGFloat?
  public var leading: CoreFoundation.CGFloat?
  public var bottom: CoreFoundation.CGFloat?
  public init(top: CoreFoundation.CGFloat, right: CoreFoundation.CGFloat, bottom: CoreFoundation.CGFloat, leading: CoreFoundation.CGFloat)
}
@_hasMissingDesignatedInitializers public class NativeAd {
  public var iConImageURL: Swift.String? {
    get
  }
  public var title: Swift.String? {
    get
  }
  public var sponsored: Swift.String? {
    get
  }
  public var desc1: Swift.String? {
    get
  }
  public var mainImageURL: Swift.String? {
    get
  }
  public var ctaText: Swift.String? {
    get
  }
  public var desc2: Swift.String? {
    get
  }
  public var rating: Swift.Double? {
    get
  }
  public var downloads: Swift.String? {
    get
  }
  public var price: Swift.String? {
    get
  }
  public var salePrice: Swift.String? {
    get
  }
  public var phone: Swift.String? {
    get
  }
  public var address: Swift.String? {
    get
  }
  public var displayURL: Swift.String? {
    get
  }
  public var likes: Swift.String? {
    get
  }
  public var customImagesUrlDict: [Swift.String : Swift.String]? {
    get
  }
  public var impressionUrlList: [Swift.String]? {
    get
  }
  public var clickTrackerUrlList: [Swift.String]? {
    get
  }
  public var linkURL: Swift.String? {
    get
  }
  public var linkFallbackURL: Swift.String? {
    get
  }
  public var brandUrl: Swift.String? {
    get
  }
  public var objective: Swift.String? {
    get
  }
  public var campaignID: Swift.String? {
    get
  }
  public var mediumImage: Swift.String? {
    get
  }
  public var video: Swift.String? {
    get
  }
  public var videoData: JioAdsFramework.VideoAd? {
    get
  }
  public var isNativeVideoAd: Swift.Bool {
    get
  }
  public var getIconImage: Swift.String? {
    get
  }
  public var getTitle: Swift.String? {
    get
  }
  public var getSponsored: Swift.String? {
    get
  }
  public var getDescription: Swift.String? {
    get
  }
  public var getMainImage: Swift.String? {
    get
  }
  public var getCtaText: Swift.String? {
    get
  }
  public var getDescription2: Swift.String? {
    get
  }
  public var getRating: Swift.String? {
    get
  }
  public var getPrice: Swift.String? {
    get
  }
  public var getSalePrice: Swift.String? {
    get
  }
  public var getPhone: Swift.String? {
    get
  }
  public var getAddress: Swift.String? {
    get
  }
  public var getDisplayUrl: Swift.String? {
    get
  }
  public var getLikes: Swift.String? {
    get
  }
  public var getObjective: Swift.String? {
    get
  }
  public var getCampaignId: Swift.String? {
    get
  }
  public var getVideo: Swift.String? {
    get
  }
  public var getCustomImages: [Swift.String : Swift.String] {
    get
  }
  public var getCustomImage: Swift.String {
    get
  }
  public var getVideoData: JioAdsFramework.VideoAd? {
    get
  }
  public var getFallbackLink: Swift.String? {
    get
  }
  public var getCtaUrl: Swift.String? {
    get
  }
  public var getImpressionTrackers: [Swift.String]? {
    get
  }
  public var getClickTrackers: [Swift.String]? {
    get
  }
  @objc deinit
}
public protocol JioAdRenderer {
  init(adSpotId: Swift.String?)
  func prepare<T>(delegate: any JioAdsFramework.JioAdRendererDelegate, adMarkup: T)
  func load(publisherContainer: UIKit.UIView, customContainer: UIKit.UIView?)
  func append<T>(adMarkup: T)
  func addListener<T>(listener: T)
  func getRendererType() -> Swift.String
  func destroy()
}
public enum JioHttpClientError : Swift.Error {
  case emptyClientModel
  case badURL
  case unknownError
  case errorHttpResponse
  case notFound
  case unknownResponseCode
  case invalidData
  case responseCountZero
  case serverError
  case requestTimeout
  case encodingFailed
  public static func == (a: JioAdsFramework.JioHttpClientError, b: JioAdsFramework.JioHttpClientError) -> Swift.Bool
  public func hash(into hasher: inout Swift.Hasher)
  public var hashValue: Swift.Int {
    get
  }
}
extension JioAdsFramework.JioVastModelFetcherError : Swift.Equatable {}
extension JioAdsFramework.JioVastModelFetcherError : Swift.Hashable {}
extension JioAdsFramework.JioVastModelFetcherError : Swift.RawRepresentable {}
extension JioAdsFramework.JioHttpMethod : Swift.Equatable {}
extension JioAdsFramework.JioHttpMethod : Swift.Hashable {}
extension JioAdsFramework.JioHttpMethod : Swift.RawRepresentable {}
extension JioAdsFramework.SdkDeviceType : Swift.Equatable {}
extension JioAdsFramework.SdkDeviceType : Swift.Hashable {}
extension JioAdsFramework.SdkDeviceType : Swift.RawRepresentable {}
extension JioAdsFramework.ViewPortStatus : Swift.Equatable {}
extension JioAdsFramework.ViewPortStatus : Swift.Hashable {}
extension JioAdsFramework.ViewPortStatus : Swift.RawRepresentable {}
extension JioAdsFramework.JioCTAaction : Swift.Equatable {}
extension JioAdsFramework.JioCTAaction : Swift.Hashable {}
extension JioAdsFramework.JioCarousalCompanionEvent : Swift.Equatable {}
extension JioAdsFramework.JioCarousalCompanionEvent : Swift.Hashable {}
extension JioAdsFramework.JioAdTargetting.KidsProtected : Swift.Equatable {}
extension JioAdsFramework.JioAdTargetting.KidsProtected : Swift.Hashable {}
extension JioAdsFramework.JioAdTargetting.Gender : Swift.Equatable {}
extension JioAdsFramework.JioAdTargetting.Gender : Swift.Hashable {}
extension JioAdsFramework.APIEnvironment : Swift.Equatable {}
extension JioAdsFramework.APIEnvironment : Swift.Hashable {}
extension JioAdsFramework.AdClickOption : Swift.Equatable {}
extension JioAdsFramework.AdClickOption : Swift.Hashable {}
extension JioAdsFramework.AdClickOption : Swift.RawRepresentable {}
extension JioAdsFramework.AdEventType : Swift.Equatable {}
extension JioAdsFramework.AdEventType : Swift.Hashable {}
extension JioAdsFramework.AdEventType : Swift.RawRepresentable {}
extension JioAdsFramework.Environments : Swift.Equatable {}
extension JioAdsFramework.Environments : Swift.Hashable {}
extension JioAdsFramework.VastTrackingEvents : Swift.Equatable {}
extension JioAdsFramework.VastTrackingEvents : Swift.Hashable {}
extension JioAdsFramework.VastTrackingEvents : Swift.RawRepresentable {}
extension JioAdsFramework.LogTrackingEvents : Swift.Equatable {}
extension JioAdsFramework.LogTrackingEvents : Swift.Hashable {}
extension JioAdsFramework.LogTrackingEvents : Swift.RawRepresentable {}
extension JioAdsFramework.JioPlaybackStateEvent : Swift.Equatable {}
extension JioAdsFramework.JioPlaybackStateEvent : Swift.Hashable {}
extension JioAdsFramework.JioPlaybackStateEvent : Swift.RawRepresentable {}
extension JioAdsFramework.JioMediaType : Swift.Equatable {}
extension JioAdsFramework.JioMediaType : Swift.Hashable {}
extension JioAdsFramework.JioMediaType : Swift.RawRepresentable {}
extension JioAdsFramework.MediationTypeCode : Swift.Equatable {}
extension JioAdsFramework.MediationTypeCode : Swift.Hashable {}
extension JioAdsFramework.MediationTypeCode : Swift.RawRepresentable {}
extension JioAdsFramework.CustomAdCategory : Swift.Equatable {}
extension JioAdsFramework.CustomAdCategory : Swift.Hashable {}
extension JioAdsFramework.JioAdError.RequestError : Swift.Equatable {}
extension JioAdsFramework.JioAdError.RequestError : Swift.Hashable {}
extension JioAdsFramework.JioAdError.RequestError : Swift.RawRepresentable {}
extension JioAdsFramework.JioAdError.RequestErrorType : Swift.Equatable {}
extension JioAdsFramework.JioAdError.RequestErrorType : Swift.Hashable {}
extension JioAdsFramework.JioAdError.RequestErrorType : Swift.RawRepresentable {}
extension JioAdsFramework.JioAdError.VASTError : Swift.Equatable {}
extension JioAdsFramework.JioAdError.VASTError : Swift.Hashable {}
extension JioAdsFramework.JioAdError.VASTError : Swift.RawRepresentable {}
extension JioAdsFramework.JioAdError.JioVmapAdsError : Swift.Equatable {}
extension JioAdsFramework.JioAdError.JioVmapAdsError : Swift.Hashable {}
extension JioAdsFramework.JioAdError.JioVmapAdsError : Swift.RawRepresentable {}
extension JioAdsFramework.AdType : Swift.Equatable {}
extension JioAdsFramework.AdType : Swift.Hashable {}
extension JioAdsFramework.JioAdState : Swift.Equatable {}
extension JioAdsFramework.JioAdState : Swift.Hashable {}
extension JioAdsFramework.JioAdState : Swift.RawRepresentable {}
extension JioAdsFramework.DynamicDisplaySize : Swift.Equatable {}
extension JioAdsFramework.DynamicDisplaySize : Swift.Hashable {}
extension JioAdsFramework.CompanionAdSize : Swift.Equatable {}
extension JioAdsFramework.CompanionAdSize : Swift.Hashable {}
extension JioAdsFramework.TrackerType : Swift.Equatable {}
extension JioAdsFramework.TrackerType : Swift.Hashable {}
extension JioAdsFramework.AdCategory : Swift.Equatable {}
extension JioAdsFramework.AdCategory : Swift.Hashable {}
extension JioAdsFramework.AdCategory : Swift.RawRepresentable {}
extension JioAdsFramework.ExpandCollapse : Swift.Equatable {}
extension JioAdsFramework.ExpandCollapse : Swift.Hashable {}
extension JioAdsFramework.MediaPlayBack : Swift.Equatable {}
extension JioAdsFramework.MediaPlayBack : Swift.Hashable {}
extension JioAdsFramework.MediaPlayBack : Swift.RawRepresentable {}
extension JioAdsFramework.AdRequestType : Swift.Equatable {}
extension JioAdsFramework.AdRequestType : Swift.Hashable {}
extension JioAdsFramework.AdRequestType : Swift.RawRepresentable {}
extension JioAdsFramework.LogLevel : Swift.Equatable {}
extension JioAdsFramework.LogLevel : Swift.Hashable {}
extension JioAdsFramework.LogLevel : Swift.RawRepresentable {}
extension JioAdsFramework.MediaType : Swift.Equatable {}
extension JioAdsFramework.MediaType : Swift.Hashable {}
extension JioAdsFramework.MediaType : Swift.RawRepresentable {}
extension JioAdsFramework.CacheMode : Swift.Equatable {}
extension JioAdsFramework.CacheMode : Swift.Hashable {}
extension JioAdsFramework.CacheMode : Swift.RawRepresentable {}
extension JioAdsFramework.VideoAdControlTags : Swift.Equatable {}
extension JioAdsFramework.VideoAdControlTags : Swift.Hashable {}
extension JioAdsFramework.VideoAdControlTags : Swift.RawRepresentable {}
extension JioAdsFramework.NativeAdTags : Swift.Equatable {}
extension JioAdsFramework.NativeAdTags : Swift.Hashable {}
extension JioAdsFramework.NativeAdTags : Swift.RawRepresentable {}
extension JioAdsFramework.CarouselAdTags : Swift.Equatable {}
extension JioAdsFramework.CarouselAdTags : Swift.Hashable {}
extension JioAdsFramework.CarouselAdTags : Swift.RawRepresentable {}
extension JioAdsFramework.JioExpandCollapseAdTag : Swift.Equatable {}
extension JioAdsFramework.JioExpandCollapseAdTag : Swift.Hashable {}
extension JioAdsFramework.JioExpandCollapseAdTag : Swift.RawRepresentable {}
extension JioAdsFramework.LbandSqueeseOverLayTags : Swift.Equatable {}
extension JioAdsFramework.LbandSqueeseOverLayTags : Swift.Hashable {}
extension JioAdsFramework.LbandSqueeseOverLayTags : Swift.RawRepresentable {}
extension JioAdsFramework.Severity : Swift.Equatable {}
extension JioAdsFramework.Severity : Swift.Hashable {}
extension JioAdsFramework.Severity : Swift.RawRepresentable {}
extension JioAdsFramework.OrientationType : Swift.Equatable {}
extension JioAdsFramework.OrientationType : Swift.Hashable {}
extension JioAdsFramework.AdPodVariant : Swift.Equatable {}
extension JioAdsFramework.AdPodVariant : Swift.Hashable {}
extension JioAdsFramework.EQuartileEventType : Swift.Equatable {}
extension JioAdsFramework.EQuartileEventType : Swift.Hashable {}
extension JioAdsFramework.EQuartileEventType : Swift.RawRepresentable {}
extension JioAdsFramework.EAppType : Swift.Equatable {}
extension JioAdsFramework.EAppType : Swift.Hashable {}
extension JioAdsFramework.EAppType : Swift.RawRepresentable {}
extension JioAdsFramework.StreamType : Swift.Equatable {}
extension JioAdsFramework.StreamType : Swift.Hashable {}
extension JioAdsFramework.PlayBackType : Swift.Equatable {}
extension JioAdsFramework.PlayBackType : Swift.Hashable {}
extension JioAdsFramework.JioHttpClientError : Swift.Equatable {}
extension JioAdsFramework.JioHttpClientError : Swift.Hashable {}
