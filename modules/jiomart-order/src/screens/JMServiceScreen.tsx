import {StyleSheet} from 'react-native';
import React, {useCallback} from 'react';
import type {JMServiceScreenProps} from '../types/JMServiceScreenType';
import ScreenSlot, {
  DeeplinkHandler,
} from '../../../jiomart-general/src/ui/JMScreenSlot';
import useJMServiceScreenController from '../controllers/useJMServiceScreenController';
import Tab from '../../../jiomart-general/src/ui/Tab/Tab';
import {JioTypography} from '@jio/rn_components/src/index.types';
import {useColor} from '@jio/rn_components';
import JMHelpServiceRequestCard from '../components/JMHelpServiceRequestCard';
import {FlatList} from 'react-native-gesture-handler';
import {formatDateFromString} from '../../../jiomart-common/src/utils/JMDateUtility';

const JMServiceScreen = (props: JMServiceScreenProps) => {
  const {navigation, navigationBean, data} =
    useJMServiceScreenController(props);
  const tabBackgroundColor = useColor('primary_60');
  const tabItemIndicatorBackgroundColor = useColor('primary_30');

  const tabLabel = ['All', 'Active', 'Completed'];

  const renderActiveTabItem = useCallback((data: any) => {
    return (
      <FlatList
        data={data ?? []}
        renderItem={({item}) => (
          <JMHelpServiceRequestCard
            key={item}
            taskTitle={item?.taskTitle}
            ticketId={`Request ID: ${item?.ticketId ?? ''}`}
            badgeTitle={item?.status === 'Pending' ? 'Active' : 'Resolved'}
            badgeColor={
              item?.status === 'Pending' ? 'primary_60' : 'sparkle_60'
            }
            date={`Created: ${formatDateFromString(item?.date ?? '')}`}
            subPrefixTitle={'Follow up date: '}
            subTitleDate={formatDateFromString(item?.lastFollowUp ?? '')}
          />
        )}
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.containerStyle}
      />
    );
  }, []);
  const renderCompletedTabItem = useCallback((data: any[]) => {
    return (
      <FlatList
        data={data ?? []}
        renderItem={({item}) => (
          <JMHelpServiceRequestCard
            key={item}
            taskTitle={item?.taskTitle}
            ticketId={`Request ID: ${item?.ticketId ?? ''}`}
            badgeTitle={item?.status === 'Pending' ? 'Active' : 'Resolved'}
            badgeColor={
              item?.status === 'Pending' ? 'primary_60' : 'sparkle_60'
            }
            date={`Created: ${formatDateFromString(item?.date ?? '')}`}
            subPrefixTitle={'Follow up date: '}
            subTitleDate={formatDateFromString(item?.lastFollowUp ?? '')}
          />
        )}
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.containerStyle}
      />
    );
  }, []);
  const renderAllTabItem = useCallback((data: any) => {
    return (
      <FlatList
        data={data ?? []}
        renderItem={({item}) => (
          <JMHelpServiceRequestCard
            key={item}
            taskTitle={item?.taskTitle}
            ticketId={`Request ID: ${item?.ticketId ?? ''}`}
            badgeTitle={item?.status === 'Pending' ? 'Active' : 'Resolved'}
            badgeColor={
              item?.status === 'Pending' ? 'primary_60' : 'sparkle_60'
            }
            date={`Created: ${formatDateFromString(item?.date ?? '')}`}
            subPrefixTitle={'Follow up date: '}
            subTitleDate={formatDateFromString(item?.lastFollowUp ?? '')}
          />
        )}
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.containerStyle}
      />
    );
  }, []);

  const renderTabItem = useCallback(
    (tab: any) => {
      switch (tab) {
        case 'Active':
          return renderActiveTabItem(
            data?.filter((item: any) => item?.status === 'Pending'),
          );
        case 'Completed':
          return renderCompletedTabItem(
            data?.filter((item: any) => item?.status !== 'Pending'),
          );
        default:
          return renderAllTabItem(data);
      }
    },
    [data, renderActiveTabItem, renderAllTabItem, renderCompletedTabItem],
  );

  return (
    <DeeplinkHandler
      navigationBean={navigationBean}
      navigation={navigation}
      children={bean => (
        <ScreenSlot
          navigationBean={bean}
          navigation={navigation}
          children={_ => {
            return (
              <Tab
                tabStyle={{
                  container: {
                    backgroundColor: '#ffffff',
                  },
                  contentContainerStyle: {flex: 1},
                  style: {
                    backgroundColor: tabBackgroundColor,
                    paddingHorizontal: 24,
                  },
                  tabItemStyle: {flex: 1},
                  tabItemIndicatorStyle: {
                    backgroundColor: tabItemIndicatorBackgroundColor,
                  },
                  tabPanStyle: {
                    backgroundColor: '#ffffff',
                  },
                }}
                // disableGesture={config?.disableTabGesture}
                tabItem={{
                  color: 'primary_inverse',
                  appearance: JioTypography.BODY_S_BOLD,
                }}
                tabs={tabLabel}>
                {tabLabel.map(renderTabItem)}
              </Tab>
            );
          }}
        />
      )}
    />
  );
};

export default JMServiceScreen;

const styles = StyleSheet.create({
  containerStyle: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    rowGap: 12,
  },
});
