import {useEffect, useState} from 'react';
import {
  useSharedValue,
  withTiming,
  useAnimatedStyle,
} from 'react-native-reanimated';
import {useConfigFile} from '../../../hooks/useJMConfig';
import {UseSliderProps, UseSliderRef} from '../types/SliderTypes';
import usePincodeChange from '../../../../../jiomart-address/src/hooks/usePincodeChange';
import {EventEmitterKeys} from '../../../../../jiomart-common/src/JMConstants';
import {useGlobalState} from '../../../context/JMGlobalStateProvider';
import {JMConfigFileName} from '../../../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import {logAnalyticsEvent} from '../../../../../jiomart-common/src/JMAnalyticsUtility';
import {EventTriggerChannel} from '../../../../../jiomart-common/src/AnalyticsParams';
import { SendWebJavaScriptFunctions } from '../../JMScreenSlot';
import { WebFunctionQueue } from '../../../../../jiomart-webmanager/src/util/WebFunctionQueue';

export const useSlider = (
  props: UseSliderProps,
  ref?: React.Ref<UseSliderRef>,
) => {
  const {setEvent, qcDetails, selectedOption, setSelectedOption,setQCDetails} = useGlobalState();  
  const {navigation} = props;

  const tabHeight = useSharedValue(0);
  const tabWidth = useSharedValue(0);

  const [fixedTabWidth, setFixedTabWidth] = useState(0);
  
  useEffect(() => {
    const option =
      qcDetails?.journey === quickCommerceConfig?.quickDeliveryKey
        ? 'Quick'
        : 'Scheduled';
    enableOptionSelectionAnim(option);
  }, [fixedTabWidth, qcDetails]);

  const commanConfigData = useConfigFile(
    JMConfigFileName.JMCommonContentFileName,
  );
  const quickCommerceConfig = commanConfigData?.quickCommerceConfig;

  const slideAnim = useSharedValue(0);
  const isVisible = useSharedValue(true);
  const prevScrollY = useSharedValue(0);
  const offsetAnim = 48;
  let animationFrameId: number | null = null;
  const SCROLL_THRESHOLD = 30;
  const {shouldEnableScrollAnimation} = props;

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      {
        translateY: withTiming(isVisible.value ? 0 : -offsetAnim, {
          duration: 500,
        }),
      },
    ],
    height: withTiming(isVisible.value ? offsetAnim : 0, {duration: 300}),
    opacity: withTiming(Number(isVisible.value), {duration: 500}),
  }));

  const tabSwitchAnimatedStyle = useAnimatedStyle(() => ({
    height: tabHeight.value,
    width: tabWidth.value,
    transform: [{translateX: slideAnim.value}],
  }));

  const enableOptionSelectionAnim = (option: string) => {
    setSelectedOption(option);
    slideAnim.value = withTiming(option === 'Quick' ? 0 : fixedTabWidth, {
      duration: 300,
    });
  };

  const handleOptionChange = (option: string) => {
    const selectedTab =
      option === 'Quick'
        ? quickCommerceConfig?.quickDeliveryKey
        : quickCommerceConfig?.scheduledDeliveryKey;
        enableOptionSelectionAnim(option)
        if (navigation) {
          SendWebJavaScriptFunctions(setEvent, navigation, "selectedSADOOptionOnNative", {
            selectedSADOOptionOnNative: JSON.stringify({
              selectedTab: selectedTab,
            }),
          })
          const webJavaScriptFunctionsMap =
          WebFunctionQueue.getInstance().getDataInWebJavaScriptFunctionsMap();
          if(webJavaScriptFunctionsMap.has("selectedSADOOptionOnNative")){
            setQCDetails((prev: any) => {
              return {
                ...prev,
                journey: selectedTab,
              };
            });
          }
        }
        else{
          setEvent({
            [EventEmitterKeys.WEB_VIEW_EVENT_EMITT]: {
              selectedSADOOptionOnNative: JSON.stringify({
                selectedTab: selectedTab,
              }),
            },
          });
        }
        setTimeout(() => {
          logAnalyticsEvent({
            eventName: 'event_home_page',
            payload: {
              action:
                option === 'Quick' ? 'hp_quick_clicked' : 'hp_scheduled_clicked',
              label:
                option === 'Quick'
                  ? 'top_main_tab_hp_quick_clicked'
                  : 'top_main_tab_hp_scheduled_clicked',
              category: 'top main tab',
            },
            channel: `${EventTriggerChannel.FIREBASE}`,
          });
        }, 500);
  };

  const onScroll = (value: number) => {
    if (!shouldEnableScrollAnimation || value < 0) return;
    if (Math.abs(value - prevScrollY.value) < SCROLL_THRESHOLD) return;

    if (animationFrameId) {
      cancelAnimationFrame(animationFrameId);
    }

    animationFrameId = requestAnimationFrame(() => {
      isVisible.value = value <= prevScrollY.value;
      prevScrollY.value = value;
      animationFrameId = null;
    });
  };

  return {
    selectedOption,
    tabHeight,
    tabWidth,
    animatedStyle,
    tabSwitchAnimatedStyle,
    handleOptionChange,
    onScroll,
    shouldEnableScrollAnimation,
    quickCommerceConfig,
    setFixedTabWidth,
  };
};
