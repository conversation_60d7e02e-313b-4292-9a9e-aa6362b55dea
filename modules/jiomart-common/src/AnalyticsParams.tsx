export enum AnalyticsParams {
  CATEGORY = 'category',
  ACTION = 'action',
  LABEL = 'label',
  JOURNEY = 'journey',
  LOGIN_STATUS = 'login_status',
  PAGE_TYPE = 'page_type',
  PINCODE = 'pincode',
  PLATFORM = 'platform',
  SEARCH_API = 'search_api',
  TECH_STACK = 'tech_stack',
  THROUGH_DEEP_LINK = 'through_deep_link',
  LOGGED_USER_ID = 'logged_user_id',
  WEBEVENT_NAME = 'web_event_name',
  VALUE = 'value',
  BANNER_ID = 'banner_id',
  BANNER_NAME = 'banner_name',
  USER_ID = 'user ID',
  CUSTOMER_ID = 'Customer Id',
  CUSTOMER_TYPE = 'Customer Type',
  SYSTEM = 'system',
  ORDER_ID = 'order_id',
  OPTION = 'option',
  INDEX_NUMBER = 'index_number',
  ORDER_STATUS = 'current_order_status',
  PAGE_NAME = 'Page Name',
  SEARCH_TERM = 'search_term',
  TITLE = 'title',
  METHOD = 'method',
  SEARCH_QUERY = 'search_query',
  AF_SEARCH_STRING = 'af_search_string',
}

export enum EventTriggerChannel {
  FIREBASE = 'firebase',
  CLEVER_TAP = 'clever_tap',
  CLEVER_TAP_PROFILE = 'clever_tap_profile',
  SENSE = 'sense',
  FIREBASE_AND_SENSE = 'firebase_and_sense',
  APPSFLYER = 'appsflyer',
}

export type AnalyticsPayload = {
  [K in (typeof AnalyticsParams)[keyof typeof AnalyticsParams]]?: string;
};
export type AnalyticEvent = {
  channel: string;
  eventName: string;
  payload: AnalyticsPayload;
};
