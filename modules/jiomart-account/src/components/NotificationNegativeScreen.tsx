import {JioText} from '@jio/rn_components';
import {JioTypography} from '@jio/rn_components/src/index.types';
import React from 'react';
import {StyleSheet, View} from 'react-native';
import { rh, rw } from '../../../jiomart-common/src/JMResponsive';

const NotificationNegativeScreen = () => {
  return (
    <View style={styles.container}>
      <JioText
        text="No new notifications"
        appearance={JioTypography.BODY_S_BOLD}
        color={'primary_80'}
        style={styles.titleText}
      />
       <JioText
        text="You will be notified with alerts and latest offers over here"
        appearance={JioTypography.BODY_XS}
        color={'primary_grey_80'}
        style={styles.subTitleText}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: rw(24)
  },
  titleText: {
    textAlign: 'center'
  },
  subTitleText: {
    textAlign: 'center',
    marginTop: rh(4)
  }
});

export default NotificationNegativeScreen;
