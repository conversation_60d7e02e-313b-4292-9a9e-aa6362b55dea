import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import Shortcuts from '@rn-bridge/react-native-shortcuts';
import {useCallback, useEffect, useRef} from 'react';
import {EventSubscription} from 'react-native';
import {useDispatch} from 'react-redux';
import JMUserApiNetworkController from '../../../../modules/jiomart-networkmanager/src/JMNetworkController/JMUserAPINetworkController';

import useNotification from '../../../jiomart-account/src/hooks/useNotification';
import useCartOperation from '../../../jiomart-cart/src/hooks/useCartOperation';
import {resetCart} from '../../../jiomart-cart/src/slices/cartSlice';
import {AppScreens} from '../../../jiomart-common/src/JMAppScreenEntry';
import {
  AsyncStorageKeys,
  EventEmitterKeys,
} from '../../../jiomart-common/src/JMConstants';
import JMDeviceInfoData from '../../../jiomart-common/src/JMDeviceInfo';
import {
  ActionType,
  navBeanObj,
  CommonBean,
  NavigationType,
  getHomeAppScreenDestination,
} from '../../../jiomart-common/src/JMNavGraphUtil';
import {isNullOrUndefinedOrEmpty} from '../../../jiomart-common/src/JMObjectUtility';
import {HeaderType} from '../../../jiomart-common/src/JMScreenSlot.types';
import {JMSharedViewModel} from '../../../jiomart-common/src/JMSharedViewModel';
import {AppSourceType} from '../../../jiomart-common/src/SourceType';
import {JMAddressModel} from '../../../jiomart-common/src/uiModals/JMAddressModel';
import {JMLogger} from '../../../jiomart-common/src/utils/JMLogger';
import {useGlobalState} from '../../../jiomart-general/src/context/JMGlobalStateProvider';
import {
  getAppShortcutBeanFromDeeplinkFile,
  getAppShortcutsFromDeeplinkFile,
} from '../../../jiomart-general/src/deeplink/JMDeeplinkUtility';
import useUserProfile from '../../../jiomart-general/src/hooks/useUserProfile';
import {navigateTo} from '../../../jiomart-general/src/navigation/JMNavGraph';
import {JMStorageService} from '../../../jiomart-networkmanager/src/api/utils/JMStorageService';
import {JMDatabaseManager} from '../../../jiomart-networkmanager/src/db/JMDatabaseManager';
import {getBaseURL} from '../../../jiomart-networkmanager/src/JMEnvironmentConfig';
import useWishlistOperation from '../../../jiomart-wishlist/src/hooks/useWishlistOperation';
import {resetWishlist} from '../../../jiomart-wishlist/src/slices/wishlistSlice';
import JMFirebaseABTestUtility from '../../../jiomart-general/src/utils/JMFirebaseABTestUtility';
import useUserSessionHook from './useUserSessionHook';
import JMAppsFlyerUtility from '../../../jiomart-general/src/utils/JMAppsFlyerUtility';
import {logAnalyticsEvent} from '../../../jiomart-common/src/JMAnalyticsUtility';
import {EventTriggerChannel} from '../../../jiomart-common/src/AnalyticsParams';
import {
  subscribeToRNEvent,
  unsubscribeToRNEvent,
} from '../../../jiomart-common/src/Emitter';
import {callVersionFileAsync} from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileManager';
import {JMConfigFileName} from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import {addStringPref} from '../../../jiomart-common/src/JMAsyncStorageHelper';
import {WebFunctionQueue} from '../../../jiomart-webmanager/src/util/WebFunctionQueue';
import {SendWebJavaScriptFunctions} from '../../../jiomart-general/src/ui/JMScreenSlot';
import {useQueryClient} from '@tanstack/react-query';

const userApiNetworkController = new JMUserApiNetworkController();

const useJioMartMainUIController = (
  navigation: NativeStackNavigationProp<any>,
) => {
  useEffect(() => {
    if (JMSharedViewModel.Instance.fileVersionCalledPromise === null) {
      callVersionFileAsync(JMConfigFileName.JMVersionFileName);
    }
  }, []);
  const dispatch = useDispatch();
  JMLogger.log('useJioMartMainUIController');
  const {saveUserData} = useUserProfile();
  const {setEvent, setUserInitials, setNotificationCount, setDeeplinkData} =
    useGlobalState();

  const {getCart} = useCartOperation();
  const {getWishlistIds} = useWishlistOperation();

  const listenerSubscription = useRef<null | EventSubscription>(null);

  const appsFlyerUtility = JMAppsFlyerUtility();

  const appsFlyerIntialisation = useCallback(() => {
    appsFlyerUtility.initialiseAppsFlyer();
  }, []);

  useEffect(() => {
    appsFlyerIntialisation();
  }, [appsFlyerIntialisation]);
  const queryClient = useQueryClient();
  const clearOnLogout = async () => {
    await JMStorageService.clearAllKeys();
    setUserInitials('');
    dispatch(resetCart());
    dispatch(resetWishlist());
    queryClient.clear();
    userApiNetworkController.fetchGuestUserSession();
  };
  const handleChildEvent = useCallback(async (data: any) => {
    if (data?.logout) {
      if (JMSharedViewModel.Instance.appSource === AppSourceType.JM_BAU) {
        await userApiNetworkController.logoutUser();
      }
      await clearOnLogout();
      if (navigation && navigation.canGoBack!!) {
        navigateTo(
          navBeanObj({
            actionType: ActionType.OPEN_WEB_URL,
            destination: getHomeAppScreenDestination(),
            headerVisibility: HeaderType.CUSTOM,
            navigationType: NavigationType.RESET,
            loginRequired: false,
            actionUrl: getBaseURL(),
            shouldShowBottomNavBar: false,
            shouldShowDeliverToBar: true,
            headerType: 5,
          }),
          navigation,
        );
      }
      return;
    } else if (navigation && navigation.canGoBack!!) {
      if (JMSharedViewModel.Instance.isCRALoginTriggerFromWeb === true) {
        const routeNames = navigation
          ?.getState()
          ?.routes?.[0]?.state?.routes?.filter(
            route =>
              route?.name === AppScreens.COMMON_WEB_VIEW &&
              (route?.params?.deeplinkIdentifier === 'customer/account/login' ||
                route?.params?.actionUrl === 'customer/account/login'),
          );
        if (routeNames && routeNames?.length > 0) {
          navigation.goBack();
        }
        JMSharedViewModel.Instance.isCRALoginTriggerFromWeb = false;
      }
      navigation.goBack();
    }
  }, []);

  async function setUserInitialsToGlobal(userDetailRes) {
    const userDetails = JSON.stringify(userDetailRes);
    saveUserData(userDetailRes);
    JMDatabaseManager.user.saveUserDetails(userDetails);
    setUserInitials(userDetailRes?.first_name + ' ' + userDetailRes?.last_name);
  }
  const {saveUserSessionDetails, saveProfileDetails, handleAddressAfterLogin} =
    useUserSessionHook();

  const handleDidLoggedInEvent = useCallback(async (data?: any) => {
    try {
      console.warn('🚀 ~ handleDidLoggedInEvent ~ data:', JSON.stringify(data));
      if (data.authCode) {
        //if (data.newUser) {
        if (data.newUser) {
          const appsflyerEventDict = {
            af_registration_method: 'Mobile',
          };

          logAnalyticsEvent({
            eventName: 'af_complete_registration',
            payload: appsflyerEventDict,
            channel: EventTriggerChannel.APPSFLYER,
          });
        } else {
          logAnalyticsEvent({
            eventName: 'af_login',
            payload: {},
            channel: EventTriggerChannel.APPSFLYER,
          });
        }
        //}
        if (JMSharedViewModel.Instance.isCRALoginTriggerFromWeb) {
          const authData = {
            auth_code: data?.authCode,
            flag: true,
          };
          setEvent({
            WebViewEventEmitt: {
              passCraTw: `'${JSON.stringify(authData)}'`,
            },
          });
          navigation.goBack();
          JMSharedViewModel.Instance.setIsCRALoginTriggerFromWeb(false);
        } else {
          console.log('sendToWebCraDetails');
          const response =
            await userApiNetworkController.fetchLoggedInUserSession(
              data.authCode,
            );

          if (JMSharedViewModel.Instance.appSource === AppSourceType.JM_BAU) {
            if (response && response.status === 'success') {
              await saveUserSessionDetails(response);
              await saveProfileDetails();
              await handleAddressAfterLogin();
              console.log('🚀 ~ handleDidLoggedInEvent ~ response.data:');
              SendWebJavaScriptFunctions(
                setEvent,
                navigation,
                'sendToWebCraDetails',
                {
                  sendToWebCraDetails: JSON.stringify(response),
                },
              );
            }
          } else {
            if (response && response?.success === true) {
              const userDetailRaw =
                response?.data?.jcp_user_details?.users?.[0];
              if (userDetailRaw) {
                setUserInitialsToGlobal(userDetailRaw);
              }
              setEvent({
                WebViewEventEmitt: {
                  sendToWebCraDetails: JSON.stringify(response),
                },
              });
              console.log(
                '🚀 ~ handleDidLoggedInEvent ~ response.data:',
                JSON.stringify(response),
              );
            }
          }
        }
        getCart.mutate();
        getWishlistIds.mutate();
      }
      if (JMSharedViewModel.Instance.getNavigationData()) {
        navigation?.pop();
        navigateTo(
          navBeanObj(JMSharedViewModel.Instance.getNavigationData()!),
          navigation,
        );
        JMSharedViewModel.Instance.setNavigationBeanData(undefined);
      }
    } catch (error) {
      JMLogger.log('Error in handleDidLoggedInEvent - '+ JSON.stringify(error));
      if (navigation && navigation.canGoBack!!) {
        navigation.goBack();
      }
    }
  }, []);


  const handleGuestSessionCreationEvent = useCallback(async (data?: string) => {
    try {
      const parsedData = await JSON.parse(data);
      const guestSession = {
        id: parsedData?.id,
        expiryTime: parsedData?.valid_till
      }
      SendWebJavaScriptFunctions(setEvent, navigation, "sendGuestSession", {
        sendGuestSession: guestSession,
      })
    } catch (error) {
      JMLogger.log('Error in handleGuestSessionCreationEvent - ', error);
    }
  }, []);




  useEffect(() => {
    subscribeToRNEvent(EventEmitterKeys.CLOSE, handleChildEvent);
    subscribeToRNEvent(EventEmitterKeys.ON_LOGGED_IN, handleDidLoggedInEvent);
    subscribeToRNEvent(EventEmitterKeys.GUEST_SESSION_CREATION, handleGuestSessionCreationEvent);
    return () => {
      console.log('cleanup EventEmitterKeys.CLOSE');
      unsubscribeToRNEvent(EventEmitterKeys.CLOSE, () =>
        handleChildEvent(null),
      );
      unsubscribeToRNEvent(
        EventEmitterKeys.ON_LOGGED_IN,
        handleDidLoggedInEvent,
      );
      unsubscribeToRNEvent(
        EventEmitterKeys.GUEST_SESSION_CREATION,
        handleGuestSessionCreationEvent,
      );
    };
  }, []);
  useNotification(val => {
    setNotificationCount(val);
  });

  useEffect(() => {
    const setupShortcuts = async () => {
      const appShortcutList = await getAppShortcutsFromDeeplinkFile();

      appShortcutList?.map((item: CommonBean, index: number) => {
        if (item.title) {
          let shortcut = {
            id: item.id ?? `${index}`, // Ensure item.id is unique and string
            title: item.title, // or item.shortLabel
            longLabel: item.title,
            iconName: item.icon || 'ic_default', // fallback icon
          };
          Shortcuts.addShortcut(shortcut);
        }
      });

      // await Shortcuts.addShortcut(shortcuts); // note plural form if available in your lib
    };

    setupShortcuts();
    JMFirebaseABTestUtility.init();
  }, []);

  useEffect(() => {
    const callback = async (id: string) => {
      console.log('Shortcut Id:', id);
      if (id) {
        const appShortcutBean = await getAppShortcutBeanFromDeeplinkFile(id);
        if (appShortcutBean?.cta) {
          setDeeplinkData({cta: appShortcutBean?.cta});
        }
      }
    };

    listenerSubscription.current =
      Shortcuts.addOnShortcutUsedListener(callback);
    Shortcuts.getInitialShortcutId().then(callback);

    return () => {
      listenerSubscription.current?.remove();
      listenerSubscription.current = null;
    };
  }, []);
};

export default useJioMartMainUIController;
