import React from 'react';
import {<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>} from '@jio/rn_components';
import {
  ColorToken,
  IconColor,
  JioTypography,
  ShimmerKind,
} from '@jio/rn_components/src/index.types';
import {FlatList, Pressable, StyleSheet, View} from 'react-native';
import ItemCard, {ShimmerCard} from './ItemCard';
import SaledayCountdownTimer from './SaledayCountdownTimer';
import {useHorizontalProductListController} from '../../../jiomart-home/src/controller/HorizontalProductListController';
import JioAds from '../../../jiomart-general/src/JioAds/JioAds';
import {shouldShowQuickLogo} from '../../../jiomart-home/src/utils/HomeSectionUtils';
import {NavigationBean} from '../../../jiomart-common/src/JMNavGraphUtil';
import {navigateTo} from '../../../jiomart-general/src/navigation/JMNavGraph';
import type {JioAdsProps} from '../../../jiomart-general/src/JioAds/types/JioAds';

interface HorizontalProductListWithBackgroundProps {
  title?: string;
  titleColor?: keyof ColorToken | undefined;
  banner?: string;
  backgroundColor: string;
  seeAllTextColor: keyof ColorToken | undefined;
  seeAllBackgroundColor: string;
  seeAllArrowIconColor: IconColor;
  onSeeAllClick?: () => void;
  showSeeAll?: boolean;
  saleDateTime?: string;
  paddingTop?: number;
  widgetData: any;
  navigation: any;
  jioAds: Omit<JioAdsProps, 'adSpotId'>;
}

const HorizontalProductListWithBackground = (
  props: HorizontalProductListWithBackgroundProps,
) => {
  const {products, loading, homeConfig, serviceabilityData, hcatConfigData} =
    useHorizontalProductListController(props);

  const renderFooter = () => {
    if (!loading) return null;
    return (
      <View style={styles.loaderContainer}>
        <JioSpinner />
      </View>
    );
  };

  const renderItem = ({item}: {item: any}) => {
    switch (item?.type) {
      case 'jioAd':
        return <JioAds {...item} navigation={props?.navigation} />;
      default:
        return (
          <ItemCard
            product={item}
            onClick={(cta: NavigationBean) =>
              navigateTo(cta, props?.navigation)
            }
            disableWishlist={false}
            discount={`${item.discount_pct}% OFF`}
            discountBackgroundColor={'#E5F7EE'}
            discountColor={'sparkle_60'}
            disableAddToCart={item.in_stock}
            addedToCart={false}
            padding={5}
            showQcLogo={shouldShowQuickLogo(
              item,
              homeConfig?.showQuickLogoInProductCardDetails
                ?.verticleAndSellerList ?? [],
              serviceabilityData,
              hcatConfigData,
            )}
            qcLogoUrl={
              homeConfig?.showQuickLogoInProductCardDetails?.quicklogoUrl ?? ''
            }
          />
        );
    }
  };

  const renderShimmer = () => {
    return (
      <View style={{flexDirection: 'column', gap: 12}}>
        <JioShimmer
          width={100}
          height={20}
          kind={ShimmerKind.RECTANGLE}
          // style={styles.shimmerImage}
        />
        <View style={{flexDirection: 'row', gap: 12}}>
          {Array.from({length: 5}).map((_, index) => (
            <ShimmerCard key={`shimmer-${index}`} />
          ))}
        </View>
      </View>
    );
  };

  return (
    <>
      {loading && products.length === 0 ? (
        <View
          style={[
            styles.container,
            {
              paddingTop: props.paddingTop,
              // backgroundColor: props.backgroundColor,
            },
          ]}>
          <View style={styles.shimmerContainer}>{renderShimmer()}</View>
        </View>
      ) : (
        <View>
          {products.length !== 0 && (
            <View
              style={[
                styles.container,
                {
                  paddingTop: props.paddingTop,
                  backgroundColor: props.backgroundColor,
                },
              ]}>
              <View style={styles.header}>
                <JioText
                  text={props.title}
                  color={props.titleColor}
                  appearance={JioTypography.HEADING_XXS}
                  style={styles.title}
                />
              </View>
              {props.banner && (
                <SaledayCountdownTimer
                  saleDateTime={
                    props.saleDateTime !== undefined ? props.saleDateTime : ' '
                  }
                  backgroundColor={props.backgroundColor}
                  bannerImage={props.banner}
                  bannerText=""
                  bannerTextColor="primary_60"
                  margin={0}
                />
              )}

              <FlatList
                data={products}
                renderItem={renderItem}
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={styles.flatListContent}
                onEndReachedThreshold={0.5}
                ListFooterComponent={renderFooter}
              />
            </View>
          )}
          {props.showSeeAll && (
            <Pressable
              onPress={() => {
                props?.onSeeAllClick?.();
                console.log('See All');
              }}
              style={[
                styles.seeAll,
                {backgroundColor: props.seeAllBackgroundColor},
              ]}>
              <JioText
                text="See All"
                color={props.seeAllTextColor}
                appearance={JioTypography.BODY_XS_BOLD}
                style={styles.seeAllText}
              />
              <JioIcon ic="IcArrowNext" color={props.seeAllArrowIconColor} />
            </Pressable>
          )}
        </View>
      )}
    </>
  );
};

export default HorizontalProductListWithBackground;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
    paddingBottom: 10,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignContent: 'center',
  },
  title: {
    paddingLeft: 16,
    paddingBottom: 12,
    // marginTop: 16,
    // fontWeight: '900',
  },
  seeAll: {
    paddingHorizontal: 16,
    paddingVertical: 4,
    gap: 4,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  seeAllText: {
    // fontWeight: '700',
  },
  flatListContent: {
    paddingLeft: 24,
    gap: 12,
  },
  loaderContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  shimmerContainer: {
    flexDirection: 'row',
    paddingLeft: 24,
    gap: 12,
  },
});
