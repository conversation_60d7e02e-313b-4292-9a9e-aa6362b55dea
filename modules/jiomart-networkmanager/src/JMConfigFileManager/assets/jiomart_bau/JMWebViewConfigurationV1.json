{"RESTRICTED_BACK_URLS": ["https://payments.jio.com/JopWebApp/?paramx1", "https://rtss-sit.jioconnect.com/JopWebApp/?paramx1"], "ACCOUNT_LOGIN_URL": "customer/account/login", "orderConfirmed": {"enableVibrate": true, "vibratePattern": []}, "upiIntentAppsArray": [{"packageName": "net.one97.paytm", "appName": "Paytm", "image": "https://jep-asset.akamaized.net/JioInvest/images/paytm_14022025.png", "AppURLPrefix": "paytmmp://"}, {"packageName": "com.google.android.apps.nbu.paisa.user", "appName": "Google Pay", "image": "https://jep-asset.akamaized.net/JioInvest/images/GPay_14022025.png", "AppURLPrefix": "gpay://"}, {"packageName": "com.phonepe.app", "appName": "PhonePe", "image": "https://jep-asset.akamaized.net/JioInvest/images/PhonePe_14022025.png", "AppURLPrefix": "phonepe://"}, {"packageName": "com.dreamplug.cred", "appName": "CRED", "image": "https://jep-asset.akamaized.net/JioInvest/images/CRED_14022025.png", "AppURLPrefix": "credpay://"}, {"packageName": "in.org.npci.ios.upiapp", "appName": "BHIM", "image": "https://jep-asset.akamaized.net/JioInvest/images/BHIM_070125.png", "AppURLPrefix": "bhim://upi://pay?"}, {"packageName": "in.jfs.jiofinance", "image": "https://jep-asset.akamaized.net/JioInvest/images/JPB_14022025.png", "appName": "jfspay", "AppURLPrefix": "jfspay://upi/pay?"}], "oneRetailStatusBarColor": "#ffffff", "journeyPatterns": {"clearPreviousURL": ["checkout\\/cart", "customer\\/giftcard(\\?|$)", "customer\\/couponstore\\/?([?]|$)", "help-support\\/account-deletion\\/?([?]|$)"]}}