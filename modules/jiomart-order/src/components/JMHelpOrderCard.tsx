import React from 'react';
import {
  StyleSheet,
  Pressable,
  View,
  type StyleProp,
  type ViewStyle,
} from 'react-native';
import {JioIcon, JioText, useColor} from '@jio/rn_components';
import {
  IconColor,
  JioTypography,
  type JioIconProps,
  type JioTextProps,
} from '@jio/rn_components/src/index.types';
import {rh, rw} from '../../../jiomart-common/src/JMResponsive';
import FastImage, {type FastImageProps} from 'react-native-fast-image';
import {FlatList} from 'react-native-gesture-handler';

interface JMOrderCardHeaderProps {
  title?: JioTextProps;
  subTitle?: JioTextProps;
  icon?: JioIconProps;
}

interface JMOrderCardItemProps {
  productTitle?: JioTextProps;
  productSubTitle?: JioTextProps;
  image?: FastImageProps;
  child?: any;
  qty?: number;
  productStatus?: JioTextProps;
}

const JMOrderCardItem = (props: JMOrderCardItemProps) => {
  console.warn('props passed11--', JSON.stringify(props));
  const {productTitle, productSubTitle, image, child, qty, productStatus} =
    props;

  const difference = child?.length > 3 ? child?.length - 3 : 0;
  const borderColor = useColor('primary_grey_40');
  const size = child?.length === 1 ? 64 : 24;
  return (
    <View style={styles.orderCard}>
      <View
        style={{
          flexDirection: 'row',
          marginVertical: 4,
          columnGap: 12,
        }}>
        <View>
          <FlatList
            data={child?.slice(0, 4)}
            showsHorizontalScrollIndicator={false}
            keyExtractor={(_, index) => `image-${index}`}
            numColumns={2}
            renderItem={({item, index}) => {
              if (index === 3) {
                return (
                  <View
                    style={{
                      width: rw(24),
                      height: rw(24),
                      justifyContent: 'center',
                      alignItems: 'center',
                      margin: 4,
                    }}>
                    <JioText
                      text={`+${difference}`}
                      color={'primary_grey_80'}
                      appearance={JioTypography.BODY_XS_BOLD}
                    />
                  </View>
                );
              }
              return (
                <FastImage
                  source={{uri: item?.product_image}}
                  style={{
                    width: rw(size),
                    height: rw(size),
                    margin: 4,
                  }}
                  resizeMode={FastImage.resizeMode.cover}
                />
              );
            }}
            contentContainerStyle={{
              justifyContent: 'center',
              alignItems: 'center',
              flex: 1,
            }}
            style={{
              width: rw(64),
              height: rw(64),
              backgroundColor: '#ffffff',
            }}
          />
          {qty ? (
            <View
              style={{
                borderWidth: 1,
                width: rw(16),
                height: rw(16),
                justifyContent: 'center',
                alignItems: 'center',
                borderColor,
                borderRadius: 100,
                position: 'absolute',
                bottom: 0,
                right: 0,
                backgroundColor: borderColor,
              }}>
              <JioText
                text={`${qty}`}
                appearance={JioTypography.BODY_XXS_BOLD}
              />
            </View>
          ) : null}
        </View>
        <View
          style={{
            flexDirection: 'row', // horizontal layout
            alignItems: 'center', // vertical centering
            justifyContent: 'space-between',
            flex: 1,
          }}>
          <View
            style={{
              rowGap: 8,
              alignSelf: 'center',
              flex: 1,
            }}>
            <JioText
              color={'primary_grey_80'}
              appearance={JioTypography.BODY_XS_BOLD}
              maxLines={1}
              {...productStatus}
            />
            <JioText
              color={'primary_grey_60'}
              appearance={JioTypography.BODY_XS}
              maxLines={1}
              text={`${productTitle?.text ?? ''} (${
                productSubTitle?.text ?? ''
              })`}
            />
          </View>
          <JioIcon ic={'IcChevronRight'} color={IconColor.PRIMARY60} />
        </View>
      </View>
    </View>
  );
};

interface JMOrderCardProps extends JMOrderCardHeaderProps {
  style?: StyleProp<ViewStyle>;
  item?: JMOrderCardItemProps[];
  onPress?: () => void;
}

const JMHelpOrderCard = (props: JMOrderCardProps) => {
  console.warn('ordre props--', JSON.stringify(props));
  const {style, title, subTitle, icon, item, onPress} = props;
  return (
    <Pressable onPress={onPress} activeOpacity={0.65}>
      <View>
        {item?.map((item, index) => {
          return <JMOrderCardItem {...item} key={index} />;
        })}
      </View>
    </Pressable>
  );
};

export default JMHelpOrderCard;

const styles = StyleSheet.create({
  orderCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 16,
    marginVertical: 4,
    elevation: 2,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    marginRight: 12,
    shadowOffset: {width: 0, height: 2},
    shadowRadius: 4,
  },
});
