import {
  Animated,
  TextInput,
  Pressable,
  View,
  type StyleProp,
  type ViewStyle,
} from 'react-native';
import React, {useState, useCallback, useMemo} from 'react';
import {JioAvatar, JioIcon, JioText, useColor} from '@jio/rn_components';
import useJioHeaderController from './controllers/useJioHeaderController';
import type {
  JioHeaderBadgeProps,
  JioHeaderIconList,
  JioHeaderIconListViewProps,
  JioHeaderNotifyProps,
  JioHeaderSearchBarProps,
  JMHeaderNavProps,
} from './types/JioHeader';
import {styles} from './styles/JioHeaderStyle';
import {
  IconColor,
  JioAvatarKind,
  JioAvatarSize,
  JioColor,
  JioTypography,
} from '@jio/rn_components/src/index.types';
import useJioHeaderSearchBar from './hooks/useJioHeaderSearchBar';

import {IconKey} from '@jio/rn_components/src/utils/IconUtility';
import {useGlobalState} from '../../context/JMGlobalStateProvider';
import {useCartCount} from '../../../../jiomart-cart/src/hooks/useCart';
import type {NavigationBean} from '../../../../jiomart-common/src/JMNavGraphUtil';
import {NavigationBeanType} from './JioMartHeader';
import {JMSharedViewModel} from '../../../../jiomart-common/src/JMSharedViewModel';
import CustomMediaRendered from '../CustomMediaRendered';
import {JMDatabaseManager} from '../../../../jiomart-networkmanager/src/db/JMDatabaseManager';
import { JMLogger } from '../../../../jiomart-common/src/utils/JMLogger';

const JioHeader = (props: JMHeaderNavProps) => {
  const {
    data,
    navTitle,
    subTitle,
    style,
    onPress: rawOnPress,
    headerTopComponent,
    headerBottomComponent,
    searchTextHandler: rawSearchTextHandler,
    onSubmitHandler: rawOnSubmitHandler,
    customFunctionality: rawCustomFunctionality,
  } = useJioHeaderController(props);
  JMLogger.log("JioHeader data")
  const backgroundColor = data?.backgroundColor;
  const headerIconsList = data?.headerIconsList;
  const searchData = data?.searchData;
  const leftIconList = headerIconsList?.leftIconList;
  const rightIconList = headerIconsList?.rightIconList;
  const headerBackgroundColor = useColor(
    (backgroundColor as JioColor) ?? 'primary_50',
  );
  const [searchValue, setSearchValue] = useState(props.searchValue ?? '');

  // Memoize callbacks and objects to avoid unnecessary re-renders
  const onPress = rawOnPress ? useCallback(rawOnPress, [rawOnPress]) : undefined;
  const searchTextHandler = rawSearchTextHandler ? useCallback(rawSearchTextHandler, [rawSearchTextHandler]) : undefined;
  const onSubmitHandler = rawOnSubmitHandler ? useCallback(rawOnSubmitHandler, [rawOnSubmitHandler]) : undefined;
  const customFunctionality = useMemo(() => rawCustomFunctionality, [rawCustomFunctionality]);

  if (!data || !Object.keys(data ?? {}).length) {
    return null;
  }

  return (
    <>
      {React.isValidElement(headerTopComponent) &&
        React.cloneElement(headerTopComponent)}

      <View
        style={[
          styles.container,
          {backgroundColor: headerBackgroundColor},
          style?.headerStyle,
        ]}>
        {leftIconList && leftIconList?.length > 0 && (
          <JioHeaderIconListView
            iconList={leftIconList as JioHeaderIconList[]}
            onPress={onPress}
            customFunctionality={customFunctionality}
          />
        )}

        {navTitle && !searchData?.enable && (
          <JioText
            text={navTitle}
            appearance={JioTypography.BODY_M_BOLD}
            maxLines={1}
            color="primary_inverse"
            style={styles.headerTitle}
          />
        )}

        {searchData?.enable && (
          <JioHeaderSearchBar
            {...searchData}
            onSubmitHandler={onSubmitHandler}
            textInput={{
              onChangeText: text => {
                setSearchValue(text);
                searchTextHandler?.(text);
              },
              value: searchData?.enableAnimation ? '' : searchValue,
            }}
            onPress={onPress}
          />
        )}

        {subTitle && (
          <JioText
            text={subTitle}
            appearance={JioTypography.BODY_XXS}
            color="primary_30"
            style={styles.marginLeftAuto}
          />
        )}

        {rightIconList && rightIconList?.length > 0 && (
          <JioHeaderIconListView
            iconList={rightIconList as JioHeaderIconList[]}
            style={[styles.marginLeftAuto]}
            onPress={onPress}
            customFunctionality={customFunctionality}
          />
        )}
      </View>

      {React.isValidElement(headerBottomComponent) &&
        React.cloneElement(headerBottomComponent)}
    </>
  );
};

const JioHeaderSearchBar = React.memo((props: JioHeaderSearchBarProps) => {
  const {
    style,
    backgroundColor,
    inputRef,
    textInput,
    isEditable,
    value,
    JioHeaderSearchBarIconListView,
    onSearchBarPress,
    onSearchText,
    onFocus,
    onBlur,
    closeSearchButton,
    onSubmitHandler,
    enableAnimation,
    animationTexts,
    fadeAnim,
    translateYAnim,
    currentIndex,
    animationEnabled,
  } = useJioHeaderSearchBar(props);

  return (
    <View
      style={[
        styles.searchBarContainer,
        {
          backgroundColor: backgroundColor,
        },
        style,
      ]}>
      <JioHeaderSearchBarIconListView />
      <Pressable
        style={styles.flexOne}
        hitSlop={7}
        onPress={onSearchBarPress}
        disabled={isEditable}>
        <View
          style={[
            styles.flexOne,
            !isEditable && value !== '' ? styles.flexWrap : null,
          ]}
          pointerEvents={isEditable ? 'auto' : 'none'}>
          <TextInput
            ref={inputRef}
            style={[styles.searchBarTextInput, textInput?.style]}
            pointerEvents={isEditable ? 'auto' : 'none'}
            underlineColorAndroid={
              textInput?.underlineColorAndroid ?? 'transparent'
            }
            placeholderTextColor={textInput?.placeholderTextColor ?? 'white'}
            enablesReturnKeyAutomatically={
              textInput?.enablesReturnKeyAutomatically ?? true
            }
            selectionColor={textInput?.selectionColor ?? 'rgba(0, 120, 173, 1)'}
            onChangeText={onSearchText}
            onFocus={onFocus}
            onBlur={onBlur}
            onSubmitEditing={() => {
              onSubmitHandler?.(value);
            }}
            {...textInput}
            placeholder={
              animationEnabled
                ? ''
                : JMSharedViewModel?.Instance?.isQcJourneySelected
                ? (props as any).qcPlaceholder ?? props.placeholder ?? 'Search in Quick'
                : props.placeholder ?? 'Search JioMart'
            }
            value={value}
            numberOfLines={1}
          />
          {animationEnabled && animationTexts && animationTexts?.length > 0 && (
            <View style={styles.animationTextWrapper}>
              <Animated.Text
                style={[
                  styles.animationText,
                  {
                    opacity: fadeAnim,
                    transform: [{translateY: translateYAnim}],
                  },
                ]}>
                {animationTexts?.[currentIndex]}
              </Animated.Text>
            </View>
          )}
        </View>
      </Pressable>

      {value && isEditable ? (
        <Pressable onPress={closeSearchButton} hitSlop={6}>
          <JioIcon ic="IcCloseRemove" color={IconColor.INVERSE} />
        </Pressable>
      ) : null}
    </View>
  );
});

const JioHeaderIconListView = React.memo(
  (props: JioHeaderIconListViewProps) => {
    const {iconList = [], style, onPress, customFunctionality} = props;
    const {userInitials, notificationCount} = useGlobalState();
    const count = useCartCount();
    const [displayedCount, setDisplayedCount] = useState(count);
    const [pendingIncrease, setPendingIncrease] = useState(false);
    const [animationTrigger, setAnimationTrigger] = useState(-1);

    React.useEffect(() => {
      if (count < displayedCount) {
        setDisplayedCount(count);
        setPendingIncrease(false);
      } else if (count > displayedCount) {
        setPendingIncrease(true);
        if (count >= 0) {
          setAnimationTrigger(new Date().getMilliseconds());
        }
      }
    }, [count, displayedCount]);

    const handleAnimationComplete = () => {
      if (pendingIncrease) {
        setDisplayedCount(count);
        setPendingIncrease(false);
      }
      return {};
    };

    // Memoize modifiedIconList
    const modifiedIconList = React.useMemo(() =>
      iconList.map(list => {
        switch (list?.cta?.type) {
          case NavigationBeanType.CART:
            return {
              ...list,
              image: {
                ...list?.image,
                isNotify: false,
                badge: displayedCount,
                iterationCount: 1,
                animationTrigger: animationTrigger,
                allowAnimationAutoPlay: false,
                onAnimationComplete: handleAnimationComplete,
              },
            };
          case NavigationBeanType.AVATAR:
            return {
              ...list,
              avatar: {
                ...list?.avatar,
                kind: userInitials ? JioAvatarKind.INITIALS : JioAvatarKind.ICON,
                name: userInitials,
                isNotify:
                  JMDatabaseManager.user.isUserLoggedInFlag() &&
                  notificationCount > 0,
              },
            };
          default:
            return list;
        }
      }),
      [iconList, userInitials, notificationCount, displayedCount, animationTrigger]
    );

    const IconList = modifiedIconList?.map((list, index) => {
      const item = list?.icon || list?.avatar || list?.image;
      const isIcon = !!list?.icon;
      const isAvatar = !!list?.avatar;
      const isImage = !!list?.image;
      const feedback = item?.feedback ?? true;

      const disabled = !!(
        customFunctionality?.[`${list?.cta?.type}`]?.disabled || item?.disabled
      );
      const badge =
        customFunctionality?.[`${list?.cta?.type}`]?.badge || item?.badge;
      const isNotify =
        customFunctionality?.[`${list?.cta?.type}`]?.isNotify || item?.isNotify;
      const Style =
        customFunctionality?.[`${list?.cta?.type}`]?.style || item?.style;

      return (
        <Pressable
          key={`h_icon-${index}`}
          style={({pressed}: {pressed: boolean}): StyleProp<ViewStyle> => [
            isIcon || feedback ? styles.padding12 : {borderRadius: 100},
            disabled ? {opacity: 0.65} : null,
            pressed ? {backgroundColor: '#0C5273A6'} : null,
            Style,
          ]}
          onPress={() => {
            if (
              !customFunctionality?.[`${list?.cta?.type}`]?.disableDefaultCall
            ) {
              onPress?.(list?.cta as NavigationBean);
            }
            customFunctionality?.[`${list?.cta?.type}`]?.onPress?.(
              list?.cta as NavigationBean,
            );
          }}
          disabled={disabled}>
          <>
            {isIcon ? (
              <JioIcon ic={list?.icon?.ic as IconKey} {...list?.icon} />
            ) : null}
            {isAvatar ? (
              <JioAvatar
                kind={list?.avatar?.kind ?? JioAvatarKind.ICON}
                name={list?.avatar?.name ?? ''}
                size={list?.avatar?.size ?? JioAvatarSize.SMALL}
                {...list?.avatar}
              />
            ) : null}
            {isImage ? (
              <CustomMediaRendered
                {...list?.image}
                customStyles={[
                  list?.image?.customStyles,
                  {
                    width: list?.image?.width || 24,
                    height: list?.image?.height || 24,
                  },
                ]}
              />
            ) : null}
            {badge && !pendingIncrease ? (
              <JioHeaderBadge count={item?.badge} />
            ) : null}
            {isNotify ? (
              <JioHeaderNotify
                style={[
                  styles.notifyIcon,
                  isAvatar ? {top: 0, right: 0} : null,
                ]}
              />
            ) : null}
          </>
        </Pressable>
      );
    });

    return <View style={[style, styles.iconListContainer]}>{IconList}</View>;
  },
);

const JioHeaderBadge = React.memo((props: JioHeaderBadgeProps) => {
  const {count, style} = props;

  const secondary50 = useColor('secondary_50');

  return count ? (
    <View
      style={[
        styles.badge,
        {
          backgroundColor: secondary50,
        },
        style,
      ]}>
      <JioText
        text={`${count}`}
        appearance={JioTypography.BODY_XXS}
        color={'primary_inverse'}
      />
    </View>
  ) : null;
});

const JioHeaderNotify = React.memo((props: JioHeaderNotifyProps) => {
  const {style} = props;
  const secondary50 = useColor('secondary_50');

  return (
    <View style={[styles.notify, {backgroundColor: secondary50}, style]} />
  );
});

export default React.memo(JioHeader);
