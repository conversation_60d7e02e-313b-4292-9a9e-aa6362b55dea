import {useInfiniteQuery, useQueryClient} from '@tanstack/react-query';
import {getTopDealsWidgets} from '../utils/jmDashboardUtils';
import {useEffect, useState} from 'react';
import {getPersistString} from '../utils/HomeUtils';
import {JMConfigFileName} from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import {useConfigFile} from '../../../jiomart-general/src/hooks/useJMConfig';

const useJMSpecialSectionRendererController = (
  slug: string,
  identifier: string,
  onWidgetLoadingChange: any,
) => {
  const homeConfig = useConfigFile(
    JMConfigFileName.JMHomeDashboardConfigurationFileName,
  );

  console.log('homeConfig---am', homeConfig);

  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    isFetching,
    status,
    refetch,
  } = useInfiniteQuery<{widgets: any[]; nextPage?: number}, Error>({
    queryKey: ['widgets', identifier, getPersistString(slug)],
    queryFn: async ({pageParam}) => {
      try {
        const page = typeof pageParam === 'number' ? pageParam : 1;
        // Dynamic page size: 2 for first page, 4 for subsequent pages
        const dynamicPageSize =
          page === 1 || page === 2
            ? homeConfig?.homeConfig
                ?.firstRenderDefaultLoadCountForSpecialSection ?? 2
            : homeConfig?.homeConfig
                ?.defaultLoadCountOnScrollForSpecialSection ?? 4;
        console.log(
          `[WidgetQuery] ${identifier} - Page: ${page}, PageSize: ${dynamicPageSize}`,
        );

        const widgets = await getTopDealsWidgets(
          page,
          dynamicPageSize,
          identifier,
        );

        // Ensure widgets is always an array
        const safeWidgets = Array.isArray(widgets) ? widgets : [];

        return {
          widgets: safeWidgets,
          nextPage:
            safeWidgets.length === dynamicPageSize ? page + 1 : undefined,
        };
      } catch (error) {
        console.error(`[WidgetQuery] ${identifier} - Query failed:`, error);
        // Return empty result to prevent crashes
        return {
          widgets: [],
          nextPage: undefined,
        };
      }
    },
    getNextPageParam: (lastPage: {widgets: any[]; nextPage?: number}) =>
      lastPage.nextPage,
    initialPageParam: 1,
    enabled: !!identifier,
    staleTime: 1000 * 60 * 0, // 5 minutes - much longer stale time for widgets to prevent re-fetching
    gcTime: 1000 * 60 * 10, // 15 minutes - keep widget data much longer
    retry: 0,
    // placeholderData: (previousData, previousQuery) => previousData, // Enable placeholder data for instant loading
  });

  // Flatten widgets from all pages with defensive programming
  const widgets = data?.pages?.flatMap(page => page?.widgets || []) || [];

  // Debug logging to track widget count and page behavior
  console.log(
    `[WidgetQuery] ${identifier} - Total widgets: ${
      widgets.length
    }, Pages loaded: ${data?.pages?.length || 0}, HasNextPage: ${hasNextPage}`,
  );

  // Notify parent about widget loading (has more widgets)
  useEffect(() => {
    if (onWidgetLoadingChange) {
      onWidgetLoadingChange(!!hasNextPage);
    }
  }, [hasNextPage, onWidgetLoadingChange]);

  const handleEndReached = () => {
    console.log(
      `[WidgetQuery] ${identifier} - End reached. HasNextPage: ${hasNextPage}, IsFetching: ${isFetchingNextPage}, Current widgets: ${widgets.length}`,
    );
    if (hasNextPage && !isFetchingNextPage) {
      console.log(`[WidgetQuery] ${identifier} - Fetching next page...`);
      fetchNextPage();
    }
  };

  return {
    widgets,
    isLoading,
    isFetchingNextPage,
    hasNextPage,
    fetchNextPage,
    handleEndReached,
    refetch,
  };
};

export default useJMSpecialSectionRendererController;
