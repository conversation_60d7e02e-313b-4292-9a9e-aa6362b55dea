import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import type {NavigationStackData} from '../../jiomart-common/src/JMNavGraphUtil';
import {AppScreens} from '../../jiomart-common/src/JMAppScreenEntry';
import ProductListingScreen from './screens/ProductListingScreen';
import ProductGridListingScreen from './screens/ProductGridListingScreen';
import ProductSearchListingScreen from './screens/ProductSearchListingScreen';
import { Platform } from 'react-native';
import { StackAnimationTypes } from 'react-native-screens';

const Stack = createNativeStackNavigator<NavigationStackData>();

const JMProductListingNavigation = () => {
  return (
    <Stack.Navigator screenOptions={{
      headerShown: false,
      gestureEnabled: Platform.OS==="ios" ? false : true
      }}>
      <Stack.Screen
        name={AppScreens.PRODUCT_LISTING_SCREEN}
        component={ProductListingScreen}
        options={({route}) => {
          const navigationBean = route.params || {};
          return {
            animation:
              (navigationBean.animation as StackAnimationTypes) || 'slide_from_right',
          };
        }}
      />
      <Stack.Screen
        name={AppScreens.PRODUCT_GRID_LISTING_SCREEN}
        component={ProductGridListingScreen}
        options={({route}) => {
          const navigationBean = route.params || {};
          return {
            animation:
              (navigationBean.animation as StackAnimationTypes) || 'slide_from_right',
          };
        }}
      />
      <Stack.Screen
        name={AppScreens.PRODUCT_SEARCH_LISTING_SCREEN}
        component={ProductSearchListingScreen}
        options={({route}) => {
          const navigationBean = route.params || {};
          return {
            animation:
              (navigationBean.animation as StackAnimationTypes) || 'slide_from_right',
          };
        }}
      />
    </Stack.Navigator>
  );
};

export default JMProductListingNavigation;
