import {JMSharedViewModel} from '../../../jiomart-common/src/JMSharedViewModel';
import {AppSourceType} from '../../../jiomart-common/src/SourceType';
import JMBaseNetworkController, {
  type JMBaseNetworkControllerOptions,
} from '../base/JMBaseNetworkController';
import JMJCPServiceablityNetworkController from '../JMJCPNetworkController/JMJCPServiceablityNetworkController';
import JMBAUServiceablityNetworkController from '../JMBAUNetworkController/JMBAUServiceablityNetworkController';
import {
  ConfigInfo,
  DataResponse,
  JMCheckServiceabilityData,
  JMPolygonStore,
  JMPStoreCode,
  PinInfo,
  PromiseResult,
  Serviceability,
} from '../models/home/<USER>';

import {jsonParse} from '../../../jiomart-common/src/uiModals/JMResponseParser';
import {AsyncStorageKeys} from '../../../jiomart-common/src/JMConstants';
import {
  addStringPref,
  getPrefString,
} from '../../../jiomart-common/src/JMAsyncStorageHelper';
import {JMDatabaseManager} from '../db/JMDatabaseManager';
import { VerticleTypes } from '../../../jiomart-home/src/utils/HomeSectionUtils';

/**
 * JMServiceablityNetworkController
 *
 * Wrapper controller for handling serviceability checks across different JioMart applications.
 * Orchestrates the 4-step serviceability flow:
 * 1. MStar Pin Info API - Pincode metadata (coordinates and location details)
 * 2. HCAT QC Config API - Quick Commerce configuration and polygon settings
 * 3. Polygon/Jio-net API - Store details and polygon data for serviceability mapping
 * 4. Fynd Promise Wrapper API :
 *    - Region code information (MStar Region API)
 *    - Delivery promise calculations (Fynd Promise API)
 *    - Distance configuration for MStar systems (Set Delivery API)
 *
 * Details Doc @link : https://rilcloud-my.sharepoint.com/:w:/r/personal/samriddha_samanta_ril_com/_layouts/15/doc.aspx?sourcedoc=%7B0479a173-9e2e-4035-b346-172315bc63b0%7D&action=edit
 *
 * @extends JMServiceablityNetworkNewController
 */
class JMServiceablityNetworkNewController extends JMBaseNetworkController {
  private _homeController: any;

  constructor(options?: JMBaseNetworkControllerOptions) {
    super(options);
    switch (this.options?.appSource) {
      case AppSourceType.JM_JCP:
        this._homeController = new JMJCPServiceablityNetworkController();
        break;
      case AppSourceType.JM_BAU:
        this._homeController = new JMBAUServiceablityNetworkController();
        break;
      default:
        this._homeController = new JMBAUServiceablityNetworkController();
    }
  }

  /**
   * Checks service availability for a given pincode with caching logic.
   *
   * This function orchestrates multiple API calls to determine if delivery services
   * are available for a specific pincode.
   *
   * @param pincode - The postal code to check serviceability for
   * @param refreshIntervalInMin - Cache refresh interval in minutes (default: 30)
   * @param invalidateCacheAndRefetch - Force refresh cache and fetch new data (default: false)
   * @returns Promise containing delivery promise and serviceability information
   *
   * Process Flow:
   * 1. Check cache for existing data within refresh interval
   * 2. Fetch pincode metadata (coordinates) via getPincodeData()
   * 3. Get Quick Commerce configuration via getQCConfig()
   * 4. Find polygon stores and serviceability (if QC config and coordinates exist)
   * 5. Get fynd Promise Api Response details via getFyndPromiseDetailsWrapper() which internally handles:
   *    - Region code information retrieval
   *    - Delivery promise calculations
   *    - Distance configuration for MStar systems
   * 6. Cache results for future use
   */
  public checkServiceabilityWithPincode = async (
    pincode: string,
    refreshIntervalInMin: number = 30,
    invalidateCacheAndRefetch: boolean = false,
  ): Promise<{
    pincodeMetaData: PinInfo | null;
    fyndPromiseApiResponse: DataResponse | null;
  }> => {
    try {
      // Check cache first to avoid unnecessary API calls
      const cachedResult = await this.getCachedServiceabilityDataWithExpiryChecking(
        refreshIntervalInMin,
      );
      if (cachedResult && !invalidateCacheAndRefetch) {
        console.log('Using cached serviceability data');
        return {
          pincodeMetaData: cachedResult.pinMetadata,
          fyndPromiseApiResponse: cachedResult.fyndPromiseData,
        };
      }

      console.log(
        '======== checkServiceabilityWithPincode : Api 1 ==================',
      );
      const pincodeMetaData = await this.getPincodeData(pincode);

      console.log(
        '======== checkServiceabilityWithPincode : Api 2 ==================',
      );
      const qcConfigParsed = await this.getQCConfig(pincode);

      // Save HCat Config Data
      await this.saveHcatConfigData(qcConfigParsed);

      let qcStoreAndServiceability: {
        store: JMPolygonStore;
        serviceability: Serviceability;
      } | null = null;

      if (
        qcConfigParsed?.[VerticleTypes.GROCERIES]?.polygon &&
        pincodeMetaData?.lat &&
        pincodeMetaData?.lon
      ) {
        //  3. Polygon/Jio-net API - Store details and polygon data
        console.log(
          '======== checkServiceabilityWithPincode : Api 3 ==================',
        );
        const polygonStoreParsed = await this.getPolygonStoreDetails(
          pincodeMetaData,
        );

        qcStoreAndServiceability = polygonStoreParsed
          ? this.findQuickCommerceStoreAndServiceability(polygonStoreParsed)
          : null;

        await this.savePolygonStoreData(
          qcStoreAndServiceability?.store, 
          qcStoreAndServiceability?.serviceability
        );
      }

      // Wrapper API For - Fynd promise response,MStar Region API,Fynd Promise API,Set Delivery API
      console.log(
        '======== checkServiceabilityWithPincode : Api 4 ==================',
      );
      const fyndPromiseApiResponse = await this.getFyndPromiseDetailsWrapper(
        pincode,
        pincodeMetaData,
        qcStoreAndServiceability?.store ?? null,
      );

      await this.saveCacheData(pincodeMetaData, fyndPromiseApiResponse);
      return {
        pincodeMetaData: pincodeMetaData,
        fyndPromiseApiResponse: fyndPromiseApiResponse,
      };
    } catch (error) {
      console.log('CheckServiceabilityWithPincode Error : ', error);
      throw error;
    }
  };

  /**
   * Retrieves cached serviceability data if it exists and is within refresh interval
   */
  private getCachedServiceabilityDataWithExpiryChecking = async (
    refreshIntervalInMin: number,
  ) => {
    const cachedData = await getPrefString(
      AsyncStorageKeys.SERVICEABILITY_DATA_WITH_WRAPPER,
    );

    if (!cachedData) return null;

    try {
      const parsedCache = JSON.parse(cachedData);
      const currentTime = Date.now();
      const cacheAge = currentTime - (parsedCache.timestamp || 0);
      const refreshThreshold = refreshIntervalInMin * 60 * 1000;

      if (cacheAge < refreshThreshold) {
        console.log(`Cache hit: Data is ${Math.round(cacheAge / 1000)}s old`);
        return parsedCache;
      }

      console.log(
        `Cache expired: Data is ${Math.round(cacheAge / 60000)}min old`,
      );
      return null;
    } catch (error) {
      console.warn('Failed to parse cached data, fetching fresh data');
      return null;
    }
  };

  getCachedServiceabilityData = async ():Promise<DataResponse|null> => {
    
    const cachedData = await getPrefString(
      AsyncStorageKeys.SERVICEABILITY_DATA_WITH_WRAPPER,
    );

    if (!cachedData) return null;

    try {
      const parsedCache = jsonParse<DataResponse>(cachedData);
      return parsedCache;
    } catch (error) {
      console.warn('Failed to parse cached data');
      return null;
    }
  };

  private saveCacheData = async (
    pincodeMetaData: PinInfo | null,
    promiseApiResponse: DataResponse | null,
  ) => {
    if (promiseApiResponse != null && promiseApiResponse) {
      const cacheData = {
        fyndPromiseData: promiseApiResponse,
        timestamp: Date.now(),
        pinMetadata: pincodeMetaData,
        //pincode: pincode // Optional: for debugging purposes
      };
      await addStringPref(
        AsyncStorageKeys.SERVICEABILITY_DATA_WITH_WRAPPER,
        JSON.stringify(cacheData),
      );
    }
  };

  private saveHcatConfigData = async (
    configInfo: Record<string, ConfigInfo> | null,
  ) => {
    if (configInfo != null && configInfo) {
      await addStringPref(
        AsyncStorageKeys.HCAT_CONFIG_DATA,
        JSON.stringify(configInfo),
      );
    }
  };

  getCachedHcatConfigData = async (): Promise<Record<string, ConfigInfo> | null> => {
    const cachedData = await getPrefString(AsyncStorageKeys.HCAT_CONFIG_DATA);
    if (cachedData) {
      try {
        const parsedCache = jsonParse<Record<string, ConfigInfo>>(cachedData);
        return parsedCache;
      } catch (error) {
        console.warn('Failed to parse cached data');
        return null;
      }
    }
    return null;
  };

  private savePolygonStoreData = async (
    storeData: JMPolygonStore | undefined,
    serviceabilities: Serviceability | undefined,
  ) => {
    if (serviceabilities != null && serviceabilities && serviceabilities!=null && serviceabilities) {
      const data = {
        storeData: storeData,
        serviceabilities: serviceabilities,
      };
      await addStringPref(
        AsyncStorageKeys.POLYGON_STORE_DATA,
        JSON.stringify(data),
      );
    }
  };

  getPolygonStoreData = async (): Promise<{
    storeData: JMPolygonStore | undefined,
    serviceabilities: Serviceability | undefined,
  } | null> => {
    const cachedData = await getPrefString(AsyncStorageKeys.POLYGON_STORE_DATA);
    if (cachedData) {
      try {
        const parsedCache = jsonParse<any>(cachedData);
        return {
          storeData: parsedCache.storeData as JMPolygonStore,
          serviceabilities: parsedCache.serviceabilities as Serviceability,
        };
      } catch (error) {
        console.warn('Failed to parse cached data');
        return null;
      }
    }
    return null;
  };

  private getPincodeData = async (pincode: string) => {
    try {
      const response = await this._homeController.getPinMetadataInfo(pincode);
      return this.mapToPincodeMetadataInfoModel(response);
    } catch (error) {
      console.error('getPinMetadataInfo failed:', error);
      return null;
    }
  };

  private getQCConfig = async (pincode: string) => {
    try {
      const response = await this._homeController.getHCATQCConfig(pincode);
      return this.mapToQCConfigInfoModel(response?.config_data ?? '');
    } catch (error) {
      console.error('getHCATQCConfig failed:', error);
      return null;
    }
  };

  private getPolygonStoreDetails = async (pincodeMetaData: PinInfo) => {
    try {
      const queryParams = {
        lat: pincodeMetaData.lat,
        lng: pincodeMetaData.lon,
      };
      let polygonStoreResponse: any =
        await this._homeController.getPolygonStoreDetails(queryParams);
      return this.mapToPolygonStoreDetailsModel(polygonStoreResponse);
    } catch (error) {
      console.error('getPolygonStoreDetails failed:', error);
      return null;
    }
  };

  private getFyndPromiseDetailsWrapper = async (
    pincode: string,
    pincodeMetaData: PinInfo | null,
    store: JMPolygonStore | null,
  ) => {
    try {
      const cart = await JMDatabaseManager.cart.readDBCart();
      const isLoggedIn = JMDatabaseManager.user.isUserLoggedInFlag();

      const params = {
        stores: store?.store_code
          ? [
              {
                code: store.store_code,
                vertical: 'GROCERIES',
              },
            ]
          : [],
        lat: pincodeMetaData?.lat?.toString() ?? '',
        long: pincodeMetaData?.lon?.toString() ?? '',
        cartId: isLoggedIn ? cart?.cart_id?.toString() ?? '' : '',
      };

      const authHeaders = await this.getBauHeaderDetails();

      const headers = {
        pin: pincode,
        ...authHeaders,
      };

      const promiseApiResponse =
        await this._homeController.getFyndPromiseInfoWrapper(params, headers);
//       const promiseApiResponse = {
//   "status": "success",
//   "pincode": "400001",
//   "data": {
//     "region_info": {},
//     "promise_info": {
//       "customer_details": {
//         "pincode": "400001",
//         "coordinates": {
//           "long": 72.838416529,
//           "lat": 18.933906932
//         }
//       },
//       "channel_id": "",
//       "to_pincode": "400001",
//       "vertical": {
//         "GROCERIES": {
//           "qc": 1,
//           "suspect": 1,
//           "store": "5518",
//           "promise": {
//             "timestamp": {
//               "min": 1751893158,
//               "max": 1751894358
//             },
//             "formatted": {
//               "min": "07 Jul, Monday - 18:29",
//               "max": "07 Jul, Monday - 18:49"
//             }
//           },
//           "display_message": "Free Delivery in 10 to 30 mins",
//           "secondary_display_message": "Free Delivery in 10 to 30 mins",
//           "distance": 0.43247983153514546
//         }
//       },
//       "stormbreaker_uuid": "ee4d90b6-f5b1-459a-98c2-8b42a1764ed4",
//       "identifier": "95a89905-12d5-41ee-bd58-a59d0e7a6218",
//       "success": true
//     }
//   }
// }

      return this.mapToPromiseResponseModel(promiseApiResponse);
    } catch (error) {
      console.error('getFyndPromiseDetails failed:', error);
      return null;
    }
  };

  private async getBauHeaderDetails() {
    const userSession = await JMDatabaseManager.user.getUserSession();
    if (userSession) {
      const userSessionData = JSON.parse(userSession);
      const headers = {
        authtoken: userSessionData?.id,
        userid: userSessionData?.customer_id,
      };
      return headers;
    } else {
      const guestUserSession =
        await JMDatabaseManager.user.getGuestUserSession();
      if (guestUserSession) {
        const userSessionData = JSON.parse(guestUserSession);
        const headers = {
          authtoken: userSessionData?.id ?? '',
          userid: userSessionData?.customer_id ?? '',
        };
        return headers;
      }
    }
  }

  private mapToPincodeMetadataInfoModel = (data: any): PinInfo | null => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        return data;
      case AppSourceType.JM_BAU:
        const parseData = jsonParse<PinInfo>(JSON.stringify(data?.result));
        return parseData;
      default:
        return data;
    }
  };

  private mapToQCConfigInfoModel = (
    data: any,
  ): Record<string, ConfigInfo> | null => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        return data;
      case AppSourceType.JM_BAU:
        const parseData = jsonParse<Record<string, ConfigInfo>>(
          JSON.stringify(data),
        );
        return parseData;
      default:
        return data;
    }
  };

  private mapToPolygonStoreDetailsModel = (
    data: any,
  ): JMPolygonStore[] | null => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        return data;
      case AppSourceType.JM_BAU:
        const parseData = jsonParse<JMPolygonStore[]>(
          JSON.stringify(data?.data),
        );
        return parseData;
      default:
        return data;
    }
  };

  private findQuickCommerceStoreAndServiceability(stores: JMPolygonStore[]) {
    for (const store of stores) {
      for (const serviceability of store.serviceabilities) {
        if (serviceability.type === 'quick_commerce') {
          return {
            store,
            serviceability,
          };
        }
      }
    }
    return null;
  }

  private mapToPromiseResponseModel = (data: any): DataResponse | null => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        return data;
      case AppSourceType.JM_BAU:
        const parseData = jsonParse<DataResponse>(JSON.stringify(data?.data));
        return parseData;
      default:
        return data;
    }
  };
}

export default JMServiceablityNetworkNewController;
