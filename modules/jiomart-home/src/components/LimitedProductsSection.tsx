import {Ji<PERSON><PERSON><PERSON>, JioText} from '@jio/rn_components';
import {JioTypography} from '@jio/rn_components/src/index.types';
import React, {useCallback, useMemo} from 'react';
import {View, StyleSheet, Pressable} from 'react-native';
import CustomMediaRendered from '../../../jiomart-general/src/ui/CustomMediaRendered';
import {getActionUrl} from './WidgetWithProducts';
import {isEqual} from 'lodash';
import { getScreenDim, rh, rw } from '../../../jiomart-common/src/JMResponsive';

interface LimitedProductsSectionProps {
  headerBanner: string;
  headerText: string;
  headerSubText?: string;
  titleUrl: string;
  children: React.ReactNode;
  backgroundColor: string;
  onClick: any;
}

const LimitedProductsSection: React.FC<LimitedProductsSectionProps> = ({
  headerBanner,
  headerText,
  headerSubText,
  titleUrl,
  children,
  backgroundColor,
  onClick,
}) => {
  const handlePress = useCallback(() => {
    onClick({
      actionType: 'T003',
      destination: 'CommonWebViewScreen',
      headerVisibility: 2,
      navigationType: 'push',
      loginRequired: false,
      actionUrl: getActionUrl({
        title_url: titleUrl,
        title: '',
      }),
      headerType: 9,
      shouldShowDeliverToBar: true,
    });
  }, [onClick, titleUrl]);

  const containerStyle = useMemo(
    () => [styles.container, {backgroundColor}],
    [backgroundColor],
  );

  const bannerStyle = useMemo(
    () => [styles.banner, {backgroundColor}],
    [backgroundColor],
  );

  const footerPaddingStyle = useMemo(
    () => ({
      padding: titleUrl ? 0 : 10,
    }),
    [titleUrl],
  );

  return (
    <View style={containerStyle}>
      <View style={[styles.header, headerText ? {paddingHorizontal: 16, paddingTop: 8} :null]}>
        {headerBanner ? (
          // <Image source={{uri: headerBanner}} style={styles.banner} />
          <CustomMediaRendered
            mediaUrl={headerBanner}
            customStyles={[bannerStyle, !headerText ? { width: getScreenDim.width}: null]}
          />
        ) : null}
        <View style={{rowGap: 4}}>
          {headerText ? (
            <JioText
              style={styles.headerText}
              text={headerText}
              appearance={JioTypography.BODY_M_BOLD}
              color={'black'}
            />
          ) : null}
          {headerSubText ? (
            <JioText
              style={styles.headerText}
              text={headerSubText}
              appearance={JioTypography.BODY_XS_BOLD}
              color={'primary_grey_80'}
            />
          ) : null}
        </View>
      </View>
      {children}
      <View style={footerPaddingStyle}>
        {titleUrl && (
          <Pressable style={styles.footer} onPress={handlePress}>
            <JioText
              text="See all items"
              appearance={JioTypography.BODY_S_BOLD}
              color="primary_60"
            />
            <JioIcon ic="IcArrowNext" />
          </Pressable>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 12,
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  banner: {
    width: rw(80),
    height: rh(80),
    resizeMode: 'cover',
    marginLeft: 5,
  },
  headerText: {
    // fontWeight: '900',
  },
  footer: {
    marginTop: 12,
    alignItems: 'center',
    backgroundColor: '#DBE6EC',
    paddingHorizontal: 16,
    paddingVertical: 8,
    gap: 4,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
});

// Custom comparison function using lodash.isEqual
const areEqual = (prevProps, nextProps) => {
  const {children: prevChildren, onClick: prevOnClick, ...prevRest} = prevProps;
  const {children: nextChildren, onClick: nextOnClick, ...nextRest} = nextProps;

  return isEqual(prevRest, nextRest) && prevOnClick === nextOnClick;
};

// Memoized component export
export default React.memo(LimitedProductsSection, areEqual);
