{"name": "jcpJiomart", "version": "0.0.1", "private": true, "license": "MIT", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start --reset-cache", "iosjsbundle": "npx react-native bundle --platform ios --dev false --entry-file index.js --bundle-output ios/main.jsbundle --assets-dest ios/assets && cd ios && zip rnBundle.zip main.jsbundle && rm main.jsbundle", "test": "jest"}, "dependencies": {"@gofynd/fp-signature": "^1.0.1", "@jio/codepush": "^3.0.0", "@jio/rn_components": "^2.0.4", "@jio/rn_webview": "0.2.0-dev.0.2", "@jm/jio-mart": "file:./modules", "@react-native-async-storage/async-storage": "~2.1.2", "@react-native-community/netinfo": "^11.4.1", "@react-native-community/push-notification-ios": "^1.11.0", "@react-native-cookies/cookies": "^6.2.1", "@react-native-firebase/analytics": "^20.5.0", "@react-native-firebase/app": "^20.5.0", "@react-native-firebase/messaging": "^21.2.0", "@react-native-firebase/remote-config": "^20.5.0", "@react-navigation/native": "^6.1.18", "@react-navigation/native-stack": "^6.11.0", "@reduxjs/toolkit": "^2.7.0", "@rn-bridge/react-native-shortcuts": "^1.0.2", "@shopify/flash-list": "^1.7.2", "@sparkfabrik/react-native-idfa-aaid": "^1.2.0", "@tanstack/query-async-storage-persister": "^5.82.0", "@tanstack/react-query": "^5.82.0", "@tanstack/react-query-persist-client": "^5.82.0", "@types/qs": "^6.9.18", "algoliasearch": "4.14.3", "axios": "^1.7.9", "babel-loader": "^8.1.0", "cache": "^3.0.0", "clevertap-react-native": "^3.4.0", "crypto-js": "^4.2.0", "events": "^3.3.0", "expo": "^51.0.8", "firebase": "^10.13.2", "install": "^0.13.0", "lodash.isequal": "^4.5.0", "lottie-react-native": "^6.7.2", "parse-link-header": "^2.0.0", "qs": "^6.14.0", "react": "18.3.1", "react-native": "0.76.7", "react-native-appsflyer": "^6.17.1", "react-native-device-info": "^14.0.4", "react-native-fast-image": "^8.6.3", "react-native-fs": "^2.20.0", "react-native-geolocation-service": "^5.3.1", "react-native-gesture-handler": "~2.20.0", "react-native-google-places-sdk": "^0.1.4", "react-native-haptic-feedback": "^2.3.3", "react-native-image-picker": "^8.2.1", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-linear-gradient": "^2.8.3", "react-native-maps": "1.15.7", "react-native-mmkv": "^2.9.2", "react-native-pager-view": "~6.6.1", "react-native-permissions": "^5.3.0", "react-native-reanimated": "3.16.7", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "^4.14.1", "react-native-screens": "~3.35.0", "react-native-svg": "~15.11.1", "react-native-video": "^6.16.1", "react-native-vision-camera": "^4.7.0", "react-native-webview": "^13.13.2", "react-redux": "^9.2.0", "redux": "^5.0.1", "redux-saga": "^1.3.0", "zustand": "^5.0.3"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.76.7", "@react-native/eslint-config": "0.76.7", "@react-native/metro-config": "0.76.7", "@react-native/typescript-config": "0.76.7", "@tsconfig/react-native": "^3.0.0", "@types/crypto-js": "^4.2.2", "@types/lodash.isequal": "^4.5.8", "@types/node": "^22.7.2", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "eslint-plugin-unused-imports": "^4.1.3", "jest": "^29.6.3", "prettier": "2.8.8", "react-devtools": "^5.3.2", "react-native-svg-transformer": "^1.5.1", "react-test-renderer": "18.3.1", "typescript": "5.0.4"}, "engines": {"node": ">=18", "npm": ">=9"}}