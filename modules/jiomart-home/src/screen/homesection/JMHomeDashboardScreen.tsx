import React, {useEffect, useMemo, useRef, useState, useCallback} from 'react';
import {View, StyleSheet, SafeAreaView, FlatList, Image} from 'react-native';
import {
  AppScreens,
  type ScreenProps,
} from '../../../../jiomart-common/src/JMAppScreenEntry';
import ScreenSlot, {
  createEventFinalBean,
  DeeplinkHandler,
} from '../../../../jiomart-general/src/ui/JMScreenSlot';
import useJMHomeScreenController from '../../controller/useJMHomeScreenController';
import {JioButton, JioText} from '@jio/rn_components';
import {JioTypography} from '@jio/rn_components/src/index.types';
import CustomMediaRendered from '../../../../jiomart-general/src/ui/CustomMediaRendered';
import TopNavList from '../../../../jiomart-main/src/components/TopNavList';
import {getBaseURL} from '../../../../jiomart-networkmanager/src/JMEnvironmentConfig';
import {navigateTo} from '../../../../jiomart-general/src/navigation/JMNavGraph';
import {SectionRenderer} from '../../components/SectionRenderer';
import ProductListWithHeadingShimmer from '../../components/ProductListWithHeadingShimmer';
import JMHomeV1View from '../../views/JMHomeV1View';
import {JMHomeDBManager} from '../../db/JMHomeDBManage';
import {QueryClient} from '@tanstack/react-query';
import {createAsyncStoragePersister} from '@tanstack/query-async-storage-persister';
import {
  persistQueryClient,
  PersistQueryClientProvider,
} from '@tanstack/react-query-persist-client';
import GlobalBottomSheet from '../../../../jiomart-main/src/features/StartUp/GlobalBottomSheet';
import BottomSheet from '../../../../jiomart-general/src/ui/BottomSheet/BottomSheet';
import JMQuickBtmSheet from '../../BottomSheet/JMQuickBtmSheet';
import {rh, rw} from '../../../../jiomart-common/src/JMResponsive';
import {useFocusEffect} from '@react-navigation/native';
import {JMSharedViewModel} from '../../../../jiomart-common/src/JMSharedViewModel';
import useGlobalBottomSheetController from '../../../../jiomart-main/src/features/StartUp/controllers/useGlobalBottomSheetController';
import {
  addStringPref,
  getPrefString,
  removeStringPref,
} from '../../../../jiomart-common/src/JMAsyncStorageHelper';
import {SPECIAL_IDENTIFIERS} from '../../utils/HomeUtils';
import {TOP_CATERORIES_STATES} from '../../utils/HomeSectionUtils';
import {extractLastPathSegment} from '../../utils/HomeSectionUtils';
import {JMLogger} from '../../../../jiomart-common/src/utils/JMLogger';
import useAppStatePermissionSync from '../../../../jiomart-main/src/controllers/useAppStatePermissionSync';
import { logAnalyticsEvent } from '../../../../jiomart-common/src/JMAnalyticsUtility';

export type JMHomeScreenProps = ScreenProps<
  typeof AppScreens.HOME_DASHBOARD_SCREEN
>;

export type UseJMHomeScreenProps = JMHomeScreenProps;

export const dummyOnion = `${getBaseURL()}/images/product/original/${'590003515/onion-1-kg-product-images-o590003515-p590003515-0-202408070949.jpg'}`;
export const dummyPotato = `${getBaseURL()}/images/product/original/${'590003516/potato-1-kg-product-images-o590003516-p590003516-0-202408070949.jpg'}`;

const MAX_CACHED_QUERIES = 4;
const DISABLE_CACHE = false;

// Create a persistent QueryClient for this hook
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 0,
      gcTime: DISABLE_CACHE ? 0 : 40 * 60 * 1000,
      retry: 0,
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
      // Add error handling to prevent crashes
      throwOnError: false,
    },
  },
  // Add global error handler
  mutationCache: undefined,
  queryCache: undefined,
});
const asyncStoragePersister = createAsyncStoragePersister({
  storage: {
    setItem: (key, value) => {
      // console.log('[Persister] setItem', key);
      return addStringPref(key, value);
    },
    getItem: key => {
      // console.log('[Persister] getItem', getPrefString(key));
      return getPrefString(key) || null;
    },
    removeItem: key => {
      // console.log('[Persister] removeItem', key);
      return removeStringPref(key);
    },
  },
  key: 'JIO_MART_HOMEPAGE_CACHE',
  serialize: data => {
    try {
      const filteredQueries = data.clientState.queries
        .slice(0, MAX_CACHED_QUERIES)
        .map(query => ({
          ...query,
          state: {
            ...query.state,
            data:
              query.state.data &&
              typeof query.state.data === 'object' &&
              'pages' in query.state.data
                ? {
                    ...query.state.data,
                    pages: Array.isArray((query.state.data as any).pages)
                      ? (query.state.data as any).pages.slice(0, 1) // Keep only the first page
                      : [], // Fallback to empty array
                    pageParams: Array.isArray(
                      (query.state.data as any).pageParams,
                    )
                      ? (query.state.data as any).pageParams.slice(0, 1) // Keep only the first pageParam
                      : [1], // Fallback to default pageParam
                  }
                : query.state.data,
            dataUpdateCount: 1, // Reflect single page update
          },
        }));
      return JSON.stringify({
        ...data,
        clientState: {...data.clientState, queries: filteredQueries},
      });
    } catch (error) {
      console.error('[Persister] Serialization failed:', error);
      // Return minimal valid data structure
      return JSON.stringify({
        clientState: {queries: []},
        buster: data.buster || 'v1',
      });
    }
  },
  deserialize: data => {
    try {
      return JSON.parse(data);
    } catch (error) {
      console.error('[Persister] Deserialization failed:', error);
      // Return empty cache structure to prevent crashes
      return {
        clientState: {queries: []},
        buster: 'v1',
      };
    }
  },
});
persistQueryClient({
  queryClient,
  persister: asyncStoragePersister,
  maxAge: DISABLE_CACHE ? 0 : 1000 * 60 * 60, // 1 hour for better cache retention
  buster: 'v1',
  dehydrateOptions: {
    shouldDehydrateQuery: query => {
      // Don't persist queries that are in error or loading state
      if (query.state.status === 'error' || query.state.status === 'pending') {
        console.log(
          `[Persister] Skipping ${query.state.status} query:`,
          query.queryKey,
        );
        return false;
      }

      // Only persist queries with 'persist' in their key (i.e., first tab)
      return !(
        Array.isArray(query.queryKey) &&
        (query.queryKey.includes('JMHomeDashboardConfigurationV1') ||
          query.queryKey.includes('JMRNCommanConfigsV1') ||
          query.queryKey.includes('GET_ADDRESS') ||
          query.queryKey.includes('non-persist') ||
          query.queryKey.includes('JMHeaderConfigurationV1'))
      );
    },
  },
});

const JMHomeScreenImpl = (props: JMHomeScreenProps) => {
  return (
    <PersistQueryClientProvider
      client={queryClient}
      persistOptions={{persister: asyncStoragePersister}}>
      <JMHomeDashboardScreen {...props} />
    </PersistQueryClientProvider>
  );
};

const JMHomeDashboardScreen = (props: JMHomeScreenProps) => {
  const {
    navigation,
    route,
    navigationBean,
    eventTriggerBean,
    homepageTabSectionData,
    isHomeDataLoading,
    successHomeDataResponse,
    selectedFlow,
    currentSelectedSlug,
    homepageTabSectionState,
    handleTabChange,
    getCurrentSelectedSlug,
    hasNextPage,
    isFetchingNextPage,
    homepageQueryData,
    homepageError,
    flattenedData,
    homeConfig,
    showBlockQcBannerHome,
    qcBannerHomeConditionData,
    conditionToShowQcBannerHome,
    setConditionToShowQcBannerHome,
    activeSpecialIndex,
    isWidgetLoading,
    setIsWidgetLoading,
    setActiveSpecialIndex,
    fetchNextPage,
    refetchHomepageData,
    loadMoreHomepageData,
    commonConfig,
    getFilteredTopCategoriesData,
    setQCDetails,
    setSelectedOption,
    isBSNonCancellable,
    setBSNonCancellable,
    showQuickBtmSheet,
    openQuickBtmSheet,
    closeQuickBtmSheet,
    quickBottomSheetIdentifierBean,
    newTagImage,
    adMetaData,
    loadHomepageData,
    setBean,
    setServiceabilityCalled,
  } = useJMHomeScreenController(props);
  const {checkAndOpenNextSheet} = useGlobalBottomSheetController();
  const flatListRef = useRef<FlatList>(null);
  useAppStatePermissionSync();

  // Create ref for DeliverToBar scroll handling
  const deliverToBarRef = useRef<any>(null);
  const isUserScrolling = useRef(false);

  const handleScrollBeginDrag = () => {
    JMLogger.log('handleScrollBeginDrag');
    isUserScrolling.current = true;
  };
  const handleScrollEndDrag = () => {
    JMLogger.log('handleScrollEndDrag');
    isUserScrolling.current = false;
  };
  // Callback to receive DeliverToBar ref from ScreenSlot
  const onDeliverToBarRef = useCallback((ref: any) => {
    deliverToBarRef.current = ref;
  }, []);

  // Add optimistic tab selection state for immediate UI response
  const [optimisticSelectedSlug, setOptimisticSelectedSlug] = useState<
    string | null
  >(null);
  const [isTabSwitching, setIsTabSwitching] = useState(false);

  // Add this temporarily to debug
  // useEffect(() => {
  //   // Clear cache and restart
  //   queryClient.clear();
  //   console.log('[ReactQuery] Cache cleared');
  // }, []);

  // / Measure MMKV read performance
  // useEffect(() => {
  //   const start = performance.now();
  //   const value = storage.getString('JIO_MART_HOMEPAGE_CACHE');
  //   console.log('cahce value---am', value);
  //   const duration = performance.now() - start;
  //   console.log(`[MMKV] Read took ${duration}ms`);
  //   if (value) {
  //     console.log(`[MMKV] Cache size: ${value.length} bytes`);
  //   }
  // }, [currentSelectedSlug]);

  useEffect(() => {
    console.log('flattenedData---->', flattenedData);
  }, [flattenedData]);

  useFocusEffect(
    useCallback(() => {
      if (!JMSharedViewModel.Instance.getNavigationData()) {
        checkAndOpenNextSheet();
      }
    }, []),
  );

  // Memoize setBSNonCancellableValue for ScreenSlot
  const setBSNonCancellableValue = useCallback(
    (value: boolean) => {
      if (isBSNonCancellable !== value) {
        setBSNonCancellable(value);
      }
    },
    [isBSNonCancellable, setBSNonCancellable],
  );

  // Memoize openPincodeBottomSheet for GlobalBottomSheet
  const openPincodeBottomSheet = useCallback(
    (isNonCancellable: boolean, openDeliverToBarPincodeBtmSheet: any) => {
      setBSNonCancellable(isNonCancellable);
      openDeliverToBarPincodeBtmSheet();
    },
    [setBSNonCancellable],
  );

  // Memoize onClose for JMQuickBtmSheet
  const onQuickBtmSheetClose = useCallback(() => {
    closeQuickBtmSheet();
  }, [closeQuickBtmSheet]);

  const onWidgetLoadingChange = useCallback(
    (index: number, loading: boolean) => {
      setIsWidgetLoading(loading);
      setActiveSpecialIndex(loading ? index : null);
    },
    [setIsWidgetLoading, setActiveSpecialIndex],
  );

  // Memoize onPress for JMHomeV1View
  const onPressJMHomeV1View = useCallback(
    async (item: any) => {
      if (
        qcBannerHomeConditionData?.city &&
        typeof qcBannerHomeConditionData.city === 'string'
      ) {
        await JMHomeDBManager.updateCityName(
          (qcBannerHomeConditionData.city as string).toUpperCase(),
        );
      }
      setConditionToShowQcBannerHome(false);
      setSelectedOption(item?.value);
      if (item?.value === 'Quick') {
        setQCDetails((prev: any) => {
          return {
            ...prev,
            qcMessage: prev.tempQcMessage,
            tempQcMessage: '',
            scheduledMessage:
              commonConfig?.quickCommerceConfig?.scheduledDeliveryMessage,
            journey: commonConfig?.quickCommerceConfig?.quickDeliveryKey,
          };
        });
      } else {
        setQCDetails((prev: any) => {
          return {
            ...prev,
            qcMessage: prev.tempQcMessage,
            tempQcMessage: '',
            scheduledMessage:
              commonConfig?.quickCommerceConfig?.scheduledDeliveryMessage,
            journey: commonConfig?.quickCommerceConfig?.scheduledDeliveryKey,
          };
        });
      }
      setBean(prev => {
        return {
          ...prev,
          headerType: 18,
        };
      });
      setServiceabilityCalled(true);
    },
    [qcBannerHomeConditionData, setSelectedOption, setQCDetails, commonConfig],
  );

  // Enhanced handleTabChange with optimistic updates
  const handleTabChangeOptimistic = useCallback(
    (tabItem: any) => {
      console.log('tab item', tabItem);
      const selectedSlug = extractLastPathSegment(tabItem?.banner_link ?? '');

      // Immediate UI update - set optimistic state
      setOptimisticSelectedSlug(selectedSlug);
      setIsTabSwitching(true);

      // Use requestAnimationFrame to defer heavy operations after UI update
      // requestAnimationFrame(() => {
      // Reset widget loading states immediately for smooth UX
      setActiveSpecialIndex(null);
      setIsWidgetLoading(false);

      // Defer the actual data loading to next frame to avoid blocking UI
      // setTimeout(() => {
      handleTabChange(tabItem);
      // }, 0);
      // });
    },
    [handleTabChange, setActiveSpecialIndex, setIsWidgetLoading],
  );

  // Reset optimistic state when data is loaded
  useEffect(() => {
    if (flattenedData.length > 0 && isTabSwitching) {
      setIsTabSwitching(false);
      setOptimisticSelectedSlug(null);

      // Enhanced smooth scroll to top
      if (flatListRef.current) {
        // Small delay to ensure the new content is rendered
        setTimeout(() => {
          flatListRef.current?.scrollToIndex({
            index: 0,
            animated: true,
          });
        }, 400);
      }
    }
  }, [flattenedData, isTabSwitching]);

  // Get the display slug for TopNavList (optimistic or actual)
  const getDisplaySelectedSlug = useCallback(() => {
    if (optimisticSelectedSlug) {
      return optimisticSelectedSlug;
    }
    return getCurrentSelectedSlug();
  }, [optimisticSelectedSlug, getCurrentSelectedSlug]);

  const topBannerUI = () => {
    return (
      <>
        {homepageTabSectionState != undefined && (
          <View>
            <TopNavList
              onItemSelect={handleTabChangeOptimistic}
              itemWidth={40}
              height={90}
              data={getFilteredTopCategoriesData}
              selectedItemId={getDisplaySelectedSlug()}
              sectionState={homepageTabSectionState}
              newCategoryText={
                homeConfig?.homeConfig?.newlyAddedCategroyText ?? undefined
              }
            />
          </View>
        )}
      </>
    );
  };

  // Memoize Footer component for loading indicator
  const renderFooter = useCallback(() => {
    return !isWidgetLoading && (hasNextPage || isFetchingNextPage) ? (
      <ProductListWithHeadingShimmer rowCount={1} />
    ) : null;
  }, [isWidgetLoading, hasNextPage, isFetchingNextPage]);

  const onClick = useCallback(
    (cta: any) => navigateTo(cta, navigation),
    [navigation.navigate], // Only depend on the navigate function, not the entire navigation object
  );

  // Memoize the renderItem function to prevent FlatList re-renders
  const renderFlatListItem = useCallback(
    ({item, index}: {item: any; index: number}) => {
      const identifier = item.data.section_details?.identifier;
      const isSpecial = Object.keys(SPECIAL_IDENTIFIERS).includes(identifier);
      console.log('is special am>', isSpecial, activeSpecialIndex, index);
      // If a special section is loading, only render items up to and including it
      if (activeSpecialIndex !== null && index > activeSpecialIndex) {
        return null;
      }

      // If this is a special section, pass the loading callback and special props
      if (isSpecial) {
        return (
          <SectionRenderer
            item={{
              currentSelectedSlug: currentSelectedSlug,
              selectedFlow: selectedFlow,
              ...item?.data,
            }}
            onClick={onClick}
            onWidgetLoadingChange={loading =>
              onWidgetLoadingChange(index, loading)
            }
            navigation={navigation}
            route={route}
            adMetaData={adMetaData}
          />
        );
      }

      // Otherwise, render generic section WITHOUT special props to prevent re-renders
      return (
        <SectionRenderer
          item={item?.data}
          onClick={onClick}
          navigation={navigation}
          route={route}
          adMetaData={adMetaData}
        />
      );
    },
    [
      activeSpecialIndex,
      currentSelectedSlug,
      selectedFlow,
      onClick,
      navigation,
      route,
      onWidgetLoadingChange,
    ],
  );

  // Memoize keyExtractor to prevent FlatList re-renders
  const keyExtractor = useCallback((item: any) => item.id, []);

  // Add ref to prevent multiple onEndReached calls
  const onEndReachedCalledRef = useRef(false);

  // Memoize onEndReached to prevent FlatList re-renders
  const onEndReached = useCallback(() => {
    if (onEndReachedCalledRef.current) {
      return;
    }
    onEndReachedCalledRef.current = true;
    loadMoreHomepageData();

    // Reset after 2 seconds to allow future calls
    setTimeout(() => {
      onEndReachedCalledRef.current = false;
    }, 2000);
  }, [loadMoreHomepageData]);

  // Handle scroll events for DeliverToBar animation
  const handleScroll = useCallback((event: any) => {
    if (!isUserScrolling.current) return;
    const scrollY = event.nativeEvent.contentOffset.y;
    // requestAnimationFrame(() => {
    if (deliverToBarRef.current?.onScroll) {
      deliverToBarRef.current.onScroll(scrollY);
    }
    // });
  }, []);

  useEffect(() => {
    if (deliverToBarRef.current?.onScroll) {
      handleScrollEndDrag();
      deliverToBarRef.current.onScroll(1, true);
    }
  }, [selectedFlow, isTabSwitching]);

  // Memoize extraData to prevent unnecessary re-renders
  const extraData = useMemo(
    () => [selectedFlow, currentSelectedSlug, activeSpecialIndex],
    [selectedFlow, currentSelectedSlug, activeSpecialIndex],
  );

  // Memoize filtered data to prevent filter operation on every render
  const filteredFlattenedData = useMemo(
    () => flattenedData.filter(f => f?.type === 'component'),
    [flattenedData],
  );
  // console.log(
  //   '🚀 ~ filteredFlattenedData:',
  //   JSON.stringify(filteredFlattenedData),
  // );

  const errorUi = (message: string) => {
    const errorImageUrl =
      homeConfig?.errorStateConfig?.image?.url ??
      'https://myjiostatic.cdn.jio.com/JioMart/BAU_RN/rn/sit/common/error_image.png';
    const width = rw(homeConfig?.errorStateConfig?.image?.width ?? 200);
    const height = rh(homeConfig?.errorStateConfig?.image?.height ?? 200);
    const errorTitle = homeConfig?.errorStateConfig?.title ?? 'Ooops!';
    const errorMessage =
      homeConfig?.errorStateConfig?.message ?? message
        ? message
        : 'Slow or No internet connection. Please check your internet settings.';

    return (
      <View style={styles.errorContainer}>
        <View style={styles.errorContent}>
          <View style={styles.errorIllustrationContainer}>
            <CustomMediaRendered
              mediaUrl={errorImageUrl}
              width={width}
              height={height}
              viewBox={`0 0 ${width} ${height}`}
              imageResizeMode="contain"
              customStyles={{
                width: width,
                height: height,
              }}
            />
          </View>

          {/* Error Title */}
          <JioText
            text={errorTitle}
            appearance={JioTypography.HEADING_XS}
            color="primary_grey_100"
            textAlign="center"
            style={styles.errorTitle}
          />

          {/* Error Message */}
          <JioText
            text={errorMessage}
            appearance={JioTypography.BODY_XS}
            color="primary_grey_80"
            textAlign="center"
            style={styles.errorMessage}
          />
        </View>

        <View style={styles.horizontalLine} />

        {/* Refresh Button with top line */}
        <View style={styles.refreshButtonContainer}>
          <JioButton
            title={homeConfig?.errorStateConfig?.refreshButtonText ?? 'Refresh'}
            onClick={() => {
              console.log('refresh button clicked');
              loadHomepageData();
            }}
            style={styles.refreshButton}
          />
        </View>
      </View>
    );
  };

  const mainUI = () => {
    return (
      <View style={{flex: 1}}>
        {/* {Array.isArray(flattenedData) && !flattenedData.length && (
          <ProductListWithHeadingShimmer />
        )} */}

        {/* Loading State - Show shimmer during tab switching or initial loading */}
        {isTabSwitching ||
        (isHomeDataLoading &&
          !homepageQueryData &&
          flattenedData.length === 0) ||
        (homepageTabSectionState === TOP_CATERORIES_STATES.LOADING &&
          !homepageTabSectionData) ? (
          <ProductListWithHeadingShimmer />
        ) : null}

        {/* Error State */}
        {homepageError &&
          !isTabSwitching &&
          errorUi(homepageError?.message ?? '')}

        {/* Success State - Show paginated data with FlatList */}
        {homepageQueryData?.pages && !isTabSwitching && (
          <FlatList
            ref={flatListRef}
            data={filteredFlattenedData}
            renderItem={renderFlatListItem}
            keyExtractor={keyExtractor}
            ListFooterComponent={renderFooter}
            onEndReached={onEndReached}
            onEndReachedThreshold={0.7}
            onScroll={handleScroll}
            onScrollBeginDrag={handleScrollBeginDrag}
            scrollEventThrottle={16}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.flatListContent}
            extraData={extraData}
            maxToRenderPerBatch={5}
            initialNumToRender={2}
            nestedScrollEnabled={false}
            maintainVisibleContentPosition={{
              minIndexForVisible: 0,
              autoscrollToTopThreshold: 10,
            }}
          />
        )}
      </View>
    );
  };

  console.log('JM HOME DASHBOARD RE-RENDER');

  const bottomSheetContent = ({openDeliverToBarPincodeBtmSheet}: any) => (
    <>
      <GlobalBottomSheet
        navigation={navigation}
        openPincodeBottomSheet={isNonCancellable => {
          openPincodeBottomSheet(
            isNonCancellable,
            openDeliverToBarPincodeBtmSheet,
          );
        }}
        openQuickBtmSheet={openQuickBtmSheet}
        quickBottomSheetIdentifierBean={quickBottomSheetIdentifierBean}
      />
      <BottomSheet
        visible={showQuickBtmSheet}
        enableKeyboarAvoidingView
        onBackDropClick={closeQuickBtmSheet}
        onDrag={closeQuickBtmSheet}
        disabledBackDropClick={true}
        headerComponent={
          <Image
            source={{
              uri: newTagImage,
            }}
            style={styles.newTagImageStyle}
            resizeMode="cover"
          />
        }>
        <JMQuickBtmSheet
          quickBottomSheetIdentifierBean={
            quickBottomSheetIdentifierBean &&
            Array.isArray(quickBottomSheetIdentifierBean)
              ? quickBottomSheetIdentifierBean[0]
              : undefined
          }
          onClose={onQuickBtmSheetClose}
          onGotIt={() => {
            closeQuickBtmSheet();
            // logAnalyticsEvent(
            //   quickCommerceConfig?.analyticEvent
            //     ?.quickBottomSheet_gotIt_buttonClick,
            // );
          }}
        />
      </BottomSheet>
    </>
  );

  // if (useIsRestoring()) {
  //   return <ProductListWithHeadingShimmer />;
  // }

  return (
    <DeeplinkHandler
      navigationBean={navigationBean}
      navigation={navigation}
      children={bean => (
        <ScreenSlot
          navigationBean={createEventFinalBean(bean, eventTriggerBean)}
          navigation={navigation}
          bottomsheetDismissable={
            {disabledBackDropClick: isBSNonCancellable} as any
          }
          bottomSheetContent={bottomSheetContent}
          setBSNonCancellableValue={setBSNonCancellableValue}
          onDeliverToBarRef={onDeliverToBarRef}
          shouldShowNoInternetToast={false}
          children={_ => {
            return (
              <SafeAreaView style={styles.wrapper}>
                {conditionToShowQcBannerHome ? (
                  <JMHomeV1View
                    items={
                      homeConfig?.bannerInfo[showBlockQcBannerHome.current]
                        ?.items
                    }
                    onPress={onPressJMHomeV1View}
                  />
                ) : (
                  <>
                    {topBannerUI()}
                    {mainUI()}
                  </>
                )}
              </SafeAreaView>
            );
          }}
        />
      )}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff', // Mart Default background for splash screen
    paddingHorizontal: 16,
    paddingVertical: 32,
  },
  wrapper: {flex: 1, backgroundColor: '#ffffff'},

  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  errorContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  errorIllustrationContainer: {
    marginBottom: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorImage: {
    alignSelf: 'center',
  },
  errorTitle: {
    marginBottom: 16,
  },
  errorMessage: {
    paddingHorizontal: 20,
    marginBottom: 40,
  },
  refreshButtonContainer: {
    paddingHorizontal: 24,
    paddingBottom: 24,
  },
  horizontalLine: {
    height: 1,
    backgroundColor: '#E0E0E0',
    marginBottom: 16,
  },
  refreshButton: {
    width: '100%',
    height: rh(48),
  },
  flatListContent: {
    paddingBottom: 20,
  },
  paginationInfo: {
    backgroundColor: '#f5f5f5',
    padding: 16,
    marginBottom: 16,
    borderRadius: 8,
  },
  infoText: {
    fontSize: 14,
    color: '#333',
    marginBottom: 4,
  },
  pageContainer: {
    backgroundColor: '#fff',
    borderWidth: 2,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  pageTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1976d2',
    marginBottom: 12,
  },
  pageMetadata: {
    backgroundColor: '#f8f9fa',
    padding: 12,
    borderRadius: 6,
    marginBottom: 16,
  },
  metadataText: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2,
  },
  componentItem: {
    backgroundColor: '#fafafa',
    borderLeftWidth: 4,
    borderLeftColor: '#4caf50',
    padding: 12,
    marginBottom: 8,
    borderRadius: 4,
  },
  componentTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 6,
  },
  componentDetails: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2,
  },
  loadingNextContainer: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  newTagImageStyle: {
    width: rw(80),
    height: rh(36),
    position: 'absolute',
    top: rh(-18),
    alignSelf: 'center',
  },
});

export default JMHomeScreenImpl;
