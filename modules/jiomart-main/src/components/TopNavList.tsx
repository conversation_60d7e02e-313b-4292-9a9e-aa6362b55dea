import {
  FlatList,
  Pressable,
  StyleSheet,
  View,
  ActivityIndicator,
  Image,
  Animated,
  Dimensions,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import WebView from 'react-native-webview';
import {JioText} from '@jio/rn_components';
import {JioTypography} from '@jio/rn_components/src/index.types';
import {
  extractLastPathSegment,
  TOP_CATERORIES_STATES,
} from '../../../jiomart-home/src/utils/HomeSectionUtils';
import Skeleton from '../../../jiomart-general/src/ui/Skeleton';
import {HomeTopCategoriesTab} from '../../../jiomart-networkmanager/src/models/home/<USER>';
import {isNullOrUndefinedOrEmpty} from '../../../jiomart-common/src/JMObjectUtility';
import React, {useRef, useEffect, useState} from 'react';

interface TopNavListProps {
  data: HomeTopCategoriesTab[];
  itemWidth?: number;
  height?: number;
  onItemSelect?: (item: HomeTopCategoriesTab) => void;
  selectedItemId: string;
  sectionState: TOP_CATERORIES_STATES | undefined;
  newCategoryText?: string;
}

// Helper function to check if URL is a GIF
const isGifUrl = (url: string): boolean => {
  return url?.toLowerCase().includes('.gif') || false;
};

const renderItem = ({
  item,
  itemWidth,
  isSelected,
  onPress,
  newCategoryText,
}: {
  item: HomeTopCategoriesTab;
  itemWidth: number;
  isSelected: boolean;
  onPress: (item: any) => void;
  newCategoryText?: string;
}) => {
  // Check if the image URL is a GIF
  const isGif = isGifUrl(item.banner_url);

  // Boolean condition for showing the badge
  const showBadge = item?.tag === 'NEW';
  const deliveryTimeText = item?.promise ?? undefined;

  return (
    <Pressable
      onPress={onPress}
      key={item.banner_link + item.banner_name}
      style={styles.itemContainer}>
      <View style={styles.imageContainer}>
        {/* Conditional rendering: WebView for GIFs, FastImage for others */}
        {isGif ? (
          <View
            style={[
              styles.image,
              {width: itemWidth, height: itemWidth, overflow: 'hidden'},
            ]}>
            <WebView
              source={{uri: item.banner_url}}
              style={{width: itemWidth, height: itemWidth}}
              scrollEnabled={false}
              showsHorizontalScrollIndicator={false}
              showsVerticalScrollIndicator={false}
              scalesPageToFit={true}
              startInLoadingState={false}
              javaScriptEnabled={false}
            />
          </View>
        ) : (
          <FastImage
            source={{uri: item.banner_url}}
            style={[styles.image, {width: itemWidth, height: itemWidth}]}
            resizeMode={FastImage.resizeMode.cover}
          />
        )}

        {showBadge && (
          <View style={styles.badgeContainer}>
            <JioText
              text={newCategoryText ? newCategoryText : 'OLD'}
              appearance={JioTypography.BODY_3XS}
              color="sparkle_60"
            />
          </View>
        )}
      </View>
      <JioText
        appearance={JioTypography.BODY_3XS}
        text={item.banner_name}
        maxLines={
          !isNullOrUndefinedOrEmpty(deliveryTimeText) && item.quick === 1
            ? 1
            : 2
        }
        minLines={1}
        style={[styles.title, isSelected && styles.selectedText]}
      />
      {!isNullOrUndefinedOrEmpty(deliveryTimeText) && item.quick === 1 && (
        <JioText
          appearance={JioTypography.BODY_3XS_BOLD}
          text={deliveryTimeText ?? ''}
          maxLines={1}
          minLines={1}
          style={[styles.title, isSelected && styles.selectedText]}
        />
      )}
    </Pressable>
  );
};

const renderLoadingState = (height: number, itemWidth: number) => {
  // Create array of skeleton items to mimic the original UI
  const skeletonItems = Array.from({length: 6}, (_, index) => (
    <View key={index} style={[styles.itemContainer]}>
      <Skeleton width={'100%'} height={'100%'} style={[styles.skeletonImage]} />
    </View>
  ));

  return (
    <View style={[styles.loadingContainer, {height}]}>
      <View style={styles.skeletonFlatListContent}>{skeletonItems}</View>
    </View>
  );
};

const TopNavList = ({
  data,
  itemWidth = 40,
  height = 85,
  onItemSelect,
  selectedItemId,
  sectionState,
  newCategoryText,
}: TopNavListProps) => {
  const ITEM_WIDTH = 72; // Width of each item including gap
  const GAP = 8; // Gap between items
  const PADDING_HORIZONTAL = 16;

  const scrollOffset = useRef(new Animated.Value(0)).current;
  const animatedSelectedPosition = useRef(
    new Animated.Value(PADDING_HORIZONTAL),
  ).current;
  const [selectedIndex, setSelectedIndex] = useState(0);
  const flatListRef = useRef<FlatList>(null);

  // Update selectedIndex when selectedItemId changes (for external updates)
  useEffect(() => {
    const index = data.findIndex(
      item => extractLastPathSegment(item.banner_link) === selectedItemId,
    );
    if (index !== -1 && index !== selectedIndex) {
      setSelectedIndex(index);
      // Update position immediately for external updates (without animation)
      const targetPosition = index * (ITEM_WIDTH + GAP) + PADDING_HORIZONTAL;
      animatedSelectedPosition.setValue(targetPosition);
      
      // If switching to first item (index 0), scroll to beginning with animation
      if (index === 0 && flatListRef.current) {
        flatListRef.current.scrollToOffset({
          offset: 0,
          animated: false, // Use animated scroll for smooth transition
        });
      }
    }
  }, [selectedItemId, data, selectedIndex, animatedSelectedPosition]);

  const handleItemPress = React.useCallback(
    (item: any) => {
      // Find the index of the clicked item
      const index = data.findIndex(
        dataItem =>
          extractLastPathSegment(dataItem.banner_link) ===
          extractLastPathSegment(item.banner_link),
      );

      if (index !== -1 && index !== selectedIndex) {
        // Start animations immediately on click
        setSelectedIndex(index);

        // Calculate the target position for sliding background
        const targetPosition = index * (ITEM_WIDTH + GAP) + PADDING_HORIZONTAL;

        // Calculate FlatList scroll offset
        const itemPosition = index * (ITEM_WIDTH + GAP);
        const screenWidth = Dimensions.get('window').width;
        const centerOffset =
          screenWidth / 2 - ITEM_WIDTH / 2 - PADDING_HORIZONTAL;
        const scrollOffset = Math.max(0, itemPosition - centerOffset);

        // Run both animations simultaneously on click
        Animated.parallel([
          // Animate sliding background position
          Animated.timing(animatedSelectedPosition, {
            toValue: targetPosition,
            duration: 200,
            useNativeDriver: true,
          }),
          // Dummy animation for synchronization
          Animated.timing(new Animated.Value(0), {
            toValue: 1,
            duration: 200,
            useNativeDriver: false,
          }),
        ]).start();

        // Trigger FlatList scroll at the same time
        if (flatListRef.current) {
          flatListRef.current.scrollToOffset({
            offset: scrollOffset,
            animated: true,
          });
        }
      }

      onItemSelect?.(item);
      // navigateTo(
      //   navBeanObj({
      //     source: '',
      //     destination: 'CommonWebViewScreen',
      //     actionType: 'T003',
      //     headerType: 1,
      //     actionUrl: `${getBaseURL()}/${item.banner_link}`,
      //   }),
      //   navigation,
      // );
    },
    [onItemSelect, data, selectedIndex, animatedSelectedPosition],
  );

  const handleScroll = Animated.event(
    [{nativeEvent: {contentOffset: {x: scrollOffset}}}],
    {useNativeDriver: true},
  );

  // Memoize the renderItem function to prevent unnecessary re-renders
  const renderTabItem = React.useCallback(
    ({item}: {item: HomeTopCategoriesTab}) => {
      const isSelected =
        extractLastPathSegment(item.banner_link) === selectedItemId;
      return renderItem({
        item,
        itemWidth,
        isSelected,
        onPress: () => handleItemPress(item),
        newCategoryText,
      });
    },
    [itemWidth, selectedItemId, handleItemPress, newCategoryText],
  );

  // Memoize keyExtractor
  const keyExtractor = React.useCallback(
    (item: HomeTopCategoriesTab) => item.banner_link + item.banner_name,
    [],
  );

  // Trigger onItemSelect for initial selection
  // useEffect(() => {
  //   if (data?.[0] && onItemSelect) {
  //     onItemSelect(data[0]);
  //   }
  // }, []);

  const containerStyle = {
    backgroundColor: 'white',
    height: height,
    borderBottomWidth: 1,
    borderColor: '#0000001A',
  };

  const renderContent = () => {
    switch (sectionState) {
      case TOP_CATERORIES_STATES.LOADING:
        return renderLoadingState(height, itemWidth);

      case TOP_CATERORIES_STATES.SHOW_TOPBAR:
        return (
          <View style={styles.containerWithBackground}>
            {/* Absolutely positioned background that follows selected item */}
            <Animated.View
              style={[
                styles.slidingBackground,
                {
                  width: ITEM_WIDTH,
                  height: height,
                  transform: [
                    {
                      translateX: Animated.subtract(
                        animatedSelectedPosition,
                        scrollOffset,
                      ),
                    },
                  ],
                },
              ]}
            />
            <Animated.View
              style={[
                styles.slidingBackgroundBottom,
                {
                  width: ITEM_WIDTH,
                  height: 4,
                  bottom: -1,
                  transform: [
                    {
                      translateX: Animated.subtract(
                        animatedSelectedPosition,
                        scrollOffset,
                      ),
                    },
                  ],
                },
              ]}
            />
            <Animated.FlatList
              ref={flatListRef}
              data={data}
              renderItem={renderTabItem}
              keyExtractor={keyExtractor}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.flatListContent}
              onScroll={handleScroll}
              scrollEventThrottle={16}
              // Performance optimizations
              removeClippedSubviews={true}
              maxToRenderPerBatch={10}
              windowSize={10}
              initialNumToRender={8}
              getItemLayout={(data, index) => ({
                length: 80, // Approximate item width + gap
                offset: 80 * index,
                index,
              })}
            />
          </View>
        );

      default:
        return null;
    }
  };

  return (
    <View style={[containerStyle, {marginTop: 2}]}>{renderContent()}</View>
  );
};

export default TopNavList;

const styles = StyleSheet.create({
  flatListContent: {
    paddingHorizontal: 16,
    paddingTop: 4,
    gap: 8,
  },
  containerWithBackground: {
    position: 'relative',
    height: '100%',
  },
  slidingBackground: {
    position: 'absolute',
    backgroundColor: '#89DCFE',
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
    zIndex: 0,
  },
  slidingBackgroundBottom: {
    position: 'absolute',
    backgroundColor: '#0078AD',
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
    zIndex: 0,
  },
  itemContainer: {
    width: 72,
    height: '100%',
    gap: 4,
    alignItems: 'center',
    padding: 4,
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
    zIndex: 1,
  },
  image: {
    borderRadius: 100,
  },
  title: {
    textAlign: 'center',
  },
  selectedText: {
    color: '#0C5273',
    // fontWeight: '700',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'white',
  },
  timeSlotShimmer: {
    borderRadius: 4,
  },
  skeletonImage: {
    borderRadius: 4,
  },
  skeletonText: {
    borderRadius: 2,
    marginTop: 2,
  },
  skeletonFlatListContent: {
    flexDirection: 'row',
    gap: 8,
  },
  imageContainer: {
    position: 'relative',
  },
  badgeContainer: {
    position: 'absolute',
    top: -2,
    left: -16,
    backgroundColor: '#D0F1E0',
    paddingHorizontal: 5,
    paddingVertical: 1,
    borderRadius: 4,
    zIndex: 1,
  },
  badgeText: {},
});
