import {CleverTap} from 'clevertap-react-native';
import {logAnalyticsEvent} from '../JMAnalyticsUtility';
import {EventTriggerChannel} from '../AnalyticsParams';
import {
  getCustomerId,
  getCustomerType,
  getCurrentPincode,
  getUserName,
  getFirstName,
  getUserIdentity,
  getUserEmail,
  getUserPhone,
  getTotalCartItems,
  getUserTimezone,
} from '../utils/UserUtils';

class AnalyticsService {
  // Internal method called by logAnalyticsEvent for CleverTap channel
  static recordEvent(eventName, properties = {}) {
    try {
      const processedProperties = {};

      if (properties && typeof properties === 'object') {
        Object.keys(properties).forEach(key => {
          const value = properties[key];

          if (typeof value === 'string') {
            // Replace all spaces with underscores in string values
            processedProperties[key] = value.replace(/\s+/g, '_');
          } else if (Array.isArray(value)) {
            // Handle arrays - process each string element
            processedProperties[key] = value.map(item =>
              typeof item === 'string' ? item.replace(/\s+/g, '_') : item,
            );
          } else if (typeof value === 'object' && value !== null) {
            // Handle nested objects recursively
            processedProperties[key] = this.processNestedObject(value);
          } else {
            // Keep other types (numbers, booleans, null) as is
            processedProperties[key] = value;
          }
        });
      }

      console.log(
        ' === logAnalyticsEvent CT Events Processed === ' +
          'processed properties =>' +
          JSON.stringify(processedProperties),
      );

      (async () => {
        await CleverTap.recordEvent(eventName, processedProperties);
      })();
    } catch (error) {
      console.error('CleverTap Analytics Error:', error);
    }
  }

  // Helper method to process nested objects
  static processNestedObject(obj) {
    if (typeof obj !== 'object' || obj === null) {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map(item =>
        typeof item === 'string'
          ? item.replace(/\s+/g, '_')
          : typeof item === 'object'
          ? this.processNestedObject(item)
          : item,
      );
    }

    const processedObj = {};
    Object.keys(obj).forEach(key => {
      const value = obj[key];

      if (typeof value === 'string') {
        processedObj[key] = value.replace(/\s+/g, '_');
      } else if (typeof value === 'object') {
        processedObj[key] = this.processNestedObject(value);
      } else {
        processedObj[key] = value;
      }
    });

    return processedObj;
  }
  // Helper method to create AnalyticEvent and call logAnalyticsEvent
  static async trackEvent(eventName, properties = {}) {
    const event = {
      eventName,
      payload: {
        system: 'react-native',
        'Customer Id': getCustomerId(),
        'Customer Type': getCustomerType(),
        ...properties,
      },
      channel: EventTriggerChannel.CLEVER_TAP,
    };

    await logAnalyticsEvent(event);
  }

  // Authentication Events
  static async trackOTPRequest() {
    await this.trackEvent('Request OTP');
  }

  static async trackLoginSuccess() {
    // Track login event
    await this.trackEvent('Login Success');

    // Update user profile
    try {
      const profileData = {
        Name: getUserName(),
        'First Name': getFirstName(),
        Identity: getUserIdentity(),
        Email: getUserEmail(),
        Phone: `+91${getUserPhone()}`,
        'MSG-whatsapp': true,
        Tz: getUserTimezone(),
      };

      CleverTap.onUserLogin(profileData);
    } catch (error) {
      console.error('CleverTap Profile Update Error:', error);
    }
  }

  static async trackLoginFailure(reason) {
    await this.trackEvent('Login Failure', {
      'Failure Reason': reason,
    });
  }

  static async trackResendOTP() {
    await this.trackEvent('Resend OTP');
  }

  // E-commerce Events
  static async trackCartAction(action, productId, quantity, pageName) {
    await this.trackEvent('Cart', {
      'Page Name': pageName,
      Option: action,
      'Product Id': productId,
      'Product Quantity': quantity?.toString(),
    });
  }

  static async trackPurchase(orderDetails, items) {
    try {
      const chargedData = {
        Charged: orderDetails.total,
        Currency: 'INR',
        'Order ID': orderDetails.orderId,
        'Customer Id': getCustomerId(),
      };

      CleverTap.recordChargedEvent(chargedData, items);
    } catch (error) {
      console.error('CleverTap Purchase Tracking Error:', error);
    }
  }

  // Search Events
  static async trackSearch(searchQuery) {
    await this.trackEvent('Search Enter', {
      'Search Key': searchQuery,
      'Pin Code': getCurrentPincode(),
    });
  }

  static async trackAutoSuggest(searchQuery, position, productId, vertical) {
    await this.trackEvent('Search Autosuggest', {
      'Search Key': searchQuery,
      Position: position,
      'Product ID': productId,
      Vertical: vertical,
      'Pin Code': getCurrentPincode(),
    });
  }

  static async trackSearchInit() {
    await this.trackEvent('Search');
  }

  // Navigation Events
  static async trackNavigation(
    eventName,
    pageName,
    option,
    additionalProps = {},
  ) {
    await this.trackEvent(eventName, {
      'Page Name': pageName,
      Option: option,
      ...additionalProps,
    });
  }

  static async trackHamburgerOption(option, pageName) {
    await this.trackEvent('Hamburger Options', {
      'Page Name': pageName,
      Option: option,
    });
  }

  static async trackBottomNavigation(option, pageName) {
    await this.trackEvent('Bottom Navigation Option', {
      'Page Name': pageName,
      Option: option,
    });
  }

  static async trackBellIcon(pageName) {
    await this.trackEvent('Bell Icon', {
      'Page Name': pageName,
      Option: 'Bell icon Click',
    });
  }

  // Order Management Events
  static async trackOrderDetails(orderId, pageName) {
    await this.trackEvent('Order details', {
      'Page Name': pageName,
      'Order ID': orderId,
    });
  }

  static async trackMyOrders() {
    await this.trackEvent('my_orders', {
      category: 'orders',
      action: 'view',
      label: 'my orders',
    });
  }

  // Location & Permission Events
  static async trackLocationPermission(response, pageName) {
    await this.trackEvent('Location Permission', {
      'Page Name': pageName,
      Option: response,
    });
  }

  static async trackAddress(action, pageName, description) {
    await this.trackEvent('Address', {
      'Page Name': pageName,
      Option: action,
      Description: description,
    });
  }

  // QR Code & Scanner Events
  static async trackQRScan(pageName) {
    await this.trackEvent('Scan QR', {
      'Page Name': pageName,
      Option: 'QR Scan',
    });
  }

  // Profile Updates
  static async updateUserProfile(profileData) {
    try {
      CleverTap.profilePush(profileData);
    } catch (error) {
      console.error('CleverTap Profile Update Error:', error);
    }
  }

  static async updateDefaultAddress(state, city, pincode) {
    try {
      CleverTap.profilePush({
        'Default State': state,
        'Default City': city,
        'Default Pincode': pincode,
      });
    } catch (error) {
      console.error('CleverTap Address Update Error:', error);
    }
  }

  static async updateCartCount(count) {
    try {
      CleverTap.profilePush({
        'Total Cart Quantity': count.toString(),
      });
    } catch (error) {
      console.error('CleverTap Cart Count Update Error:', error);
    }
  }

  // Push Notifications
  static async trackNotificationViewed(messageId) {
    try {
      CleverTap.pushInboxNotificationViewedEvent(messageId);
    } catch (error) {
      console.error('CleverTap Notification Viewed Error:', error);
    }
  }

  static async trackNotificationClicked(messageId) {
    try {
      CleverTap.pushInboxNotificationClickedEvent(messageId);
    } catch (error) {
      console.error('CleverTap Notification Clicked Error:', error);
    }
  }

  static async markNotificationRead(messageId) {
    try {
      CleverTap.markReadInboxMessage(messageId);
    } catch (error) {
      console.error('CleverTap Mark Read Error:', error);
    }
  }

  static async deleteNotification(messageId) {
    try {
      CleverTap.deleteInboxMessage(messageId);
    } catch (error) {
      console.error('CleverTap Delete Notification Error:', error);
    }
  }

  // Technical Events
  static async trackAppLaunch() {
    await this.trackEvent('app_launch', {
      category: 'app',
      action: 'launch',
      label: 'app startup',
    });
  }

  static async trackDeviceBackClick(pageName) {
    await this.trackEvent('native back clicked', {
      'Page Name': pageName,
    });
  }

  // In-App Banner Events
  static async trackInAppBanner(campaignId, campaignData) {
    await this.trackEvent('InApp Banner Event', {
      campaign_id: campaignId,
      campaign_start_time: campaignData.startTime,
      campaign_end_time: campaignData.endTime,
      campaign_start_date: campaignData.startDate,
      campaign_end_date: campaignData.endDate,
    });
  }

  // Firebase GA4 Integration
  static async trackFirebaseEvent(eventName, parameters) {
    const event = {
      eventName,
      payload: parameters,
      channel: EventTriggerChannel.FIREBASE,
    };

    await logAnalyticsEvent(event);
  }

  static async trackCombinedEvent(
    ctEventName,
    ctParams,
    gaEventName,
    gaParams,
  ) {
    // Track CleverTap event
    await this.trackEvent(ctEventName, ctParams);

    // Track Firebase event
    await this.trackFirebaseEvent(gaEventName, gaParams);
  }
}

export default AnalyticsService;
