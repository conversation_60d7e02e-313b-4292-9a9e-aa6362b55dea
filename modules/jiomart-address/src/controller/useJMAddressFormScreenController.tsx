import { StackActions } from '@react-navigation/native';
import { QueryClient } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import {
  AnalyticsParams,
  EventTriggerChannel,
} from '../../../jiomart-common/src/AnalyticsParams';
import { logAnalyticsEvent } from '../../../jiomart-common/src/JMAnalyticsUtility';
import { AppScreens } from '../../../jiomart-common/src/JMAppScreenEntry';
import { RQKey } from '../../../jiomart-common/src/JMConstants';
import { navBeanObj } from '../../../jiomart-common/src/JMNavGraphUtil';
import {
  GenericToast,
  mergeGenericToastTypeData,
} from '../../../jiomart-common/src/JMScreenSlot.types';
import { JMSharedViewModel } from '../../../jiomart-common/src/JMSharedViewModel';
import { JMAddressModel } from '../../../jiomart-common/src/uiModals/JMAddressModel';
import {
  getValidName,
  getVersionSpecificFilterListData,
} from '../../../jiomart-common/src/utils/JMCommonFunctions';
import { JMLogger } from '../../../jiomart-common/src/utils/JMLogger';
import {
  capitalizeFirstLetter,
  getValidString,
} from '../../../jiomart-common/src/utils/JMStringUtility';
import { useGlobalState } from '../../../jiomart-general/src/context/JMGlobalStateProvider';
import useFormHandler from '../../../jiomart-general/src/hooks/useFormHandler';
import { useConfigFile } from '../../../jiomart-general/src/hooks/useJMConfig';
import useUserProfile from '../../../jiomart-general/src/hooks/useUserProfile';
import { navigateTo } from '../../../jiomart-general/src/navigation/JMNavGraph';
import { JMConfigFileName } from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import useAddressOperation from '../hooks/useAddressOperation';
import type { UseJMAddressFormScreenProps } from '../types/JMAddressFormScreenType';

export const enum AddressFormField {
  ID = 'id',
  HOUSE_NO = 'flat_or_house_no',
  FLOOR_NO = 'floor_no',
  TOWER_NO = 'tower_no',
  BUILDING_APARTMENT_NAME = 'area',
  ADDRESS = 'address',
  LANDMARK_AREA = 'landmark',
  PINCODE = 'pincode',
  CITY = 'city',
  STATE = 'state',
  RECEIVER_NAME = 'name',
  RECEIVER_NUMBER = 'phone',
  ADDRESS_TYPE = 'address_type',
  ADDRESS_TYPE_OTHER = 'address_type_other',
}

export const enum AddressType {
  HOME = 'Home',
  WORK = 'Work',
  OTHER = 'Other',
}

const queryClient = new QueryClient();

const useJMAddressFormScreenController = (
  props: UseJMAddressFormScreenProps,
) => {
  const { route, navigation } = props;
  const config: any = getVersionSpecificFilterListData(
    useConfigFile(JMConfigFileName.JMAddressConfigurationFileNAme)?.screen
      ?.addressForm,
  )?.[0];

  const isOrderReview = route.params?.params?.isOrderReview;
  const rcpDeskDelFlow = route.params?.params?.rcpDeskDelFlow ?? false;
  const address =
    rcpDeskDelFlow && !route.params?.params?.address
      ? {
        address: `${route.params?.params?.city},${route.params?.params?.state}`,
        formattedAddress: `${route.params?.params?.city},${route.params?.params?.state}`,
        city: route.params?.params?.city,
        state: route.params?.params?.state,
        pin: route.params?.params?.pinCode,
      }
      : route.params?.params?.address;

  const regionCamera = {
    ...config?.map?.initialCamera,
    center: {
      latitude: address?.lat,
      longitude: address?.lon,
    },
  };

  const { setToastTypeData } = useGlobalState();

  const { userData } = useUserProfile();

  const [isFormValid, setFormValid] = useState(false);
  const [isApiLoading, setApiLoading] = useState(false);

  const receiverName = address?.id
    ? getValidName(address?.name)
    : capitalizeFirstLetter(getValidString(userData?.first_name)) + ' ' + capitalizeFirstLetter(getValidString(userData?.last_name));
  const receiverNumber = address?.id
    ? address?.phone
    : userData?.phone_numbers?.length > 0
      ? userData.phone_numbers[0].phone
      : '';

  const {
    saveAddress,
    updateAddress,
    setDefaultAddressApi,
    setDefaultAddressFromList,
    generateDefaultAddressRequestBody,
    generateSaveAddressRequestBody,
    generateUpdateAddressRequestBody,
  } = useAddressOperation();

  const {
    formData,
    formError,
    handleInputRef,
    handleFormError,
    handleFormData,
    onChangeText,
    validateValue,
    validateForm,
  } = useFormHandler(
    {
      [AddressFormField.ID]: address?.id,
      [AddressFormField.RECEIVER_NAME]: receiverName,
      [AddressFormField.RECEIVER_NUMBER]: receiverNumber ?? '',
      [AddressFormField.ADDRESS_TYPE]: address?.address_type
        ? capitalizeFirstLetter(address?.address_type)
        : address?.address_type,
      [AddressFormField.ADDRESS_TYPE_OTHER]: address?.address_type_other,
      [AddressFormField.ADDRESS]: address?.address.replace(
        config?.form?.[AddressFormField.ADDRESS]?.regex ?? /[^A-Za-z0-9,./ -]/g,
        '',
      ),
      [AddressFormField.HOUSE_NO]: address?.flat_or_house_no,
      [AddressFormField.FLOOR_NO]: address?.floor_no,
      [AddressFormField.TOWER_NO]: address?.tower_no,
      [AddressFormField.BUILDING_APARTMENT_NAME]: address?.area,
      [AddressFormField.LANDMARK_AREA]: address?.landmark,
      [AddressFormField.CITY]: address?.city,
      [AddressFormField.STATE]: address?.state,
      [AddressFormField.PINCODE]: address?.pin,
    },
    config?.form,
  );

  useEffect(() => {
    isReqAddressFieldsValid();
  }, [formData]);

  const handleSaveAs = (val: string) => {
    handleFormData(AddressFormField.ADDRESS_TYPE, config?.[AddressFormField.ADDRESS_TYPE])(val);
  };

  const handleRedirection = () => {
    try {
      queryClient.invalidateQueries({
        queryKey: [RQKey.GET_ADDRESS],
      });
    } catch (error) {
      JMLogger.log('handleRedirection ' + error);
    }
    JMSharedViewModel.Instance.setRefreshAddressList(true);
    setApiLoading(false);
    const routes = navigation.getState().routes;

    const screenAIndex = routes.findIndex(
      route => route.name === AppScreens.ADDRESS_LIST_SCREEN,
    );

    if (isOrderReview || screenAIndex === -1) {
      moveToLastWebview();
    } else {
      navigateTo(
        navBeanObj({
          ...getVersionSpecificFilterListData<any>(config?.submitButton)?.[0]
            ?.cta,
        }),
        navigation,
      );
    }
  };

  const moveToLastWebview = () => {
    try {
      const parentNavigation = navigation.getParent();
      const parentRoutes = parentNavigation?.getState().routes;
      if (parentRoutes) {
        const routeNames = parentRoutes.map(route => route.name);
        const screenAIndex = routeNames.lastIndexOf(AppScreens.COMMON_WEB_VIEW);
        console.log('🚀 ~ handleRedirection ~ screenAIndex:', screenAIndex);
        if (screenAIndex !== -1) {
          const currentIndex = parentNavigation.getState().index;
          const popCount = currentIndex - screenAIndex;

          if (popCount > 0) {
            parentNavigation.dispatch(StackActions.pop(popCount));
          } else {
            parentNavigation?.dispatch(StackActions.popToTop());
          }
        } else {
          parentNavigation?.dispatch(StackActions.popToTop());
        }
      } else {
        if (parentNavigation)
          parentNavigation?.dispatch(StackActions.popToTop());
        else navigation.popToTop();
      }
    } catch (error) {
      JMLogger.log('handleAddressRedirection ' + error);
      navigation?.popToTop();
    }
  };

  const onTextChange = (field: AddressFormField) => (text: string) => {
    onChangeText(field)(text);
  };

  const isReqAddressFieldsValid = () => {
    const getLimit = (field: string, fallback: number) =>
      parseInt(
        config?.form?.[field]?.validation?.minLength ||
        config?.form?.[field]?.validation?.exactLength ||
        '',
      ) || fallback;

    const validations = [
      formData[AddressFormField.PINCODE]?.trim().length ===
      getLimit(AddressFormField.PINCODE, 6),

      formData[AddressFormField.CITY]?.trim() !== '-' &&
      formData[AddressFormField.CITY]?.trim().length > 0,

      formData[AddressFormField.STATE]?.trim() !== '-' &&
      formData[AddressFormField.STATE]?.trim().length > 0,

      formData[AddressFormField.ADDRESS]?.trim().length >=
      getLimit(AddressFormField.ADDRESS, 3),

      formData[AddressFormField.LANDMARK_AREA]?.trim().length >=
      getLimit(AddressFormField.LANDMARK_AREA, 3),

      formData[AddressFormField.RECEIVER_NAME]?.trim().length >=
      getLimit(AddressFormField.RECEIVER_NAME, 3),

      formData[AddressFormField.RECEIVER_NUMBER]?.trim().length ===
      getLimit(AddressFormField.RECEIVER_NUMBER, 10),

      !!formData[AddressFormField.ADDRESS_TYPE]?.trim(),
    ];

    if (
      formData[AddressFormField.ADDRESS_TYPE] === 'Other' &&
      getValidString(formData[AddressFormField.ADDRESS_TYPE_OTHER]?.trim())
        .length < getLimit(AddressFormField.ADDRESS_TYPE_OTHER, 3)
    ) {
      validations.push(false);
    }

    setFormValid(validations.every(Boolean));
  };

  const handleAddressSubmit = () => {
    try {
      console.log('handleAddressSubmit', 'handleAddressSubmit--');
      if (!validateForm()) {
        return;
      }

      console.log('🚀 ~ handleAddressSubmit ~ formData:', formData);
      setApiLoading(true);
      const payload: JMAddressModel = {
        id: formData[AddressFormField.ID],
        city: formData[AddressFormField.CITY],
        state: formData[AddressFormField.STATE],
        pin: formData[AddressFormField.PINCODE],
        name: formData[AddressFormField.RECEIVER_NAME],
        phone: formData[AddressFormField.RECEIVER_NUMBER],
        address: formData[AddressFormField.ADDRESS],
        flat_or_house_no: formData[AddressFormField.HOUSE_NO],
        floor_no: formData[AddressFormField.FLOOR_NO],
        tower_no: formData[AddressFormField.TOWER_NO],
        area: formData[AddressFormField.BUILDING_APARTMENT_NAME],
        landmark: formData[AddressFormField.LANDMARK_AREA],
        lat: regionCamera?.center?.latitude ?? 0.0,
        lon: regionCamera?.center?.longitude ?? 0.0,
        input_mode:
          'MAP_POLY' /*(regionCamera?.center?.latitude) ? 'MAP_POLY' : 'manual'*/,
        is_default_address: true,
        ...(capitalizeFirstLetter(formData[AddressFormField.ADDRESS_TYPE]) !=
          'Home' &&
          capitalizeFirstLetter(formData[AddressFormField.ADDRESS_TYPE]) != 'Work'
          ? {
            address_type: capitalizeFirstLetter(
              formData[AddressFormField.ADDRESS_TYPE],
            ),
            address_type_other: formData[AddressFormField.ADDRESS_TYPE_OTHER],
          }
          : {
            address_type: capitalizeFirstLetter(
              formData[AddressFormField.ADDRESS_TYPE],
            ),
          }),
      };

      console.log('🚀 ~ handleAddressSubmit ~ payload:', payload);
      if (formData[AddressFormField.ID]) {
        const request = generateUpdateAddressRequestBody(payload);
        updateAddress.mutate(request, {
          onSuccess: res => {
            if (res?.success || res?.status === 'success') {
              logAnalyticsEvent({
                eventName: 'Address',
                payload: {
                  option: 'Save As',
                  [AnalyticsParams.PAGE_NAME]: 'Address Page',
                },
                channel: `${EventTriggerChannel.FIREBASE},${EventTriggerChannel.CLEVER_TAP}`,
              });
              const request = generateDefaultAddressRequestBody(payload);
              setDefaultAddressApi.mutate(request, {
                onSuccess: res => {
                  setDefaultAddressFromList(request);

                  let modifiedToastData = {
                    isVisible: true,
                    message: 'Address updated',
                  };

                  setToastTypeData(
                    mergeGenericToastTypeData(
                      GenericToast.SUCCESS,
                      modifiedToastData,
                    ),
                  );
                  handleRedirection();
                },
                onError(error, variables, context) {
                  setApiLoading(false);
                  let modifiedToastData = {
                    isVisible: true,
                    message: 'Something went wrong. Please try again',
                  };

                  setToastTypeData(
                    mergeGenericToastTypeData(
                      GenericToast.ERROR,
                      modifiedToastData,
                    ),
                  );
                },
              });
            }
          },
          onError(error, variables, context) {
            setApiLoading(false);
            let modifiedToastData = {
              isVisible: true,
              message: 'Something went wrong. Please try again',
            };

            setToastTypeData(
              mergeGenericToastTypeData(GenericToast.ERROR, modifiedToastData),
            );
          },
        });
      } else {
        const request = generateSaveAddressRequestBody(payload);
        saveAddress.mutate(request, {
          onSuccess: res => {
            if (res?.success === true || res?.status === 'success') {
              logAnalyticsEvent({
                eventName: 'Address',
                payload: {
                  option: 'Save & Proceed',
                  [AnalyticsParams.PAGE_NAME]: 'Address Page',
                },
                channel: `${EventTriggerChannel.FIREBASE},${EventTriggerChannel.CLEVER_TAP}`,
              });
              payload.id = res.result.address_id;
              const request = generateDefaultAddressRequestBody(payload);
              setDefaultAddressApi.mutate(request, {
                onSuccess: res => {
                  setDefaultAddressFromList(request);
                  let modifiedToastData = {
                    isVisible: true,
                    message: 'Address saved',
                  };

                  setToastTypeData(
                    mergeGenericToastTypeData(
                      GenericToast.SUCCESS,
                      modifiedToastData,
                    ),
                  );
                  handleRedirection();
                },
              });
            } else {
              setApiLoading(false);
              let modifiedToastData = {
                isVisible: true,
                message: 'Something went wrong. Please try again',
              };

              setToastTypeData(
                mergeGenericToastTypeData(
                  GenericToast.ERROR,
                  modifiedToastData,
                ),
              );
            }
          },
          onError(error, variables, context) {
            setApiLoading(false);
            let modifiedToastData = {
              isVisible: true,
              message: 'Something went wrong. Please try again',
            };

            setToastTypeData(
              mergeGenericToastTypeData(GenericToast.ERROR, modifiedToastData),
            );
          },
        });
      }
    } catch (error) {
      setApiLoading(false);
      let modifiedToastData = {
        isVisible: true,
        message: 'Something went wrong. Please try again',
      };

      setToastTypeData(
        mergeGenericToastTypeData(GenericToast.ERROR, modifiedToastData),
      );
    }
  };

  useEffect(() => {
    navigation.setParams({
      ...route.params,
      navTitle: formData[AddressFormField.ID]
        ? config?.header?.editNavTitle
        : route.params.navTitle,
    });
  }, []);

  return {
    ...props,
    address,
    regionCamera,
    config,
    formData,
    formError,
    isFormValid,
    isApiLoading,
    rcpDeskDelFlow,
    onTextChange,
    handleInputRef,
    handleFormError,
    validateValue,
    handleAddressSubmit,
    handleSaveAs,
    navigationBean: route.params,
  };
};

export default useJMAddressFormScreenController;
