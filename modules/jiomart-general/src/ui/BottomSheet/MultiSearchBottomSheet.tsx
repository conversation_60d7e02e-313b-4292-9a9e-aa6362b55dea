import React from 'react';
import {
  StyleSheet,
  Text,
  View,
  TextInput,
  Keyboard,
  SafeAreaView,
} from 'react-native';
import {JioIcon, JioText} from '@jio/rn_components';
import {IconColor, JioTypography} from '@jio/rn_components/src/index.types';
// import {trackFirebaseSearchEvent} from '../../Analytics/AnalyticsUtility';
// import addDashesToMultiWordString from '../../../utilities/utils/Utility';
import {TouchableWithoutFeedback} from 'react-native-gesture-handler';
import useMultiSearchBottomSheetController from './controller/useMultiSearchBottomSheetController';
import {
  getChipHeight,
  getChipWidth,
} from '../../../../jiomart-common/src/utils/JMCommonFunctions';
import {BottomSheetChildren} from './types/BottomSheetType';
import {logAnalyticsEvent} from '../../../../jiomart-common/src/JMAnalyticsUtility';
import {
  AnalyticsParams,
  EventTriggerChannel,
} from '../../../../jiomart-common/src/AnalyticsParams';

interface PropsType extends BottomSheetChildren {
  textInputRef: any;
  closeMultiSearchBtmSheet?: Function;
  onChangeMultiSearch: Function;
  config: any;
}
const MultiSearchBottomSheet = ({
  textInputRef,
  closeMultiSearchBtmSheet,
  onChangeMultiSearch,
  config,
  close,
}: PropsType) => {
  const {
    setSearchText,
    windowValue,
    handleTextChange,
    searchText,
    isValidSearch,
    setIsValidSearch,
    // showToast,
    insets,
  } = useMultiSearchBottomSheetController();

  return (
    <View style={{flex: 1, paddingBottom: insets.bottom}}>
      <View style={styles.top_container}>
        <View style={styles.header_container}>
          <JioText
            text={config.header_title}
            appearance={JioTypography.HEADING_XXS}
            color={'black'}
          />
          <TouchableWithoutFeedback
            onPress={() => {
              console.warn(
                'multi search',
                'multi search close called--' +
                  JSON.stringify(config.analyticEvent.closeClick),
              );
              logAnalyticsEvent(config?.analyticEvent?.closeClick);

              console.warn('multi search', 'multi search GA called--');

              setSearchText('');
              close?.(() => {
                closeMultiSearchBtmSheet?.();
              });
            }}
            style={{
              paddingHorizontal: 8,
              paddingVertical: 4,
            }}>
            <JioIcon ic={'IcClose'} color={IconColor.PRIMARY60} />
          </TouchableWithoutFeedback>
        </View>
        <JioText
          text={config.sub_title}
          appearance={JioTypography.BODY_XS}
          color={'primary_grey_80'}
          style={{
            marginBottom: 24,
          }}
        />
        {config.text_input.is_visible ? (
          <>
            <JioText
              text={config.text_input.label}
              appearance={JioTypography.BODY_XS}
              color={'primary_grey_80'}
            />
            <View
              style={{
                flexDirection: 'row',
                width: 312 * getChipWidth(),
                justifyContent: 'space-between',
              }}>
              <TextInput
                ref={textInputRef}
                onChangeText={handleTextChange}
                value={searchText}
                placeholder={config.text_input.placeholder}
                placeholderTextColor={'#E0E0E0'}
                multiline={true}
                editable
                enablesReturnKeyAutomatically={true}
                scrollEnabled={true}
                style={{
                  borderBottomWidth: 2,
                  borderColor: '#0C5273',
                  margin: 0,
                  padding: 0,
                  minHeight: 32 * getChipHeight(),
                  maxHeight: (windowValue / 6) * getChipHeight(),
                  fontSize: 16,
                  fontWeight: '500',
                  color: '#141414',
                  textAlignVertical: 'center',
                  textAlign: 'justify',
                  flex: 1,
                  paddingBottom: 8,
                  paddingTop: 5,
                }}
              />
            </View>
            <JioText
              text={
                isValidSearch
                  ? config.text_input.sub_label
                  : config.text_input.invalid_message
              }
              appearance={JioTypography.BODY_XS}
              color={isValidSearch ? 'primary_grey_80' : 'feedback_error_80'}
              style={{marginTop: 4}}
            />
          </>
        ) : null}
      </View>
      <View style={[styles.bottom_container]}>
        {config.action_button[0].is_visible ? (
          <TouchableWithoutFeedback
            onPress={() => {
              setSearchText('');
              setIsValidSearch(true);
            }}
            style={[styles.btn, styles.clearBtn]}>
            <Text style={[styles.text, styles.clear_text]}>
              {config.action_button[0].button_title}
            </Text>
          </TouchableWithoutFeedback>
        ) : null}
        {config.action_button[1].is_visible ? (
          <TouchableWithoutFeedback
            onPress={() => {
              logAnalyticsEvent({
                ...config?.analyticEvent?.searchButtonClick,
                payload: {
                  ...config?.analyticEvent?.searchButtonClick?.payload,
                  [AnalyticsParams.SEARCH_QUERY]: searchText,
                },
              });

              const appsflyerEventDict = {
                af_search_string: searchText,
              };

              logAnalyticsEvent({
                eventName: 'af_search',
                payload: {
                  ...appsflyerEventDict,
                },
                channel: EventTriggerChannel.APPSFLYER,
              });

              if (searchText && isValidSearch) {
                close?.(() => {
                  onChangeMultiSearch(searchText);
                  close?.(() => {
                    closeMultiSearchBtmSheet?.();
                  });
                  setSearchText('');
                });
              }
            }}
            style={[
              styles.btn,
              styles.searchBtn,
              {
                backgroundColor:
                  searchText && isValidSearch ? '#0078AD' : '#b7dbeaff',
              },
              {
                borderColor:
                  searchText && isValidSearch ? '#0078AD' : '#b7dbeaff',
              },
            ]}>
            <Text style={[styles.text, styles.search_text]}>
              {config.action_button[1].button_title}
            </Text>
          </TouchableWithoutFeedback>
        ) : null}
      </View>
    </View>
  );
};

export default MultiSearchBottomSheet;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'column',
    backgroundColor: 'white',
    justifyContent: 'space-between',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
  },
  top_container: {
    // minHeight: 184 * Height,
    marginHorizontal: 24 * getChipHeight(),
    marginTop: 16,
    marginBottom: 32 * getChipHeight(),
  },
  bottom_container: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingBottom: 16,
  },
  header_container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  header_text: {
    fontSize: 16,
    fontWeight: '900',
    fontFamily: 'JioType-Medium',
    color: '#141414',
  },
  btn: {
    paddingVertical: 12,
    borderWidth: 1,
    borderRadius: 1000,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  clearBtn: {
    borderColor: '#E0E0E0',
    paddingHorizontal: 53.5,
  },
  searchBtn: {
    paddingHorizontal: 35,
  },
  text: {
    fontSize: 16,
    fontWeight: '700',
    fontFamily: 'JioType-Medium',
  },
  clear_text: {
    color: '#0C5273',
  },
  search_text: {
    color: '#FFFFFF',
  },
});
