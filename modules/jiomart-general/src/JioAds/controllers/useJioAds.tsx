import {useCallback, useState} from 'react';
import {AdEventName, type useJioAdsProps} from '../types/JioAds';
import {handleDeeplinkIntent} from '../../deeplink/JMDeeplinkUtility';
import {navigateTo} from '../../navigation/JMNavGraph';
import type {NavigationBean} from '../../../../jiomart-common/src/JMNavGraphUtil';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';

const useJioAds = (props: useJioAdsProps) => {
  const {
    onAdStatus,
    adMetaData,
    adHeight,
    adSpotId,
    adType,
    adWidth,
    onClick,
    navigation,
  } = props;

  const [adSuccess, setAdSuccess] = useState<boolean | null>(null);

  const handleJioAdsClick = useCallback(
    async (event: any) => {
      const adClickUrlValue = event.nativeEvent.value;

      const adClickUrl = adClickUrlValue?.ctaUrl
        ? adClickUrlValue?.ctaUrl
        : adClickUrlValue?.fallbackUrl;

      const bean: NavigationBean = (await handleDeeplinkIntent({
        mUri: adClickUrl,
      })) as NavigationBean;

      navigateTo(bean, navigation as NativeStackNavigationProp<any>);

      onClick?.();
    },
    [navigation, onClick],
  );

  const handleAdEvent = useCallback(
    e => {
      console.log(
        '======== jio ads event ========\n',
        'adSpotId => ',
        adSpotId,
        '\nadType =>',
        adType,
        '\nadWidth => ',
        adWidth,
        '\nadHeight => ',
        adHeight,
        '\nadMetaData => ',
        adMetaData,
        '\nevent => ',
        e.nativeEvent.event,
        '\nvalue => ',
        e.nativeEvent.value,
        '\n======== jio ads event ========',
      );
      switch (e.nativeEvent.event) {
        case AdEventName.adFailed:
          setAdSuccess(false);
          onAdStatus?.(false);
          break;
        case AdEventName.adPrepared:
          setAdSuccess(true);
          onAdStatus?.(true);
          break;
        case AdEventName.adClicked:
          handleJioAdsClick(e);
          break;
      }
    },
    [
      adHeight,
      adMetaData,
      adSpotId,
      adType,
      adWidth,
      handleJioAdsClick,
      onAdStatus,
    ],
  );

  return {adSuccess, adMetaData, handleAdEvent};
};

export default useJioAds;
