import {Platform} from 'react-native';
import {
  AnalyticsParams,
  EventTriggerChannel,
  type AnalyticEvent,
  type AnalyticsPayload,
} from './AnalyticsParams';
import {getPrefString} from './JMAsyncStorageHelper';
import {AsyncStorageKeys} from './JMConstants';
import {JMSharedViewModel} from './JMSharedViewModel';
import {JHExceptionLogger, JMLogger} from './utils/JMLogger';
import analytics from '@react-native-firebase/analytics';
import AnalyticsService from './services/AnalyticsService';
import appsFlyer from 'react-native-appsflyer';

/**
 * Log an analytics event
 * @param name 40 character max alphanumeric and '_' only, starts with alpha
 * @param props keys 40 character max w/name content rules, values 100 character max
 */

export const logAnalyticsEvent = async (event: AnalyticEvent) => {
  try {
    const eventChannel = event?.channel?.split(',');
    const eventName = event?.eventName;
    const eventPayload = event?.payload;

    let size = eventChannel?.length;

    for (let i = 0; i < size; i++) {
      JMLogger.log(
        ' === logAnalyticsEvent === ' +
          '===size===' +
          size +
          '===eventChannel =>' +
          eventChannel +
          '==event channel at--' +
          i +
          '||' +
          eventChannel[i] +
          '||eventName =>' +
          eventName +
          '||paramMap =>' +
          JSON.stringify(eventPayload),
      );

      if (!eventPayload) {
        continue;
      }

      switch (eventChannel[i]) {
        case EventTriggerChannel.FIREBASE: {
          const updateEventPayload = (await appendDefaultValuesToGAModel(
            eventPayload as AnalyticsPayload,
          )) as AnalyticsPayload;
          JMLogger.log(
            ' === logAnalyticsEvent === ' +
              'updated to =>' +
              JSON.stringify(updateEventPayload),
          );
          logAnalyticsEventOnGA(eventName, updateEventPayload);
          break;
        }
        case EventTriggerChannel.CLEVER_TAP: {
          // CleverTap event tracking implementation
          const updateCtEventPayload = (await appendDefaultValuesToCTModel(
            eventPayload as AnalyticsPayload,
          )) as AnalyticsPayload;

          AnalyticsService.recordEvent(eventName, updateCtEventPayload);
          break;
        }
        case EventTriggerChannel.CLEVER_TAP_PROFILE: {
          AnalyticsService.updateUserProfile(eventPayload);
          break;
        }
        case EventTriggerChannel.FIREBASE_AND_SENSE: {
          break;
        }
        case EventTriggerChannel.SENSE: {
          break;
        }
        case EventTriggerChannel.APPSFLYER: {
          JMLogger.log(
            ' === logAnalyticsEvent === ' +
              'updated to APPSFLYER =>' +
              JSON.stringify(eventPayload),
          );
          logAnalyticsEventOnAppsflyer(eventName, eventPayload);
          break;
        }
        default:
          break;
      }
    }
  } catch (e) {
    JHExceptionLogger.log('Unable to tag analytics event:', e);
  }
};

/**
 * Log AppsFlyer analytics event with enhanced error handling
 * @param name Event name
 * @param payload Event payload/parameters
 */
const logAnalyticsEventOnAppsflyer = (
  name: string,
  payload: AnalyticsPayload,
) => {
  try {
    JMLogger.log(
      ' === logAnalyticsEvent AppsFlyer === ' +
        'logging name =>' +
        name +
        '||payload =>' +
        JSON.stringify(payload),
    );

    // Convert payload to AppsFlyer compatible format
    const appsFlyerParams: Record<string, any> = {};

    if (payload && typeof payload === 'object') {
      Object.keys(payload).forEach(key => {
        const value = payload[key];
        // AppsFlyer accepts string, number, boolean values
        if (
          typeof value === 'string' ||
          typeof value === 'number' ||
          typeof value === 'boolean'
        ) {
          appsFlyerParams[key] = value;
        } else if (value !== null && value !== undefined) {
          // Convert objects/arrays to strings
          appsFlyerParams[key] = JSON.stringify(value);
        }
      });
    }

    // Log event with AppsFlyer
    appsFlyer.logEvent(
      name || '',
      appsFlyerParams,
      (result: any) => {
        // Success callback
        JMLogger.log(
          'AppsFlyer Event Success--',
          `Event sent successfully -- ${name}||${JSON.stringify(
            appsFlyerParams,
          )}`,
        );
      },
      (error: any) => {
        // Error callback
        const errorCode = error?.code || -1;
        const errorDesc =
          error?.message || error?.toString() || 'Unknown AppsFlyer error';

        JHExceptionLogger.log(
          'AppsFlyer Event Error--',
          `Event failed to be sent: -- ${name}||Error code: ${errorCode}\nError description: ${errorDesc}`,
        );
      },
    );
  } catch (e) {
    JHExceptionLogger.log('Unable to tag AppsFlyer analytics event:', e);
  }
};

const appendDefaultValuesToGAModel = async (gaModel: AnalyticsPayload) => {
  try {
    console.log('🚀 ~ appendDefaultValuesToGAModel 1 ~ gaModel:', gaModel);
    let [xLocationStr, userSessionStr, qcJourney]: any = await Promise.all([
      getPrefString(AsyncStorageKeys.X_LOCATION_DETAIL),
      getPrefString(AsyncStorageKeys.USER_SESSION),
      getPrefString(AsyncStorageKeys.QC_JOURNEY),
    ]);
    console.log(
      '🚀 ~ appendDefaultValuesToGAModel ~ xLocationStr:',
      xLocationStr,
    );

    xLocationStr = xLocationStr ? JSON.parse(xLocationStr) : {};
    userSessionStr = userSessionStr ? JSON.parse(userSessionStr) : {};

    const pinCode = xLocationStr?.pin;
    const userId = userSessionStr?.customer_id;
    const userStatus = JMSharedViewModel.Instance.getLoggedInStatus()
      ? 'Logged In'
      : 'Not Logged In';
    const platform = `app_${Platform.OS}_jiomart`;

    return {
      ...gaModel,
      [AnalyticsParams.PINCODE]: pinCode,
      [AnalyticsParams.LOGGED_USER_ID]: userId?.toFixed(0),
      [AnalyticsParams.TECH_STACK]: 'react_native',
      [AnalyticsParams.USER_ID]: userId,
      [AnalyticsParams.LOGIN_STATUS]: userStatus,
      [AnalyticsParams.PLATFORM]: platform,
      [AnalyticsParams.JOURNEY]:
        qcJourney === 'jiomart_quick'
          ? 'Quick'
          : qcJourney === 'scheduled_delivery'
          ? 'Scheduled JM'
          : 'JM REGULAR',
    };
  } catch (error) {
    console.error('Error appending default values to GA model:', error);
    return gaModel;
  }
};

const appendDefaultValuesToCTModel = async (ctModel: AnalyticsPayload) => {
  try {
    let [userSessionStr]: any = await Promise.all([
      getPrefString(AsyncStorageKeys.USER_SESSION),
    ]);

    userSessionStr = userSessionStr ? JSON.parse(userSessionStr) : {};

    const customerId = userSessionStr?.customer_id;
    const userStatus = JMSharedViewModel.Instance.getLoggedInStatus()
      ? 'Logged In'
      : 'Not Logged In';

    return {
      ...ctModel,
      [AnalyticsParams.CUSTOMER_ID]: customerId,
      [AnalyticsParams.CUSTOMER_TYPE]: userStatus,
      [AnalyticsParams.SYSTEM]: `app_${Platform.OS}_jiomart`,
    };
  } catch (error) {
    console.error('Error appending default values to CT model:', error);
    return ctModel;
  }
};

export const extractAnalyticDataFromHeaderConfig = ({
  screen,
  deeplinkIdentifier,
  type,
  gaConfig,
}: Partial<{
  screen: string;
  deeplinkIdentifier: string;
  type: string;
  gaConfig: any[];
}>) => {
  if (!gaConfig?.length) {
    return {};
  }

  for (const config of gaConfig) {
    const identifierList = config?.identifier?.split(',') || [];
    const gaType = config?.type;
    if (
      identifierList?.includes(screen || '') ||
      identifierList?.includes(deeplinkIdentifier || '') ||
      gaType === type
    ) {
      return config?.gaModel || {};
    }
  }
  return {};
};

export const logScreenEvent = (screenName: string) => {
  try {
    JMLogger.log('logScreenEvent ' + screenName);
    if (
      screenName === null ||
      screenName === undefined ||
      screenName.length === 0
    )
      return;
    (async () => {
      await analytics().logScreenView({
        screen_name: screenName,
        screen_class: screenName,
      });
    })();
  } catch (e) {
    JHExceptionLogger.log('Unable to tag analytics screen event:', e);
  }
};

const logAnalyticsEventOnGA = (name: string, payload: AnalyticsPayload) => {
  try {
    (async () => {
      try {
        const processedName =
          typeof name === 'string' ? name.replace(/\s+/g, '_') : name;
        JMLogger.log(
          ' === logAnalyticsEvent === ' +
            'logging name =>' +
            processedName +
            '||payload =>' +
            JSON.stringify(payload),
        );
        await analytics().logEvent(processedName ?? '', payload);
      } catch (e) {
        JHExceptionLogger.log('Unable to tag analytics event with Error:', e);
      }
    })();
  } catch (e) {
    JHExceptionLogger.log('Unable to tag analytics event:', e);
  }
};

/**
 * Log user id when user logs in
 * @param id
 */
const logUserId = async (id: string) => {
  try {
    await analytics().setUserId(id);
    JMLogger.log('logUserId:' + id);
  } catch (e) {
    JHExceptionLogger.log('Unable to tag analytics user id event:', e);
  }
};

/**
 * Set user properties
 * @param props keys 24 char max alphanumeric and '_' only starts alpha, values 26 char max
 */
const logUserProperties = async (props: {[key: string]: string | null}) => {
  try {
    await analytics().setUserProperties(props);
    console.log('params', props);
  } catch (e) {
    console.log('Unable to tag analytics user properties event:', e);
  }
};

/**
 * Clears all analytics data for this instance
 * from the device and resets the app instance ID
 */
const logSignOut = async () => {
  try {
    await analytics().resetAnalyticsData();
  } catch (e) {
    console.log(
      'Unable to tag analytics signout reset anlytics data event:',
      e,
    );
  }
};
