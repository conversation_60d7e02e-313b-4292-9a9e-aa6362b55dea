import {useFocusEffect} from '@react-navigation/native';
import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {ActivityIndicator, BackHandler, Text, View} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import JMDeliverToBarBtmSheet from '../../../jiomart-address/src/BottomSheet/JMDeliverToBarBtmSheet';
import JMDeliverToPincodeBtmSheet from '../../../jiomart-address/src/BottomSheet/JMDeliverToPincodeBtmSheet';
import useDeliverToBarBtmSheet from '../../../jiomart-address/src/hooks/useDeliverToBarBtmSheet';
import usePincodeChange from '../../../jiomart-address/src/hooks/usePincodeChange';
import {AppScreens} from '../../../jiomart-common/src/JMAppScreenEntry';
import {AuthState} from '../../../jiomart-common/src/JMAuthType';
import {EventEmitterKeys} from '../../../jiomart-common/src/JMConstants';
import {
  ActionType,
  getHomeAppScreenDestination,
  navBeanObj,
  NavigationType,
  type NavigationBean,
} from '../../../jiomart-common/src/JMNavGraphUtil';
import networkService from '../../../jiomart-common/src/JMNetworkConnectionUtility';
import useNetworkController from '../../../jiomart-common/src/JMNetworkController';
import {isNullOrUndefinedOrEmpty} from '../../../jiomart-common/src/JMObjectUtility';
import {
  DeeplinkProps,
  GenericToast,
  genericToastTypeData,
  HeaderType,
  ScreenSlotProps,
} from '../../../jiomart-common/src/JMScreenSlot.types';
import {JMSharedViewModel} from '../../../jiomart-common/src/JMSharedViewModel';
import {JMAddressModel} from '../../../jiomart-common/src/uiModals/JMAddressModel';
import {JMLogger} from '../../../jiomart-common/src/utils/JMLogger';
import {capitalizeFirstLetter} from '../../../jiomart-common/src/utils/JMStringUtility';
import {checkPinCodeGivenIfPermissionIsNotGiven} from '../../../jiomart-common/src/utils/JMLocationUtility';
import useGlobalBottomSheetController from '../../../jiomart-main/src/features/StartUp/controllers/useGlobalBottomSheetController';
import {JMDatabaseManager} from '../../../jiomart-networkmanager/src/db/JMDatabaseManager';
import {JMConfigFileName} from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import {
  getBaseURL,
  isHomeUrl,
} from '../../../jiomart-networkmanager/src/JMEnvironmentConfig';
import {useGlobalState} from '../context/JMGlobalStateProvider';
import {
  getInitialNavBean,
  handleDeeplinkIntent,
  updatedNavBeanData,
} from '../deeplink/JMDeeplinkUtility';
import {useConfigFile} from '../hooks/useJMConfig';
import {checkRouteExist, navigateTo} from '../navigation/JMNavGraph';
import CustomBottomNavBar from './BottomNavBar/CustomBottomNavBar';
import BottomSheet from './BottomSheet/BottomSheet';
import {CustomStatusBar} from './CustomStatusBar';
import DeliverToBar from './DeliverToBar/DeliverToBar';
import JioMartHeader, {NavigationBeanType} from './Header/JioMartHeader';
import JMToast from './JMToast';
import QCSlidingTab from './Qc/QCSlidingTab';
import {
  extractAnalyticDataFromHeaderConfig,
  logAnalyticsEvent,
} from '../../../jiomart-common/src/JMAnalyticsUtility';
import {EventTriggerChannel} from '@jm/jio-mart/jiomart-common/src/AnalyticsParams';
import CustomMediaRendered from './CustomMediaRendered';
import {getScreenDim} from '../../../jiomart-common/src/JMResponsive';
import JMLoader from './JMLoader';
import useOpenDeliverToBarBtmSheet from '../../../jiomart-address/src/hooks/useOpenDeliverToBarBtmSheet';
import {WebFunctionQueue} from '../../../jiomart-webmanager/src/util/WebFunctionQueue';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';

export const userAuthenticationErrorState = () => {
  return (
    <View>
      <Text>Authentication failed</Text>
    </View>
  );
};
export const loadingState = () => {
  return (
    <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
      <CustomMediaRendered
        mediaUrl="https://myjiostatic.cdn.jio.com/JioMart/Common/Jiomart_loader.json"
        width={54}
        height={54}
        customStyles={{width: 54, height: 54}}
      />
    </View>
  );
};

export const createEventFinalBean = (
  bean: NavigationBean,
  eventTriggerBean: NavigationBean,
): NavigationBean => {
  const baseBean = {...bean};
  return {
    ...baseBean,
    ...(eventTriggerBean?.headerType
      ? {headerType: eventTriggerBean?.headerType}
      : {}),
    ...(eventTriggerBean?.shouldShowDeliverToBar
      ? {shouldShowDeliverToBar: eventTriggerBean?.shouldShowDeliverToBar}
      : {}),
    ...(eventTriggerBean?.navTitle
      ? {navTitle: eventTriggerBean?.navTitle}
      : {}),
    ...(eventTriggerBean?.shouldShowBottomNavBar
      ? {shouldShowBottomNavBar: eventTriggerBean?.shouldShowBottomNavBar}
      : {}),
  };
};

export function SendWebJavaScriptFunctions(
  setEvent: React.Dispatch<
    React.SetStateAction<{
      [key: string]: any;
    }>
  >,
  navigation?: NativeStackNavigationProp<any>,
  eventName?: string,
  eventValue?: any,
) {
  if (eventName && eventValue) {
    if (navigation && checkRouteExist(navigation)) {
      setEvent({
        [EventEmitterKeys.WEB_VIEW_EVENT_EMITT]: eventValue,
      });
    } else {
      JMLogger.log(
        'webJavaScriptFunctionsMap eventName ' +
          eventName +
          ' eventValue ' +
          JSON.stringify(eventValue),
      );
      WebFunctionQueue.getInstance().setDataInWebJavaScriptFunctionsMap(
        eventName,
        eventValue,
      );
    }
  }
}

export function DeeplinkHandler(props: DeeplinkProps): JSX.Element {
  let [navigationBeanData, setNavigationBeanData] = useState(
    getInitialNavBean(props.navigationBean),
  );

  useEffect(() => {
    // will check user is loggedin or not and set the value accordingly
    updatedNavBeanData(
      JMSharedViewModel.Instance.deeplinkUrl,
      navigationBeanData,
    ).then(bean => {
      if (bean) {
        setNavigationBeanData(bean);
      }
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return <>{props?.children ? props.children(navigationBeanData) : null}</>;
}

const usePinCodeChangeController = (
  navigation: NativeStackNavigationProp<any>,
) => {
  const {setEvent, setAddress} = useGlobalState();
  const handleAddress = async (initialAddress?: JMAddressModel) => {
    let address: any = await JMDatabaseManager.address.getDefaultAddress();
    JMLogger.log('handleAddress ', 'handleAddress ' + JSON.stringify(address));
    address = !address ? initialAddress : JSON.parse(address ?? '');
    setAddress(address);
  };

  usePincodeChange(async event => {
    console.log('usePincodeChange--', event);
    if (event?.type === 'pincode') {
      console.log('🚀 ~ event?.addressId:', event?.pin);
      SendWebJavaScriptFunctions(
        setEvent,
        navigation,
        'addressModifiedOnNative',
        {
          pincodeModifiedOnNative: `'${JSON.stringify({
            pin: event?.pin,
          })}'`,
        },
      );
    } else {
      console.log('🚀 ~ event?.addressId:', event?.addressId);
      SendWebJavaScriptFunctions(
        setEvent,
        navigation,
        'addressModifiedOnNative',
        {
          addressModifiedOnNative: `'${JSON.stringify({
            addressId: event?.addressId,
          })}'`,
        },
      );
    }
    await handleAddress();
  });
};

// Memoize JioMartHeader and DeliverToBar if not already memoized
const MemoizedJioMartHeader = React.memo(JioMartHeader);
const MemoizedDeliverToBar = React.memo(DeliverToBar);
const MemoizedCustomBottomNavBar = React.memo(CustomBottomNavBar);

const ScreenSlot = (props: ScreenSlotProps) => {
  const {
    navigationBean,
    statusBarColor,
    deliverToBarData,
    setBSNonCancellableValue,
    bottomsheetDismissable,
    enableOpenPincodeBtmSheetListener,
    shouldShowNoInternetToast=true,
    updateGAModel
  } = props;

  if (enableOpenPincodeBtmSheetListener) {
    useOpenDeliverToBarBtmSheet(() => {
      openDeliverToBarPincodeBtmSheet(true);
    });
  }

  const {checkAndOpenNextSheet} = useGlobalBottomSheetController();
  usePinCodeChangeController(props.navigation);
  const header: any = useConfigFile(
    JMConfigFileName.JMHeaderConfigurationFileName,
  );
  // console.log('ScreenSlot rendered');
  let headerConfig = header?.[props.navigationBean?.headerType ?? '2'];
  const gaConfig = header?.gaConfig;
  const isOrderReview = props.navigationBean?.params?.orderReviewFlow ?? false;
  const {
    userAuthenticationStatus,
    toastTypeData,
    address,
    setToastTypeData,
    deeplinkData,
    setDeeplinkData,
    permissionBtmSheet,
    privacyPolicyBtmSheet,
    setPrivacyPolicyBtmSheet,
    centralLoader,
    setCentralLoader,
  } = useGlobalState();
  const {
    showDeliverToBarBtmSheet,
    showDeliverToBarPincodeBtmSheet,
    openDeliverToBarBtmSheet,
    openDeliverToBarPincodeBtmSheet,
    closeDeliverToBarBtmSheet,
    closeDeliverToBarPincodeBtmSheet,
    showCloseBtton,
  } = useDeliverToBarBtmSheet();

  // Memoize deliverToBarText to avoid recalculation unless address changes
  const deliverToBarText = useMemo(() => {
    if (JMSharedViewModel.Instance.loggedInStatus === true) {
      const textData = [
        address?.flat_or_house_no,
        address?.floor_no,
        address?.area,
        address?.address,
        address?.landmark,
      ]
        .filter(item => !isNullOrUndefinedOrEmpty(item))
        .join(', ');
      if (!isNullOrUndefinedOrEmpty(textData)) {
        return textData;
      } else if (address?.address) {
        return address?.address;
      } else {
        return (
          capitalizeFirstLetter(address?.city ?? '') +
          ' ' +
          (address?.pin ?? '')
        );
      }
    } else if (address?.address) {
      return address?.address;
    } else {
      return (
        capitalizeFirstLetter(address?.city ?? '') + ' ' + (address?.pin ?? '')
      );
    }
  }, [address]);

  const navigation = props.navigation;
  const insets = useSafeAreaInsets();
  const {isNetworkConnected} = useNetworkController();

  useEffect(() => {
    if (
      !isNullOrUndefinedOrEmpty(JMSharedViewModel.Instance.externalDeeplinkData)
    ) {
      const timer = setTimeout(() => {
        if (JMSharedViewModel.Instance.externalDeeplinkData) {
          const copyDeeplinkData = {
            ...JMSharedViewModel.Instance.externalDeeplinkData,
          };
          JMSharedViewModel.Instance.setExternalDeeplink(undefined);
          setDeeplinkData(copyDeeplinkData);
        }
      }, 500);
      return () => clearTimeout(timer);
    }
  }, []);

  useFocusEffect(
    React.useCallback(() => {
      // This will run only when ScreenB is in focus
      const handleDeeplink = async () => {
        const showAnimationOnDeeplink =
          JMSharedViewModel.Instance.getShowAnimationOnDeeplink();
        if (deeplinkData && !isNullOrUndefinedOrEmpty(deeplinkData)) {
          const pinCodeBottomSheetIsVisible =
            await checkPinCodeGivenIfPermissionIsNotGiven();
          if (pinCodeBottomSheetIsVisible) {
            JMSharedViewModel.Instance.setShowAnimationOnDeeplink(0);
            setDeeplinkData(undefined);
          } else if (deeplinkData.cta) {
            const navBean = navBeanObj(deeplinkData.cta);
            if (
              navBean.destination === AppScreens.COMMON_WEB_VIEW &&
              showAnimationOnDeeplink !== 0
            ) {
              navBean.animation = 'none';
              JMSharedViewModel.Instance.setShowAnimationOnDeeplink(2);
            } else {
              JMSharedViewModel.Instance.setShowAnimationOnDeeplink(0);
            }
            setDeeplinkData(undefined);
            console.log('ScreenSlot deeplinkData navBeanData navigateTo');
            navigateTo(navBean, props.navigation);
          } else {
            const navBeanData = await handleDeeplinkIntent(deeplinkData);
            if (navBeanData) {
              if (navBeanData?.actionUrl && isHomeUrl(navBeanData.actionUrl)) {
                navigateTo(
                  navBeanObj({
                    actionType: ActionType.OPEN_WEB_URL,
                    destination: getHomeAppScreenDestination(),
                    headerVisibility: HeaderType.CUSTOM,
                    navigationType: NavigationType.RESET,
                    loginRequired: false,
                    actionUrl: getBaseURL(),
                    shouldShowBottomNavBar: false,
                    shouldShowDeliverToBar: true,
                    headerType: 5,
                  }),
                  navigation,
                );
              } else {
                console.log(
                  'ScreenSlot deeplinkData navBeanData ' +
                    JSON.stringify(navBeanData),
                );
                if (
                  navBeanData.destination === AppScreens.COMMON_WEB_VIEW &&
                  showAnimationOnDeeplink !== 0
                ) {
                  navBeanData.animation = 'none';
                  JMSharedViewModel.Instance.setShowAnimationOnDeeplink(2);
                } else {
                  JMSharedViewModel.Instance.setShowAnimationOnDeeplink(0);
                }
                setDeeplinkData(undefined);
                console.log('ScreenSlot deeplinkData navBeanData navigateTo');
                navigateTo(navBeanData, props.navigation);
              }
            } else {
              console.log('ScreenSlot deeplinkData navBeanData Not found');
              JMSharedViewModel.Instance.setShowAnimationOnDeeplink(0);
              setDeeplinkData(undefined);
            }
          }
        }
      };
      console.log('ScreenSlot deeplinkData ' + deeplinkData);
      handleDeeplink();

      return () => {
        // Clean up or perform any action when ScreenB is unfocused
        // setDeeplinkData(undefined);
        console.log('ScreenSlot deeplinkData cleanup ' + deeplinkData);
      };
    }, [deeplinkData]),
  );

  // Memoize onBackPress to avoid recreation
  const onBackPress = useCallback(() => {
    if (
      permissionBtmSheet ||
      privacyPolicyBtmSheet ||
      (showDeliverToBarPincodeBtmSheet && !showCloseBtton)
    ) {
      if (privacyPolicyBtmSheet) {
        setPrivacyPolicyBtmSheet(false);
      }
      return true;
    } else if (isOrderReview) {
      if (props.showCustomViewOnBackPress) {
        props.showCustomViewOnBackPress();
      }
      return true;
    } else if (props.onCustomBackPress) {
      props.onCustomBackPress();
    } else if (navigation.canGoBack()) {
      navigation.goBack();
    } else {
      networkService.stopMonitoring();
      BackHandler.exitApp();
    }
    props.onBackPressCallback?.();
    return true;
  }, [
    permissionBtmSheet,
    privacyPolicyBtmSheet,
    showDeliverToBarPincodeBtmSheet,
    showCloseBtton,
    setPrivacyPolicyBtmSheet,
    props.onCustomBackPress,
    navigation,
    networkService,
    props.onBackPressCallback,
  ]);

  const handleHeaderCtaIconPress = (cta: NavigationBean) => {
    let updatedCta: NavigationBean = updateGAModel?.(cta) ?? cta;
    switch (updatedCta?.type) {
      case NavigationBeanType.BACK:
        logAnalyticsEvent({
          eventName: 'header',
          channel: `${EventTriggerChannel.FIREBASE}`,
          payload: {
            action: 'back clicked',
            page_type: navigationBean.navTitle,
            category: 'header',
            label: 'header_back_clicked',
          },
        });
        onBackPress();
        break;
      default:
        const gaModelData = extractAnalyticDataFromHeaderConfig({
          screen: cta?.destination ?? '',
          deeplinkIdentifier: cta?.actionUrl ?? '',
          type: cta?.type ?? '',
          gaConfig,
        });
        console.log('gaModelData', gaModelData);
        updatedCta = {
          ...updatedCta,
          analyticEvent: {...(updatedCta?.analyticEvent ?? {}), ...gaModelData},
        };
        console.log('gaModelData', updatedCta);
        navigateTo(
          navBeanObj({
            ...updatedCta,
            navigationType:
              NavigationType[
                (
                  updatedCta?.navigationType ?? NavigationType.PUSH
                ).toUpperCase() as keyof typeof NavigationType
              ],
            actionUrl: `${getBaseURL() + '/' + updatedCta?.actionUrl}`,
            headerType: updatedCta?.headerType ?? 1,
            userJourneyRequiredState: updatedCta?.userJourneyRequiredState ?? 0,
          }),
          navigation,
        );
        break;
    }
  };

  // Memoize searchTextHandler
  const searchTextHandler = useCallback(
    (text: string) => {
      props.searchTextHandler?.(text);
      // store in redux if value !=
    },
    [props.searchTextHandler],
  );

  // Memoize onSubmitHandler
  const onSubmitHandler = useCallback(
    (text: string) => {
      props.onSubmitHandler?.(text);
    },
    [props.onSubmitHandler],
  );

  useEffect(() => {
    if(shouldShowNoInternetToast){
      if (isNetworkConnected === false) {
        setToastTypeData(genericToastTypeData(GenericToast.NO_INTERNET));
      } else if (toastTypeData?.genericToastType === GenericToast.NO_INTERNET) {
        setToastTypeData(undefined);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isNetworkConnected]);

  useFocusEffect(
    useCallback(() => {
      const backHandler = BackHandler.addEventListener(
        'hardwareBackPress',
        onBackPress,
      );
      return () => backHandler.remove();
    }, [onBackPress]),
  );

  useEffect(() => {
    const unsubscription = navigation.addListener('focus', () => {
      closeDeliverToBarBtmSheet();
      closeDeliverToBarPincodeBtmSheet();
      setToastTypeData(undefined);
    });
    return () => {
      closeDeliverToBarBtmSheet();
      closeDeliverToBarPincodeBtmSheet();
      unsubscription();
    };
  }, []);

  function getLocalUserAuthStatus(
    userAuthenticationStatus: AuthState,
  ): AuthState {
    if (
      (props.navigationBean?.userAuthenticationRequired === 1 &&
        userAuthenticationStatus === AuthState.SESSION_CREATED) ||
      props.navigationBean?.userAuthenticationRequired === 0
    ) {
      return AuthState.AUTHENTICATED;
    }
    return userAuthenticationStatus;
  }

  // Memoize the rendered children to avoid unnecessary re-renders
  const renderedChildren = useMemo(() => {
    if (props?.children) {
      return props.children(AuthState.AUTHENTICATED);
    }
    return null;
  }, [props.children]);

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: '#ffffff',
      }}>
      <CustomStatusBar
        color={
          statusBarColor ?? props.navigationBean?.params?.customStatusBarColor
        }
      />
      {headerConfig && (
        <MemoizedJioMartHeader
          data={headerConfig}
          navTitle={props?.navigationBean?.navTitle ?? ''}
          headerTopComponent={
            headerConfig?.isQCEnabled ? (
              <QCSlidingTab navigation={props.navigation} />
            ) : null
          }
          searchValue={props?.navigationBean?.params?.['searchValue'] ?? ''}
          headerBottomComponent={
            props?.navigationBean?.shouldShowDeliverToBar ? (
              <MemoizedDeliverToBar
                ref={props?.onDeliverToBarRef}
                text={deliverToBarText}
                onPress={() => {
                  openDeliverToBarBtmSheet();
                  logAnalyticsEvent({
                    eventName: 'header',
                    channel: `${EventTriggerChannel.FIREBASE}`,
                    payload: {
                      action: 'deliver to clicked',
                      page_type: 'Home',
                      category: 'header',
                      label: 'header_deliver_to_clicked',
                    },
                  });
                }}
                deliverToBarData={deliverToBarData}
              />
            ) : null
          }
          onPress={handleHeaderCtaIconPress}
          searchTextHandler={searchTextHandler}
          onSubmitHandler={onSubmitHandler}
          {...props?.header}
        />
      )}
      {renderedChildren}
      {toastTypeData !== undefined && (
        <JMToast
          message={toastTypeData?.message}
          isVisible={toastTypeData.isVisible}
          semanticState={toastTypeData?.semanticState}
          duration={toastTypeData?.duration}
          showClose={toastTypeData?.showClose}
          type={toastTypeData?.type}
          offset={insets.top}
          showButton={toastTypeData?.showButton}
          buttonText={toastTypeData?.buttonText}
          onClick={() => {
            if (toastTypeData?.cta) {
              navigateTo(toastTypeData?.cta, navigation);
            } else {
              toastTypeData?.onClick?.();
            }
            setToastTypeData(undefined);
          }}
          onDismiss={() => {
            setToastTypeData(undefined);
          }}
        />
      )}

      {props?.navigationBean?.shouldShowBottomNavBar ? (
        <MemoizedCustomBottomNavBar />
      ) : null}

      <BottomSheet
        visible={showDeliverToBarBtmSheet}
        enableKeyboarAvoidingView
        onBackDropClick={closeDeliverToBarBtmSheet}
        onDrag={closeDeliverToBarBtmSheet}>
        <JMDeliverToBarBtmSheet
          onClose={closeDeliverToBarBtmSheet}
          openDeliverToBarPincode={() => {
            setTimeout(() => {
              openDeliverToBarPincodeBtmSheet(true);
              setBSNonCancellableValue?.(false);
            }, 300);
          }}
        />
      </BottomSheet>
      <BottomSheet
        visible={showDeliverToBarPincodeBtmSheet}
        enableKeyboarAvoidingView
        onBackDropClick={closeDeliverToBarPincodeBtmSheet}
        onDrag={closeDeliverToBarPincodeBtmSheet}
        {...bottomsheetDismissable}>
        <JMDeliverToPincodeBtmSheet
          showCloseButton={showCloseBtton}
          onClose={() => {
            closeDeliverToBarPincodeBtmSheet();
            if (bottomsheetDismissable?.disabledBackDropClick) {
              checkAndOpenNextSheet();
            }
          }}
        />
      </BottomSheet>
      {centralLoader && (
        <JMLoader
          visible={centralLoader}
          media={{
            mediaUrl:
              'https://myjiostatic.cdn.jio.com/JioMart/Common/Jiomart_loader.json',
          }}
        />
      )}

      {props?.bottomSheetContent?.({
        openDeliverToBarBtmSheet,
        openDeliverToBarPincodeBtmSheet,
      })}
    </View>
  );
};

export default ScreenSlot;
