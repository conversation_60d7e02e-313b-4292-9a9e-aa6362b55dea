import { useMutation, useQueryClient } from '@tanstack/react-query';
import { <PERSON><PERSON><PERSON><PERSON> } from '../../../jiomart-common/src/JMConstants';
import { JMSharedViewModel } from '../../../jiomart-common/src/JMSharedViewModel';
import { AppSourceType } from '../../../jiomart-common/src/SourceType';
import type { JMAddressModel } from '../../../jiomart-common/src/uiModals/JMAddressModel';
import { JMLogger } from '../../../jiomart-common/src/utils/JMLogger';
import { JMDatabaseManager } from '../../../jiomart-networkmanager/src/db/JMDatabaseManager';
import JMAddressNetworkController from '../../../jiomart-networkmanager/src/JMNetworkController/JMAddressNetworkController';
import { emitPincodeChange } from '../Event';
import { normalizeJMAddress, updateDBAddress } from '../utils';

const addressController = new JMAddressNetworkController();

const useAddressOperation = () => {
  const queryClient = useQueryClient();

  const getPincodeStatus = useMutation({
    mutationFn: addressController.fetchPincodeCity,
    retry: 0,
  });

  const setDefaultAddressFromList = async (variable: any) => {
    let formatedAddress: any;
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        formatedAddress = normalizeJMAddress({
          is_default_address: variable?.is_default_address,
          id: variable?.id,
          pin: variable?.pincode,
          name: variable?.name,
          phone: variable?.phone,
          address: variable?.address,
          area: variable?.area,
          city: variable?.city,
          state: variable?.state,
          flat_or_house_no: variable?.flat_or_house_no,
          floor_no: variable?.floor_no,
          landmark: variable?.landmark
        });
        updateDBAddress(formatedAddress);
        emitPincodeChange({
          type: 'address',
          addressId: variable?.id,
        });
        break;
      case AppSourceType.JM_BAU:
        // bau formated need
        formatedAddress = normalizeJMAddress({
          is_default_address: variable?.is_default_address,
          id: variable?.id,
          pin: variable?.pincode,
          name: variable?.name,
          phone: variable?.phone,
          address: variable?.address,
          area: variable?.area,
          city: variable?.city,
          state: variable?.state,
          flat_or_house_no: variable?.flat_or_house_no,
          floor_no: variable?.floor_no,
          landmark: variable?.landmark
        });
        updateDBAddress(formatedAddress);
        emitPincodeChange({
          type: 'address',
          addressId: variable?.id,
        });
        break;
    }
  }
  const setDefaultAddress = (request: any) => {
    queryClient.invalidateQueries({
      queryKey: [RQKey.GET_ADDRESS],
    });
    setDefaultAddressFromList(request)
    setDefaultAddressApi.mutate(request);
  }
  const setDefaultAddressApi = useMutation({
    mutationFn: addressController.defaultAddress,
    onSuccess: (response: any, variable) => {
      if (response?.success || response?.status === 'success') {
        queryClient.invalidateQueries({
          queryKey: [RQKey.GET_ADDRESS],
        });
      }
    },
    retry: 0,
  });
  const removeAddress = useMutation({
    mutationFn: addressController.removeAddress,
    onSuccess: (response: any) => {
      if (response?.is_deleted) {
        queryClient.invalidateQueries({
          queryKey: [RQKey.GET_ADDRESS],
        });
      }
    },
    retry: 0,
  });
  const saveAddress = useMutation({
    mutationFn: addressController.insertAddress,
    onSuccess: (response: any, variable) => {
      if (response?.success) {
        queryClient.invalidateQueries({
          queryKey: [RQKey.GET_ADDRESS],
        });
        let formatedAddress: any;
        switch (JMSharedViewModel.Instance.appSource) {
          case AppSourceType.JM_JCP:
            formatedAddress = normalizeJMAddress({
              is_default_address: variable?.is_default_address,
              id: variable?.id,
              pin: variable?.area_code,
              name: variable?.name,
              phone: variable?.phone,
              address: variable?.address,
              area: variable?.area,
              city: variable?.city,
              state: variable?.state,
              flat_or_house_no: variable?.flat_or_house_no,
              floor_no: variable?.floor_no,
              landmark: variable?.landmark
            });
            updateDBAddress(formatedAddress);
            emitPincodeChange({
              type: 'address',
              addressId: variable?.id,
            });
            break;
          case AppSourceType.JM_BAU:
            // bau formated need
            formatedAddress = normalizeJMAddress({
              is_default_address: variable?.is_default_address,
              id: variable?.id,
              pin: variable?.area_code,
              name: variable?.name,
              phone: variable?.phone,
              address: variable?.address,
              area: variable?.area,
              city: variable?.city,
              state: variable?.state,
              flat_or_house_no: variable?.flat_or_house_no,
              floor_no: variable?.floor_no,
              landmark: variable?.landmark
            });
            updateDBAddress(formatedAddress);
            emitPincodeChange({
              type: 'address',
              addressId: variable?.id,
            });
            break;
        }
      }
    },
    retry: 0,
  });
  const updateAddress = useMutation({
    mutationFn: addressController.editAddress,
    onSuccess: (response: any, variable) => {
      if (response?.success) {
        queryClient.invalidateQueries({
          queryKey: [RQKey.GET_ADDRESS],
        });
        let formatedAddress: any;
        switch (JMSharedViewModel.Instance.appSource) {
          case AppSourceType.JM_JCP:
            formatedAddress = normalizeJMAddress({
              is_default_address: variable?.is_default_address,
              id: variable?.id,
              pin: variable?.area_code,
              name: variable?.name,
              phone: variable?.phone,
              address: variable?.address,
              area: variable?.area,
              city: variable?.city,
              state: variable?.state,
              flat_or_house_no: variable?.flat_or_house_no,
              floor_no: variable?.floor_no,
              landmark: variable?.landmark
            });
            updateDBAddress(formatedAddress);
            emitPincodeChange({
              type: 'address',
              addressId: variable?.id,
            });
            break;
          case AppSourceType.JM_BAU:
            // bau formated need
            formatedAddress = normalizeJMAddress({
              is_default_address: variable?.is_default_address,
              id: variable?.id,
              pin: variable?.area_code,
              name: variable?.name,
              phone: variable?.phone,
              address: variable?.address,
              area: variable?.area,
              city: variable?.city,
              state: variable?.state,
              flat_or_house_no: variable?.flat_or_house_no,
              floor_no: variable?.floor_no,
              landmark: variable?.landmark
            });
            updateDBAddress(formatedAddress);
            emitPincodeChange({
              type: 'address',
              addressId: variable?.id,
            });
            break;
        }
      }
    },
    retry: 0,
  });


  const checkAndSetPincode = async (requestData: any, onlyInCaseOfLoggedIn: boolean = false) => {
    try {
      const {pincode, state, city} = requestData;
      const isUserLoggedIn = JMDatabaseManager.user.isUserLoggedInFlag();
      const parseAddress = isUserLoggedIn
        ? (await addressController.getAddressList(false)) ?? []
        : [];
      const addressPresentInList = parseAddress?.find(
        (it: JMAddressModel) => it.pin === pincode,
      );
      if (addressPresentInList && isUserLoggedIn) {
        const request = generateDefaultAddressRequestBody(addressPresentInList);
        setDefaultAddress(request);
        return request
      } else if (!onlyInCaseOfLoggedIn) {
        const changePincodeDetail: JMAddressModel = {
          pin: pincode,
          state,
          city,
        };
        updateDBAddress(changePincodeDetail);
        emitPincodeChange({
          type: 'pincode',
          pin: pincode,
        });
      } else if (requestData?.pin && requestData?.address && JMSharedViewModel.Instance.loggedInStatus !== true) {
        updateDBAddress(requestData);
        emitPincodeChange({
          type: 'pincode',
          pin: requestData?.pin,
        });
      }
    } catch (error) {
      JMLogger.log("checkAndSetPincode ", "checkAndSetPincode error " + error)
    }
  };


  const getAddressFromId = async (addressId: any) => {
    try {
      const isUserLoggedIn = JMDatabaseManager.user.isUserLoggedInFlag()
      const parseAddress = isUserLoggedIn ? (await addressController.getAddressList(false)) ?? [] : [];
      const addressPresentInList = parseAddress?.find(
        (it: JMAddressModel) => it.id === addressId,
      );
      if (addressPresentInList && isUserLoggedIn) {
        return addressPresentInList
      }
    } catch (error) {
      JMLogger.log("checkAndSetPincode ", "checkAndSetPincode error " + error)
    }
  };

  const generateDefaultAddressRequestBody = (item: JMAddressModel) => {
    console.log('🚀 ~ generateDefaultAddressRequestBody ~ item:', item);
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        return {
          id: item?.id,
          is_default_address: true,
          pincode: item?.pin,
          name: item?.name,
          phone: item?.phone,
          address: item?.address,
          area: item?.area,
          city: item?.city,
          state: item?.state,
          flat_or_house_no: item?.flat_or_house_no,
          floor_no: item?.floor_no,
          landmark: item?.landmark,
        };
      case AppSourceType.JM_BAU:
        return {
          id: item?.id,
          is_default_address: true,
          pincode: item?.pin,
          name: item?.name,
          phone: item?.phone,
          address: item?.address,
          area: item?.area,
          city: item?.city,
          state: item?.state,
          flat_or_house_no: item?.flat_or_house_no,
          floor_no: item?.floor_no,
          landmark: item?.landmark
        };
      default:
        throw new Error('app source not found');
    }
  };
  const generateRemoveAddressRequestBody = (item: JMAddressModel) => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        return {id: item?.id};
      case AppSourceType.JM_BAU:
        return {id: item?.id};
      default:
        throw new Error('app source not found');
    }
  };
  const generateSaveAddressRequestBody = (item: JMAddressModel) => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        const _custom_json = {
          flat_or_house_no: item?.flat_or_house_no,
          floor_no: item?.floor_no,
          tower_no: item?.tower_no,
          input_mode: item?.input_mode,
        };
        const geo_location = {
          longitude: item?.lon,
          latitude: item?.lat,
        };
        const bodyParam = {
          is_default_address: item?.is_default_address,
          name: item?.name,
          phone: item?.phone,
          address: item?.address,
          area: item?.area,
          landmark: item?.landmark,
          area_code: item?.pin,
          address_type: item?.address_type,
          geo_location: geo_location,
          _custom_json,
          city: item?.city,
          state: item?.state,
        };
        return bodyParam;
      case AppSourceType.JM_BAU:
        const body: any = {};
        if (item?.name) body.addressee_name = item.name;
        if (item?.phone) body.mobile_no = item.phone;
        if (item?.pin) body.pin = item.pin;
        if (item?.city) body.city = item.city;
        if (item?.state) body.state = item.state;
        if (item?.address) body.building_address = item.address;
        if (item?.landmark) body.area_name = item.landmark;
        if (item?.flat_or_house_no) body.flat_or_house_no = item.flat_or_house_no;
        if (item?.tower_no) body.tower_no = item.tower_no;
        if (item?.floor_no) body.floor_no = item.floor_no;
        if (item?.area) body.building_name = item.area;
        if (item?.address_type) body.address_type = item.address_type;
        if (item?.lat) body.lat = item.lat;
        if (item?.lon) body.lon = item.lon;
        if (item?.input_mode) body.input_mode = item.input_mode;

        // Special case for "Other"
        if (item?.address_type === 'Other' && item?.address_type_other) {
          body.address_type_other = item.address_type_other;
        }

        return body;
      default:
        throw new Error('app source not found');
    }
  };
  const generateUpdateAddressRequestBody = (item: JMAddressModel) => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        const _custom_json = {
          flat_or_house_no: item?.flat_or_house_no,
          floor_no: item?.floor_no,
          tower_no: item?.tower_no,
          input_mode: item?.input_mode,
        };
        const geo_location = {
          longitude: item?.lon,
          latitude: item?.lat,
        };
        const bodyParam = {
          id: item?.id,
          is_default_address: item?.is_default_address,
          name: item?.name,
          phone: item?.phone,
          address: item?.address,
          area: item?.area,
          landmark: item?.landmark,
          area_code: item?.pin,
          address_type: item?.address_type,
          geo_location: geo_location,
          _custom_json,
          city: item?.city,
          state: item?.state,
        };
        return bodyParam;
      case AppSourceType.JM_BAU:
        const body: any = {
          id: item?.id,
        };

        if (item?.name) body.addressee_name = item.name;
        if (item?.phone) body.mobile_no = item.phone;
        if (item?.pin) body.pin = item.pin;
        if (item?.city) body.city = item.city;
        if (item?.state) body.state = item.state;
        if (item?.address) body.building_address = item.address;
        if (item?.landmark) body.area_name = item.landmark;
        if (item?.flat_or_house_no)
          body.flat_or_house_no = item.flat_or_house_no;
        if (item?.tower_no) body.tower_no = item.tower_no;
        if (item?.floor_no) body.floor_no = item.floor_no;
        if (item?.area) body.building_name = item.area;
        if (item?.address_type) body.address_type = item.address_type;
        if (item?.lat) body.lat = item.lat;
        if (item?.lon) body.lon = item.lon;
        if (item?.input_mode) body.input_mode = item.input_mode;

        // Special case for "Other"
        if (item?.address_type === 'Other' && item?.address_type_other) {
          body.address_type_other = item.address_type_other;
        }

        return body;
      default:
        throw new Error('app source not found');
    }
  };

  return {
    getPincodeStatus,
    setDefaultAddress,
    setDefaultAddressApi,
    removeAddress,
    saveAddress,
    updateAddress,
    checkAndSetPincode,
    getAddressFromId,
    generateDefaultAddressRequestBody,
    generateRemoveAddressRequestBody,
    generateSaveAddressRequestBody,
    generateUpdateAddressRequestBody,
    setDefaultAddressFromList,
  };
};

export default useAddressOperation;
