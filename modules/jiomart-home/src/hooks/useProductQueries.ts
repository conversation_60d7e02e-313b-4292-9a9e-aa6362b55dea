import {useQuery, useQueries} from '@tanstack/react-query';
import {useEffect, useMemo, useState} from 'react';
import SearchService from '../../../jiomart-networkmanager/src/JMNetworkController/JMSearchAPINetworkController';
import JMHomeNetworkController from '../../../jiomart-networkmanager/src/JMNetworkController/JMHomeNetworkController';
import {getPrefString} from '../../../jiomart-common/src/JMAsyncStorageHelper';
import {AsyncStorageKeys} from '../../../jiomart-common/src/JMConstants';
import {isNullOrUndefinedOrEmpty} from '../../../jiomart-common/src/JMObjectUtility';
import {JMDatabaseManager} from '../../../jiomart-networkmanager/src/db/JMDatabaseManager';
import {getTopDealsWidgets, getWidgetProducts} from '../utils/jmDashboardUtils';
import {
  MyListOffersProduct,
  MyListOffersWidgetRenderParsedDataMap,
} from '../../../jiomart-networkmanager/src/models/home/<USER>';
import {
  insertProductsAtSlots,
  parseFlashSlots,
  selectProductsForCurrentHour,
  trimJiomartUrl,
} from '../utils/HomeSectionUtils';
import {JMSharedViewModel} from '../../../jiomart-common/src/JMSharedViewModel';
import {AppSourceType} from '../../../jiomart-common/src/SourceType';

const homeController = new JMHomeNetworkController();

const staleTime = 0 * 60 * 1000; // 0 minutes
const gcTime = 30 * 60 * 1000; // 30 minutes

// Query Keys Factory
export const productQueryKeys = {
  all: ['products'] as const,
  productList: (currentSelectedSlug: string, selectedFlow: string) =>
    [
      ...productQueryKeys.all,
      'list',
      currentSelectedSlug,
      selectedFlow,
    ] as const,
  buyAgain: (
    currentSelectedSlug: string,
    selectedFlow: string,
    userId?: string,
  ) =>
    [
      ...productQueryKeys.productList(currentSelectedSlug, selectedFlow),
      'buyAgain',
      userId,
    ] as const,
  recommended: (currentSelectedSlug: string, selectedFlow: string) =>
    [
      ...productQueryKeys.productList(currentSelectedSlug, selectedFlow),
      'recommended',
    ] as const,
  flashDeals: (
    currentSelectedSlug: string,
    selectedFlow: string,
    selectionType: string,
  ) =>
    [
      ...productQueryKeys.productList(currentSelectedSlug, selectedFlow),
      'flashDeals',
      selectionType,
    ] as const,
  topDeals: (
    currentSelectedSlug: string,
    selectedFlow: string,
    selectionType: string,
  ) =>
    [
      ...productQueryKeys.productList(currentSelectedSlug, selectedFlow),
      'topDeals',
      selectionType,
    ] as const,
  cmsProducts: (
    currentSelectedSlug: string,
    selectedFlow: string,
    skuString: string,
  ) =>
    [
      ...productQueryKeys.productList(currentSelectedSlug, selectedFlow),
      'cmsProducts',
      skuString,
    ] as const,
};

// Utility Functions
const mapToProductsBAU = (buyAgainItems: any[]): MyListOffersProduct[] => {
  return buyAgainItems.map(item => {
    const sellingPrice = item.price.effective.min;
    const mrp = item.price.marked.min;

    let discountPct = 0;
    if (mrp > 0 && sellingPrice < mrp) {
      discountPct = ((mrp - sellingPrice) / mrp) * 100;
    }

    const imagePath = item.medias.length > 0 ? item.medias[0].url : '';

    return {
      product_code: item.item_code,
      display_name: item.name,
      selling_price: sellingPrice,
      mrp: mrp,
      discount_pct: parseFloat(discountPct.toFixed(2)),
      image_path: trimJiomartUrl(imagePath),
      url_path: item.url,
      vertical_code: item.attributes['vertical-code'],
      seller_id: item.seller_id.toString(),
      category_level_id: item.category_level_id,
    };
  });
};

const mapToUiModel = (data: any): MyListOffersProduct[] | null => {
  if (!data) return null;

  switch (JMSharedViewModel.Instance.appSource) {
    case AppSourceType.JM_JCP:
      return data;
    case AppSourceType.JM_BAU:
      return mapToProductsBAU(data);
    default:
      return data;
  }
};

// Individual Query Hooks
export const useBuyAgainQuery = (
  currentSelectedSlug: string,
  selectedFlow: string,
  enabled: boolean = true,
) => {
  return useQuery({
    queryKey: productQueryKeys.buyAgain(currentSelectedSlug, selectedFlow),
    queryFn: async (): Promise<MyListOffersProduct[]> => {
      const user = await getPrefString(AsyncStorageKeys.USER_DETAILS);
      const userDetails = !isNullOrUndefinedOrEmpty(user)
        ? JSON.parse(user ?? '')
        : {};

      const queryParams = {
        userid: userDetails?._id,
      };

      let buyAgainItemArray: any[] = [];
      buyAgainItemArray = await SearchService.getBuyAgainItems(queryParams);
      const uniqueBuyAgainItems = Array.from(new Set(buyAgainItemArray));

      if (buyAgainItemArray.length > 0) {
        const buyAgainInventory =
          await homeController.fetchProductDetailsFromAlgolia(
            uniqueBuyAgainItems,
          );
        const mappedProducts = mapToUiModel(buyAgainInventory?.items ?? []);
        return mappedProducts || [];
      }

      return [];
    },
    enabled: enabled && JMDatabaseManager.user.isUserLoggedInFlag(),
    staleTime: staleTime,
    gcTime: gcTime,
    retry: 0,
  });
};

export const useRecommendedQuery = (
  currentSelectedSlug: string,
  selectedFlow: string,
  enabled: boolean = true,
) => {
  return useQuery({
    queryKey: productQueryKeys.recommended(currentSelectedSlug, selectedFlow),
    queryFn: async (): Promise<MyListOffersProduct[]> => {
      try {
        const itemCodesArray = await SearchService.fetchRecommendedItems({});
        if (itemCodesArray && itemCodesArray.length > 0) {
          const recommendedItemsData =
            await homeController.fetchProductDetailsFromAlgolia(itemCodesArray);
          const mappedProducts = mapToUiModel(
            recommendedItemsData?.items ?? [],
          );
          return mappedProducts || [];
        }
        return [];
      } catch (error) {
        console.error('getRecommendedItems API Failed', error);
        return [];
      }
    },
    enabled,
    staleTime: staleTime,
    gcTime: gcTime,
    retry: 0,
  });
};

export const useFlashDealsQuery = (
  currentSelectedSlug: string,
  selectedFlow: string,
  enabled: boolean = true,
) => {
  return useQuery({
    queryKey: productQueryKeys.flashDeals(
      currentSelectedSlug,
      selectedFlow,
      'FLASH',
    ),
    queryFn: async (): Promise<MyListOffersProduct[]> => {
      try {
        const widgets = await getTopDealsWidgets(1, 50, 'FLASH');
        if (widgets && widgets.length !== 0) {
          const products: any[] = [];
          for (let i = 0; i < widgets.length; i++) {
            const widget = widgets[i];
            const product = await getWidgetProducts(
              widget.title_url ?? '',
              1,
              10,
            );
            products.push(...(product?.products || []));
          }
          return products;
        }
        return [];
      } catch (error) {
        console.error('getFlashDealsProducts API Failed', error);
        return [];
      }
    },
    enabled,
    staleTime: staleTime,
    gcTime: gcTime,
    retry: 0,
  });
};

export const useTopDealsQuery = (
  currentSelectedSlug: string,
  selectedFlow: string,
  enabled: boolean = true,
) => {
  return useQuery({
    queryKey: productQueryKeys.topDeals(
      currentSelectedSlug,
      selectedFlow,
      'TOPDEAL',
    ),
    queryFn: async (): Promise<MyListOffersProduct[]> => {
      try {
        const widgets = await getTopDealsWidgets(1, 50, 'TOPDEAL');
        if (widgets && widgets.length !== 0) {
          const products: any[] = [];
          for (let i = 0; i < widgets.length; i++) {
            const widget = widgets[i];
            const product = await getWidgetProducts(
              widget.title_url ?? '',
              1,
              10,
            );
            products.push(...(product?.products || []));
          }
          return products;
        }
        return [];
      } catch (error) {
        console.error('getTopDealsProducts API Failed', error);
        return [];
      }
    },
    enabled,
    staleTime: staleTime,
    gcTime: gcTime,
    retry: 0,
  });
};

export const useCMSProductsQuery = (
  currentSelectedSlug: string,
  selectedFlow: string,
  skuString: string,
  enabled: boolean = true,
) => {
  return useQuery({
    queryKey: productQueryKeys.cmsProducts(
      currentSelectedSlug,
      selectedFlow,
      skuString,
    ),
    queryFn: async (): Promise<MyListOffersProduct[]> => {
      try {
        if (!skuString) return [];

        const skuArray: string[] = skuString.split(',').map(sku => sku.trim());
        if (skuArray && skuArray.length > 0) {
          const cmsPoweredItemsData =
            await homeController.fetchProductDetailsFromAlgolia(skuArray);
          const mappedProducts = mapToUiModel(cmsPoweredItemsData?.items ?? []);
          return mappedProducts || [];
        }
        return [];
      } catch (error) {
        console.error('getCMSPoweredSKUItems API Failed', error);
        return [];
      }
    },
    enabled: enabled && Boolean(skuString),
    staleTime: staleTime,
    gcTime: gcTime,
    retry: 0,
  });
};

// Consolidated Product Queries Hook
export const useAllProductQueries = (
  currentSelectedSlug: string,
  selectedFlow: string,
  parsedWidgetData: MyListOffersWidgetRenderParsedDataMap,
  enabled: boolean = true,
  homeConfig: any,
) => {
  const isLoggedIn = JMDatabaseManager.user.isUserLoggedInFlag();
  const [consolidatedProducts, setConsolidatedProducts] = useState<
    MyListOffersProduct[]
  >([]);

  // Determine which queries should be enabled based on widget configuration
  const buyAgainEnabled =
    enabled && parsedWidgetData?.buyagain === true && isLoggedIn;
  const personalizedEnabled =
    enabled && parsedWidgetData?.personalised === true;
  const flashDealsEnabled = enabled && parsedWidgetData?.flashdeals === true;
  const topDealsEnabled = enabled && Boolean(parsedWidgetData?.topdeals);
  const cmsProductsEnabled = enabled && Boolean(parsedWidgetData?.products);

  // const buyAgainEnabled = false;
  // const personalizedEnabled = false;
  // const flashDealsEnabled = false;
  // const topDealsEnabled = true;
  // const cmsProductsEnabled = false;

  // Individual queries
  const buyAgainQuery = useBuyAgainQuery(
    currentSelectedSlug,
    selectedFlow,
    buyAgainEnabled,
  );
  const recommendedQuery = useRecommendedQuery(
    currentSelectedSlug,
    selectedFlow,
    personalizedEnabled,
  );
  const flashDealsQuery = useFlashDealsQuery(
    currentSelectedSlug,
    selectedFlow,
    flashDealsEnabled,
  );
  const topDealsQuery = useTopDealsQuery(
    currentSelectedSlug,
    selectedFlow,
    topDealsEnabled,
  );
  const cmsProductsQuery = useCMSProductsQuery(
    currentSelectedSlug,
    selectedFlow,
    parsedWidgetData?.products || '',
    cmsProductsEnabled,
  );

  // Consolidated state
  const isLoading =
    buyAgainQuery.isLoading ||
    recommendedQuery.isLoading ||
    flashDealsQuery.isLoading ||
    topDealsQuery.isLoading ||
    cmsProductsQuery.isLoading;

  const isFetching =
    buyAgainQuery.isFetching ||
    recommendedQuery.isFetching ||
    flashDealsQuery.isFetching ||
    topDealsQuery.isFetching ||
    cmsProductsQuery.isFetching;

  const hasError =
    buyAgainQuery.isError ||
    recommendedQuery.isError ||
    flashDealsQuery.isError ||
    topDealsQuery.isError ||
    cmsProductsQuery.isError;

  const getProcessedFlashDeals = useMemo(async () => {
    if (!parsedWidgetData?.flashdeals || !flashDealsQuery?.data?.length) {
      return [];
    }

    try {
      const flashSlots = parseFlashSlots(parsedWidgetData?.flash_slot || '');
      const slotsNeeded = flashSlots?.length ?? 0;

      if (slotsNeeded > 0) {
        const selectedFlashProducts = await selectProductsForCurrentHour(
          flashDealsQuery?.data,
          slotsNeeded,
          homeConfig?.homeConfig?.myListFlashProductsIntervalInMin,
        );

        // Ensure selectedFlashProducts is properly resolved
        const resolvedProducts = Array.isArray(selectedFlashProducts)
          ? selectedFlashProducts
          : [];
        console.log(
          'Flash Deals - Slots:',
          flashSlots,
          'Products:',
          resolvedProducts.length,
        );
        return resolvedProducts;
      }
    } catch (error) {
      console.error('Error processing flash deals:', error);
    }

    return [];
  }, [
    flashDealsQuery?.data,
    parsedWidgetData?.flashdeals,
    parsedWidgetData?.flash_slot,
    homeConfig,
  ]);

  // Memoized consolidated products array
  useEffect(() => {
    const processProducts = async () => {
      let allProducts: MyListOffersProduct[] = [];

      // Add products in the order they should appear
      if (buyAgainEnabled && buyAgainQuery.data) {
        allProducts.push(...buyAgainQuery.data);
      }

      if (personalizedEnabled && recommendedQuery.data) {
        allProducts.push(...recommendedQuery.data);
      }

      if (topDealsEnabled && topDealsQuery.data) {
        allProducts.push(...topDealsQuery.data);
      }

      if (cmsProductsEnabled && cmsProductsQuery.data) {
        allProducts.push(...cmsProductsQuery.data);
      }

      const processedFlashDeals = await getProcessedFlashDeals;
      if (flashDealsEnabled && processedFlashDeals.length > 0) {
        //allProducts.length = 0;
        allProducts = insertProductsAtSlots(
          allProducts,
          processedFlashDeals,
          parsedWidgetData?.slot,
        );
        console.log(
          'Flash deals added at slots:',
          parsedWidgetData?.slot,
          processedFlashDeals,
        );
      }

      setConsolidatedProducts(allProducts);
    };

    processProducts();
  }, [
    buyAgainQuery.data,
    recommendedQuery.data,
    topDealsQuery.data,
    cmsProductsQuery.data,
    flashDealsQuery?.data,
    buyAgainEnabled,
    personalizedEnabled,
    topDealsEnabled,
    cmsProductsEnabled,
  ]);

  return {
    // Individual query data
    buyAgainProducts: buyAgainQuery.data || [],
    recommendedProducts: recommendedQuery.data || [],
    flashDealsProducts: flashDealsQuery.data || [],
    topDealsProducts: topDealsQuery.data || [],
    cmsProducts: cmsProductsQuery.data || [],

    // Consolidated products
    consolidatedProducts,

    // Loading states
    isLoading,
    isFetching,
    hasError,

    // Individual query states for debugging
    queries: {
      buyAgain: buyAgainQuery,
      recommended: recommendedQuery,
      flashDeals: flashDealsQuery,
      topDeals: topDealsQuery,
      cmsProducts: cmsProductsQuery,
    },
  };
};
