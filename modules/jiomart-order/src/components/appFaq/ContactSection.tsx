import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Linking,
  Alert,
} from 'react-native';
import Divider, { DividerGap, DividerType } from '../../../../jiomart-general/src/ui/Divider';
import { JioIcon } from '@jio/rn_components';
import { IconColor, IconKind, IconSize } from '@jio/rn_components/src/index.types';
import { NavigationBean } from '../../../../jiomart-common/src/JMNavGraphUtil';

interface ContactOption {
  id: string;
  title: string;
  subtitle?: string;
  icon: string;
  cta? : NavigationBean;
  action: (cta : NavigationBean) => void;
}

interface ContactSectionProps {
  title: string;
  options: ContactOption[];
  onChatPress?: () => void;
  onRaiseRequestPress?: () => void;
}

const ContactSection: React.FC<ContactSectionProps> = ({
  title,
  options,
  onChatPress,
  onRaiseRequestPress
}) => {
  const handleCall = () => {
    const phoneUrl = `tel:${phoneNumber}`;
    Linking.canOpenURL(phoneUrl)
      .then((supported) => {
        if (supported) {
          Linking.openURL(phoneUrl);
        } else {
          Alert.alert('Error', 'Phone call not supported on this device');
        }
      })
      .catch((err) => console.error('Error opening phone:', err));
  };

  const handleWhatsApp = () => {
    const whatsappUrl = `whatsapp://send?phone=${whatsappNumber.replace(/\D/g, '')}`;
    Linking.canOpenURL(whatsappUrl)
      .then((supported) => {
        if (supported) {
          Linking.openURL(whatsappUrl);
        } else {
          Alert.alert('Error', 'WhatsApp not installed on this device');
        }
      })
      .catch((err) => console.error('Error opening WhatsApp:', err));
  };

  return (
    <View>
    <Divider
        type={DividerType.LARGE}
        bottom={DividerGap.GAP16}
    />
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>{title}</Text>
      <View style={styles.optionsContainer}>
        {options.map((option) => (
          <TouchableOpacity
            key={option.id}
            style={[
              styles.optionButton,
              { backgroundColor: IconColor.PRIMARY },
            ]}
            onPress={option.action}
            activeOpacity={0.7}
          >
            <View style={styles.optionContent}>
            <JioIcon
                ic={option.icon as any}
                color={IconColor.PRIMARY}
                kind={IconKind.DEFAULT}
                size={IconSize.MEDIUM}
              />
              <Text
                  style={[
                    styles.optionTitle,
                    { color: IconColor.PRIMARY },
                  ]}
                >
                  {option.title}
                </Text>
            </View>
          </TouchableOpacity>
        ))}
      </View>
    </View>
    </View>
  );
};

interface ServiceRequestSectionProps {
  title: string;
  description: string;
  buttonText: string;
  onViewAllPress: () => void;
}

const ServiceRequestSection: React.FC<ServiceRequestSectionProps> = ({
  title,
  description,
  buttonText,
  onViewAllPress,
}) => {
  return (
    <View>
    <Divider
        type={DividerType.LARGE}
        bottom={DividerGap.GAP16}
        top={DividerGap.GAP24}
    />
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>{title}</Text>
      <Text style={styles.description}>{description}</Text>
      <TouchableOpacity
        style={styles.viewAllButton}
        onPress={onViewAllPress}
        activeOpacity={0.7}
      >
        <Text style={styles.viewAllButtonText}>{buttonText}</Text>
      </TouchableOpacity>
    </View>
    </View>
  );
};

const styles = StyleSheet.create({
  section: {
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: '900',
    color: '#333333',
    marginBottom: 8,
  },
  description: {
    fontSize: 16,
    color: '#666666',
    fontWeight: '400',
    marginBottom: 16,
    lineHeight: 20,
  },
  optionsContainer: {
    gap: 12,
  },
  optionButton: {
    paddingTop: 5,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  optionContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  optionIcon: {
    fontSize: 24,
    marginRight: 16,
  },
  optionText: {
    flex: 1,
  },
  optionTitle: {
    fontSize: 20,
    paddingStart: 12,
    fontWeight: '400'
  },
  optionSubtitle: {
    fontSize: 12,
    color: '#666666',
    marginTop: 2,
  },
  viewAllButton: {
    borderWidth: 1,
    borderColor: '#80808050',
    borderRadius: 24,
    paddingVertical: 8,
    paddingHorizontal: 20,
    alignSelf: 'flex-start',
  },
  viewAllButtonText: {
    color: '#0078AD',
    fontSize: 18,
    fontWeight: '900',
  },
});

export { ContactSection, ServiceRequestSection };
export type { ContactOption }; 