import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  LayoutAnimation,
  Platform,
  UIManager,
} from 'react-native';
import { NavigationBean } from '../../../../jiomart-common/src/JMNavGraphUtil';

// Enable LayoutAnimation on Android
if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: string;
  order: number;
  cta : NavigationBean;
}

interface FAQAccordionProps {
  item: FAQItem;
  isExpanded?: boolean;
  onPress?: (id: string) => void;
  questionTextColor?: string;
  answerTextColor?: string;
  borderColor?: string;
}

const FAQAccordion: React.FC<FAQAccordionProps> = ({
  item,
  isExpanded = false,
  onPress,
  questionTextColor = '#333333',
  answerTextColor = '#666666',
  borderColor = '#E0E0E0',
}) => {
  const handlePress = () => {
    // Smooth animation for expand/collapse
    LayoutAnimation.configureNext({
      duration: 300,
      create: {
        type: LayoutAnimation.Types.easeInEaseOut,
        property: LayoutAnimation.Properties.opacity,
      },
      update: {
        type: LayoutAnimation.Types.easeInEaseOut,
      },
    });
    onPress?.(item.id);
  };

  return (
    <View style={[styles.container, { borderColor }]}>
      <TouchableOpacity
        style={styles.header}
        onPress={handlePress}
        activeOpacity={0.7}
      >
        <Text style={[styles.question, { color: questionTextColor }]}>
          {item.question}
        </Text>
        <Text style={[styles.icon, { color: questionTextColor }]}>
          {isExpanded ? '−' : '+'}
        </Text>
      </TouchableOpacity>
      
      {isExpanded && (
        <View style={styles.content}>
          <Text style={[styles.answer, { color: answerTextColor }]}>
            {item.answer}
          </Text>
        </View>
      )}
    </View>
  );
};

interface FAQSectionProps {
  title: string;
  items: FAQItem[];
  titleColor?: string;
  questionTextColor?: string;
  answerTextColor?: string;
  borderColor?: string;
}

const FAQSection: React.FC<FAQSectionProps> = ({
  title,
  items,
  titleColor = '#333333',
  questionTextColor = '#333333',
  answerTextColor = '#666666',
  borderColor = '#E0E0E0',
}) => {
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

  const toggleItem = (id: string) => {
    const newExpandedItems = new Set(expandedItems);
    if (newExpandedItems.has(id)) {
      newExpandedItems.delete(id);
    } else {
      newExpandedItems.add(id);
    }
    setExpandedItems(newExpandedItems);
  };

  return (
    <View style={styles.section}>
      <Text style={[styles.sectionTitle, { color: titleColor }]}>{title}</Text>
      {items.map((item) => (
        <FAQAccordion
          key={item.id}
          item={item}
          isExpanded={expandedItems.has(item.id)}
          onPress={toggleItem}
          questionTextColor={questionTextColor}
          answerTextColor={answerTextColor}
          borderColor={borderColor}
        />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  section: {
    marginBottom: 12,
    marginTop: 16,
  },
  sectionTitle: {
    fontSize: 21,
    fontWeight: '900',
    marginBottom: 3,
    paddingHorizontal: 16,
    color: '#000000',
  },
  container: {
    borderBottomWidth: 1,
    marginHorizontal: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    paddingRight: 8,
  },
  question: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
    lineHeight: 24,
  },
  icon: {
    fontSize: 20,
    fontWeight: 'bold',
    marginLeft: 16,
    width: 24,
    textAlign: 'center',
  },
  content: {
    paddingBottom: 16,
    paddingRight: 32,
  },
  answer: {
    fontSize: 14,
    lineHeight: 20,
  },
});

export { FAQAccordion, FAQSection };
export type { FAQItem }; 